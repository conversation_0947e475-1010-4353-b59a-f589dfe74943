package com.coldlar.hotwallet.model;

import java.math.BigInteger;

/**
 * Created by Administrator on 2017/5/8 0008.
 */

public class EthTransactionInfo {
    BigInteger nonce;
    BigInteger gasPrice;
    BigInteger gasLimit;
    String receiveAddress;
    BigInteger value;
    ContractInfo contractInfo;



    long chainId;

    private BigInteger maxPriorityFee;
    private BigInteger maxFee;

    private String transactionType;


    public ContractInfo getContractInfo() {
        return contractInfo;
    }

    public void setContractInfo(ContractInfo contractInfo) {
        this.contractInfo = contractInfo;
    }

    public BigInteger getValue() {
        return value;
    }

    public void setValue(BigInteger value) {
        this.value = value;
    }

    public BigInteger getNonce() {
        return nonce;
    }

    public void setNonce(BigInteger nonce) {
        this.nonce = nonce;
    }

    public BigInteger getGasPrice() {
        return gasPrice;
    }

    public void setGasPrice(BigInteger gasPrice) {
        this.gasPrice = gasPrice;
    }

    public BigInteger getGasLimit() {
        return gasLimit;
    }

    public void setGasLimit(BigInteger gasLimit) {
        this.gasLimit = gasLimit;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }


    public BigInteger getMaxPriorityFee() {
        return maxPriorityFee;
    }

    public void setMaxPriorityFee(BigInteger maxPriorityFee) {
        this.maxPriorityFee = maxPriorityFee;
    }

    public BigInteger getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(BigInteger maxFee) {
        this.maxFee = maxFee;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public long getChainId() {
        return chainId;
    }

    public void setChainId(long chainId) {
        this.chainId = chainId;
    }

}
