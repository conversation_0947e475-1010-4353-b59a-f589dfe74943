package com.coldlar.hotwallet.model;

/**
 * <AUTHOR>
 * @date 2019/8/20 16:30
 * description 描述
 */
public class EosPaymentItemBean {
    public static final int TYPE_NORMAL = 0;
    public static final int TYPE_INSUFFICIENT = 256;
    public static final int TYPE_AVAILABLE = 257;
    private int type;
    /**
     * 币种
     */
    private String coinSymbol;
    /**
     * 需要支付的金额
     */
    private String coinPrice;
    /**
     * 币种余额
     */
    private String coinBalance;
    /**
     * 币种ID
     */
    private String coinCode;
    /**
     * 币种充值状态 0不可充值,1可充值
     */
    private int rechargeStatus;
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCoinSymbol() {
        return coinSymbol;
    }

    public void setCoinSymbol(String coinSymbol) {
        this.coinSymbol = coinSymbol;
    }

    public String getCoinPrice() {
        return coinPrice;
    }

    public void setCoinPrice(String coinPrice) {
        this.coinPrice = coinPrice;
    }

    public String getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(String coinBalance) {
        this.coinBalance = coinBalance;
    }

    public String getCoinCode() {
        return coinCode;
    }

    public void setCoinCode(String coinCode) {
        this.coinCode = coinCode;
    }

    public int getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(int rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }
}
