package com.coldlar.hotwallet.utils;

import android.annotation.TargetApi;
import android.app.KeyguardManager;
import android.content.Context;
import android.os.Build;

import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.os.CancellationSignal;

import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.BaseApplication;
import com.coldlar.hotwallet.utils.view.AppLog;
import com.coldlar.hotwallet.utils.view.AppManager;


/**
 * author wangz<PERSON>
 * date 2018/10/23 10:41
 * FileName:  FingerprintManagerUtil
 * description 指纹控制工具类
 */
public class FingerprintManagerUtil {
    private KeyguardManager keyguardManager;

    private FingerprintManagerCompat fingerprintManager;
    private CancellationSignal cancellationSignal;
    private AuthenticationCallbackListener mCustomCallback;
    private static FingerprintManagerUtil mFingerprintLockUtil = null;
    private CryptoObjectHelper mCryptoObjectCreatorHelper;

    private FingerprintManagerUtil() {
        init();
    }

    public static FingerprintManagerUtil getInstance() {
        if ((mFingerprintLockUtil == null)) {

            synchronized (FingerprintManagerUtil.class) {
                if (mFingerprintLockUtil == null) {
                    mFingerprintLockUtil = new FingerprintManagerUtil();

                }
            }
        }
        return mFingerprintLockUtil;
    }


    private void init() {
        if (android.os.Build.VERSION.SDK_INT >= 23) {
            keyguardManager = (KeyguardManager) BaseApplication.getAppContext().getSystemService(Context.KEYGUARD_SERVICE);
            fingerprintManager = FingerprintManagerCompat.from(BaseApplication.getAppContext());
        }
    }

    /**
     * //判断设置是否支持指纹
     *
     * @return true 支持  false 不支持
     */
    public boolean isHardwareDetected() {
        if (fingerprintManager == null) {
            return false;
        }
        return android.os.Build.VERSION.SDK_INT >= 23 && fingerprintManager.isHardwareDetected();
    }


    /**
     * 是否设置密码锁屏
     *
     * @return true 已设置  false 没有设置
     */
    public boolean isKeyguardSecure() {
        if (fingerprintManager == null) {
            return false;
        }
        return keyguardManager.isKeyguardSecure();
    }

    /**
     * 是否录入指纹
     *
     * @return true 已录入  false 没有没有录入
     */
    @TargetApi(Build.VERSION_CODES.M)
    public boolean hasEnrolledFingerprints() {
        if (fingerprintManager == null) {
            return false;
        }
        return fingerprintManager.hasEnrolledFingerprints();
    }


    /**
     * 保存 指纹验证失败次数过多导致错误的时间
     */
    public void saveAuthenErrorTime() {
        SpfHelper.setParam(BaseApplication.getAppContext(), Constant.FINGERPRINT_VERIFY_EOORO_NUM_KEY, System.currentTimeMillis());

    }

    public long getAuthenErrorTime() {
        return (long) SpfHelper.getParam(BaseApplication.getAppContext(), Constant.FINGERPRINT_VERIFY_EOORO_NUM_KEY, (long) 0);
    }

    public long getwaitTime() {
        long currentTime = System.currentTimeMillis();
        long beforeTime = (long) SpfHelper.getParam(BaseApplication.getAppContext(), Constant.FINGERPRINT_VERIFY_EOORO_NUM_KEY, (long) 0);
        return Constant.FINGERPRINT_VERIFY_EOORO_AWAIT_TIMT - (currentTime - beforeTime);
    }

    private void reBeginAuthenticate() {
        try {
            mCryptoObjectCreatorHelper = new CryptoObjectHelper(new CryptoObjectHelper.ICryptoObjectCreateListener() {
                @Override
                public void onDataPrepared(FingerprintManagerCompat.CryptoObject cryptoObject) {
                    AppLog.e("初始化加密对象完成，开始扫描");
                    cancellationSignal = new CancellationSignal();
                    fingerprintManager.authenticate(cryptoObject, 0, cancellationSignal, new MyAuthCallback(), null);
//                    if (mListener != null) {
//                        mListener.onCryptoObjectCreateComplete();
//                    }
                }
            });
        } catch (Exception e) {
            throw new FingerPrintException(e);
        }
//
    }

    /**
     * 开始认证
     *
     * @param mCustomCallback
     * @throws FingerPrintException
     */
    @TargetApi(Build.VERSION_CODES.M)
    public void beginAuthenticate(final AuthenticationCallbackListener mCustomCallback) throws FingerPrintException {
        this.mCustomCallback = mCustomCallback;
        AppLog.e("开始指纹认证");
        try {
            mCryptoObjectCreatorHelper = new CryptoObjectHelper(new CryptoObjectHelper.ICryptoObjectCreateListener() {
                @Override
                public void onDataPrepared(FingerprintManagerCompat.CryptoObject cryptoObject) {
                    AppLog.e("初始化加密对象完成，开始扫描");
                    cancellationSignal = new CancellationSignal();
                    fingerprintManager.authenticate(cryptoObject, 0, cancellationSignal, new MyAuthCallback(), null);
                    if (mCustomCallback != null) {
                        mCustomCallback.onCryptoObjectCreateComplete();
                    }
                }
            });
        } catch (Exception e) {
            throw new FingerPrintException(e);
        }
    }

    /**
     * 是否支持指纹
     *
     * @return
     */
    public boolean isOpenFingerprint() {
        if (!isHardwareDetected()) {  //不支持指纹

            ToastUtils.showLongSafe(AppManager.getAppManager().currentActivity().getResources().getString(R.string.text_no_support_fingerprint));
            return false;
        }
        if (!isKeyguardSecure()) {  //没有设置锁屏密码
            ToastUtils.showLongSafe(AppManager.getAppManager().currentActivity().getResources().getString(R.string.text_no_set_screen_pass));
//            Intent intent = new Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS);
//            AppManager.getAppManager().currentActivity().startActivity(intent);
            IntentSystemUtils.startSettingsActivity();
            return false;
        }

        if (!hasEnrolledFingerprints()) {  //没有录入指纹
            ToastUtils.showLongSafe(AppManager.getAppManager().currentActivity().getResources().getString(R.string.text_no_set_fingerprint));
            IntentSystemUtils.startSettingsActivity();
            return false;
        }

        return true;

    }

    /**
     * 取消认证
     */
    public void stopsFingerprintListen() {
        //如果不取消的话，那么指纹扫描器会一直扫描直到超时（一般为30s，取决于具体的厂商实现），这样的话就会比较耗电。
        if (cancellationSignal != null) {
            cancellationSignal.cancel();
            cancellationSignal = null;
        }
        if (mCryptoObjectCreatorHelper != null) {
            mCryptoObjectCreatorHelper.onDestroy();
        }
    }

    private int happenCount = 0;

    public class MyAuthCallback extends FingerprintManagerCompat.AuthenticationCallback {
        public static final int ERROR_CANCEL = 5;
        public static final int ERROR_BEYOND = 7;

        // 这个接口会再系统指纹认证出现不可恢复的错误的时候才会调用，并且参数errorCode就给出了错误码，
//                    // 标识了错误的原因。这个时候app能做的只能是提示用户重新尝试一遍。
        //会有一段时间的禁用期 有的是1分钟，有的是30秒等等。而且，由于手机厂商的系统区别
        @Override
        public void onAuthenticationError(int errMsgId, CharSequence errString) {
            super.onAuthenticationError(errMsgId, errString);
            if (mCustomCallback != null) {
                mCustomCallback.onAuthenticationError(errMsgId, errString.toString());
            }
            if (errMsgId == 7 && "0".equals(errString)) {  //不能短时间内调用指纹验证
                AppLog.e("onAuthenticationError>>>>>>>失误次数过多");
                FingerprintManagerUtil.getInstance().saveAuthenErrorTime();
            }
            mCryptoObjectCreatorHelper.onDestroy();
            AppLog.e("onAuthenticationError>>>>>>>指纹不可恢复错误");
//            switch (errMsgId) {
//                case 5://取消
//
//                    break;
//                default:
//
//                    break;
//            }

        }

        //上面的认证失败是认证过程中的一个异常情况，我们说那种情况是因为出现了不可恢复的错误，
//                    // 而我们这里的OnAuthenticationHelp方法是出现了可以回复的异常才会调用的。
//                    // 什么是可以恢复的异常呢？一个常见的例子就是：手指移动太快，当我们把手指放到传感器上的时候，
//                    // 如果我们很快地将手指移走的话，那么指纹传感器可能只采集了部分的信息，因此认证会失败。
//                    // 但是这个错误是可以恢复的，因此只要提示用户再次按下指纹，并且不要太快移走就可以解决。
        @Override
        public void onAuthenticationHelp(int helpMsgId, CharSequence helpString) {
            super.onAuthenticationHelp(helpMsgId, helpString);
            if (mCustomCallback != null) {

                mCustomCallback.onAuthenticationHelp(helpString.toString());
            }
            switch (helpMsgId) {
                case 5: //手指一动太快
                    break;
                default:

                    break;
            }
            AppLog.e("onAuthenticationHelp>>>>指纹错误请重试");
        }

        //这个接口会在认证成功之后回调。我们可以在这个方法中提示用户认证成功。这里需要说明一下，
//                    // 如果我们上面在调用authenticate的时候，我们的CryptoObject不是null的话，
//                    // 那么我们在这个方法中可以通过AuthenticationResult来获得Cypher对象然后调用它的doFinal方法。
//                    // doFinal方法会检查结果是不是会拦截或者篡改过，如果是的话会抛出一个异常。
//                    // 当我们发现这些异常的时候都应该将认证当做是失败来来处理，为了安全建议大家都这么做。
        @Override
        public void onAuthenticationSucceeded(FingerprintManagerCompat.AuthenticationResult result) {
            super.onAuthenticationSucceeded(result);


            if (mCustomCallback != null) {

                mCustomCallback.onAuthenticationSucceeded(true);
            }


//            /**
//             * doFinal方法会检查结果是不是会拦截或者篡改过，
//             * 如果是的话会抛出一个异常，异常的时候都将认证当做是失败来处理
//             */
//            try {
//                result.getCryptoObject().getCipher().doFinal();
//                if (mCustomCallback!=null) {
//
//                    mCustomCallback.onAuthenticationSucceeded(true);
//                }
//            } catch (IllegalBlockSizeException e) {
//                //如果是新录入的指纹，会抛出该异常，需要重新生成密钥对重新验证，这里加个次数限制，避免进入验证异常->重新验证->又验证异常的死循环
//                if (happenCount == 0) {
//                    reBeginAuthenticate();
//                    happenCount++;
//                    return;
//                }
//
//                if (mCustomCallback!=null) {
//                    mCustomCallback.onAuthenticationSucceeded(false);
//                }
//            } catch (Exception e) {
//                if (mCustomCallback!=null) {
//                    mCustomCallback.onAuthenticationSucceeded(false);
//                }
//            }
            mCryptoObjectCreatorHelper.onDestroy();


            AppLog.e("onAuthenticationSucceeded>>>>成功");
        }

        //这个接口会在系统指纹认证失败的情况的下才会回调。注意这里的认证失败和上面的认证错误是不一样的，
//                    // 虽然结果都是不能认证。认证失败是指所有的信息都采集完整，并且没有任何异常，
//                    // 但是这个指纹和之前注册的指纹是不相符的；
//                    // 但是认证错误是指在采集或者认证的过程中出现了错误，
//                    // 比如指纹传感器工作异常等。也就是说认证失败是一个可以预期的正常情况，而认证错误是不可预期的异常情况。
        //续调用了5次onAuthenticationFailed()方法后，会调用onAuthenticationError()方法
        @Override
        public void onAuthenticationFailed() {
            super.onAuthenticationFailed();
            if (mCustomCallback != null) {

                mCustomCallback.onAuthenticationFailed();
            }
            AppLog.e("onAuthenticationFailed>>>>失败");
        }

    }


    public interface AuthenticationCallbackListener {
        void onAuthenticationHelp(String helpString);

        void onAuthenticationFailed();

        void onAuthenticationError(int errMsgId, String errString);

        void onAuthenticationSucceeded(boolean isAuthSuccess);

        void onCryptoObjectCreateComplete();
    }

    class FingerPrintException extends RuntimeException {
        public FingerPrintException(String message) {
            super(message);
        }

        public FingerPrintException(Throwable e) {
            super(e);
        }
    }

}
