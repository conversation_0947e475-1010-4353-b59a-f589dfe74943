package com.coldlar.hotwallet.dialog;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bitfork.core.coins.EosMain;
import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.dialog.BaseDialog;
import com.coldlar.hotwallet.eos.EosUtil;
import com.coldlar.hotwallet.model.EosAccount;
import com.coldlar.hotwallet.network.request.EosRequest;
import com.coldlar.hotwallet.utils.StringUtil;
import com.coldlar.hotwallet.utils.ToastUtils;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * <AUTHOR>
 * @date 2018/7/16 20:11.
 * @description
 */
public class RegistEosDailog extends BaseDialog {
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.ed_account)
    EditText edAccount;
    @BindView(R.id.btn_no)
    Button btnNo;
    @BindView(R.id.btn_ok)
    Button btnOk;
    @BindView(R.id.account_exist)
    TextView accountExist;

    private onOkClickListener mListener;

    public RegistEosDailog(@NonNull Context context) {
        super(context);
    }

    public RegistEosDailog(@NonNull Context context, int style) {
        super(context, style);
    }

    public void setmListener(onOkClickListener mListener) {
        this.mListener = mListener;
    }


    public void showNoAccount() {
        accountExist.setVisibility(View.VISIBLE);
    }

    public void hideNoAccount() {
        accountExist.setVisibility(View.GONE);
    }

    @Override
    public int getLayoutID() {
        return R.layout.dialog_register_eos;
    }

    @Override
    public void initView(View view) {
        ButterKnife.bind(this, view);
        btnNo.setOnClickListener(this);
        btnOk.setOnClickListener(this);

        TextWatcher textWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                accountExist.setVisibility(View.GONE);
            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString();
                if (StringUtil.isEmpty(str)) {
                    accountExist.setVisibility(View.GONE);
                }
            }
        };
        edAccount.addTextChangedListener(textWatcher);

    }

    @Override
    public void handleClick(View v) {
        switch (v.getId()) {
            case R.id.btn_no:
                edAccount.setText("");
                dismiss();
                break;
            case R.id.btn_ok:
                if (!TextUtils.isEmpty(edAccount.getText().toString().trim()) && edAccount.getText().toString().trim().length() == 12) {
                    if (mListener != null) {
                        mListener.onOkClick(edAccount.getText().toString().trim());
                    }
                    //                    checkEosAccout(edAccount.getText().toString().trim());
                } else {
                    ToastUtils.showShort(mContext.getResources().getString(R.string.text_input_account_msg));
                }
                break;
            default:
                break;
        }
    }


    /**
     * 检查Eos账户有没有注册过
     *
     * @param account
     */
    private void checkEosAccout(final String account) {
        if (EosUtil.matchAccount(account)) {
            EosRequest.getAccountDetail(EosMain.get(),account, new EosRequest.OnEosDetailCallback() {
                @Override
                public void onSuccess(EosAccount response) {
                    try {
                        if (response != null) {
                            showNoAccount();
                        }else {
                            if (mListener != null) {
                                mListener.onOkClick(account);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(Exception e) {
                    ToastUtils.showShort(mContext.getResources().getString(R.string.text_eos_server_error));
                }
            });
        }else {
            ToastUtils.showShort(mContext.getResources().getString(R.string.text_input_account_msg));
        }
    }






    public interface onOkClickListener {
        void onOkClick(String account);

    }
}
