package com.coldlar.hotwallet.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.dialog.BaseDialog;

/**
 * <AUTHOR>
 * @date 2019年1月9日 18:31:42
 * @description 提醒
 */
public class MyAlertDailog extends BaseDialog {

    private TextView mTvTitle;
    private TextView mTvContent;
    private Button mBtnOk;

    private Context mContext;
    private String mTitle;
    private String mContent;
    private String mBtnStr;
    private onOkClickListener mListener;

    public MyAlertDailog(@NonNull Context context,String title,String content,String btnStr, onOkClickListener listener) {
        super(context);
        this.mContext = context;
        this.mTitle = title;
        this.mContent = content;
        this.mBtnStr = btnStr;
        this.mListener = listener;
    }

    public void setmListener(onOkClickListener mListener) {
        this.mListener = mListener;
    }


    @Override
    public int getLayoutID() {
        return R.layout.dialog_alert;
    }

    @Override
    public void initView(View view) {
        mTvTitle = (TextView) view.findViewById(R.id.tv_title);
        mTvContent = (TextView) view.findViewById(R.id.tvContent);
        mBtnOk = (Button) view.findViewById(R.id.btn_ok);
        mBtnOk.setOnClickListener(this);
        setIsTouchOutsideDismiss(false);
    }

    public void showAlert(){
        if (!TextUtils.isEmpty(mTitle)) {
            mTvTitle.setText(mTitle);
        }
        if (!TextUtils.isEmpty(mContent)) {
            mTvContent.setText(mContent);
        }
        if (!TextUtils.isEmpty(mBtnStr)) {
            mBtnOk.setText(mBtnStr);
        }
        show();
    }

    @Override
    public void handleClick(View v) {
        switch (v.getId()) {
            case R.id.btn_ok:
                dismiss();
                break;
            case R.id.btn_cancel:
                dismiss();
                break;
            default:
                break;
        }
    }

    public interface onOkClickListener {
        void onOkClick();

    }
}
