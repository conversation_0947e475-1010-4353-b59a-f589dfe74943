package com.coldlar.hotwallet.dialog;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.dialog.BaseDialog;
import com.coldlar.hotwallet.utils.coin.NumberUtil;

/**
 * <AUTHOR>
 * @date 2019年12月26日
 * description 质押部分还款提示框
 */
public class PledgeReturnDialog extends BaseDialog {
    private TextView tvTheBorrowDes;
    private TextView tvTheBorrow;
    private TextView tvInterest;
    private RelativeLayout rlInterest;
    private TextView tvConfirm;
    onOkClickListener mListener;
    public PledgeReturnDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    public int getLayoutID() {
        return R.layout.dialog_pledge_turn;
    }

    @Override
    public void initView(View view) {

        Window dialogWindow = dialog.getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        if (location == BOTTOM) {
            dialogWindow.setGravity(Gravity.BOTTOM);
        }
        dialogWindow.setWindowAnimations(getAnimations() == 0 ? R.style.promptdialog_anim : getAnimations()); //设置窗口弹出动画
        DisplayMetrics d = mContext.getResources().getDisplayMetrics(); // 获取屏幕宽、高用
        lp.width = (int) (d.widthPixels * 0.7); // 高度设置为屏幕的0.6
        dialogWindow.setAttributes(lp);


        tvTheBorrowDes = view.findViewById(R.id.tv_the_borrow_des);
        tvTheBorrow = view.findViewById(R.id.tv_the_borrow);
        rlInterest = view.findViewById(R.id.rl_interest);
        tvInterest = view.findViewById(R.id.tv_interest);
        tvConfirm = view.findViewById(R.id.tv_confirm);
        tvConfirm.setOnClickListener(this);
        setBoldText(tvTheBorrow);
        setBoldText(tvInterest);
    }

    @Override
    public void handleClick(View v) {
        switch (v.getId()) {
            case R.id.tv_confirm:
//                onClick();
                dismiss();
                mListener.onOkClick();
                break;
            default:

                break;
        }

    }


    /**
     * 还款
     * @param symbol
     * @param returnValue 还款金额
     * @param waitReturn  待还金额
     * @return
     */
    public PledgeReturnDialog showRefund(String symbol, String returnValue, String waitReturn,  onOkClickListener listener) {
        tvTheBorrow.setText(NumberUtil.setFormate(returnValue) + " "+symbol);
        tvInterest.setText(NumberUtil.setFormate(waitReturn)+" "+symbol);
        mListener=listener;
        show();
        return this;
    }

    public void onClick(){

    }
    public interface onOkClickListener {
        void onOkClick();

    }
}
