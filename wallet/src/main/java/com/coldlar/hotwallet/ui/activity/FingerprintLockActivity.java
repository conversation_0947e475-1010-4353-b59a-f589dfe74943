package com.coldlar.hotwallet.ui.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.activity.BaseTileActivity;
import com.coldlar.hotwallet.dialog.FingerprintErrorDialog;
import com.coldlar.hotwallet.utils.FingerprintManagerUtil;
import com.coldlar.hotwallet.utils.UmengUtils;
import com.coldlar.hotwallet.utils.UserManager;
import com.coldlar.hotwallet.utils.view.AccountLockManagerUtil;
import com.coldlar.hotwallet.utils.view.AppLog;
import com.coldlar.hotwallet.utils.view.AppManager;
import com.umeng.analytics.MobclickAgent;

import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * author wangz<PERSON>
 * date 2018/5/10 16:10
 * FileName:  FingerprintLockActivity
 * description 指纹解锁页面
 */
public class FingerprintLockActivity extends BaseTileActivity {
    private static final int SNQUIRY_SENSOR = 524;
    public static final int KEY_MONITOR_ACCOUNT = 0x001; //监控账户
    //    public static final int KEY_ACCOUNT_SWITCH_ON = 0x002;  //账户锁开关
//    public static final int KEY_ACCOUNT_SWITCH_OFF = 0x003;  //账户锁开关
    public static final String KEY_FINGERPRINT_STATE = "FINGERPRINT_STATE";
    public static final String KEY_FINGERPRINT_STATE_TAG = "FINGERPRINT_STATE_TAG";
    @BindView(R.id.tv_hint)
    TextView tvHint;
    @BindView(R.id.tv_hint1)
    TextView tvHint1;

    @BindView(R.id.tv_fingerprint)
    TextView tvFingerprint;
    @BindView(R.id.tv_pass_unlock)
    TextView tvPassUnlock;
    private int tryNum = 5;
    /**
     * 是否去检查指纹传感器是否开启
     */
    private boolean isSnquirySensor = false;

    private long snquiryIntervalTime = 10;

    private Disposable disposable;

    private FingerprintErrorDialog errorDialog;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_fingerprint_lock;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        tvPassUnlock.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        MobclickAgent.onEvent(mContext, UmengUtils.ACTIVITY_LOCKING);
        setTopLineVisibility(View.GONE);
        setLeftViewVisibility(View.INVISIBLE);
        tvHint.setText("ColdLar");
        if (UserManager.getInstance().isLogin()) {
            if (!TextUtils.isEmpty(UserManager.getInstance().getHiAccount(mContext))) {
                tvHint.setVisibility(View.VISIBLE);
                tvHint.setText("Hi,"+UserManager.getInstance().getHiAccount(mContext));

            }
        }

    }


    @Override
    protected void onResume() {
        super.onResume();
        if (!FingerprintManagerUtil.getInstance().hasEnrolledFingerprints()) {
            finish();
            return;
        }
        beginAuthenticate();
    }

    @Override
    protected void onPause() {
        super.onPause();
        FingerprintManagerUtil.getInstance().stopsFingerprintListen();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (errorDialog != null && errorDialog.isShowing()) {
            errorDialog.dismiss();
            errorDialog.cancel();
        }
    }

    private void authFailed() {
        tryNum++;
        setHint(R.string.text_validation_failure);
    }

    @Override
    protected void handleClick(View v) {
        super.handleClick(v);
        switch (v.getId()) {

            case R.id.tv_pass_unlock:
                gotoActivity(PassUnlockActivity.class);
                break;
            default:

                break;
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return false;
    }

    @Override
    protected void onLeftClick(View v) {
        AppManager.getAppManager().finishAllActivity();
        gotoActivity(MainActivity.class);
    }


    private void beginAuthenticate() {


        if (FingerprintManagerUtil.getInstance().isOpenFingerprint()) {
            FingerprintManagerUtil.getInstance().beginAuthenticate(new FingerprintManagerUtil.AuthenticationCallbackListener() {
                @Override
                public void onAuthenticationHelp(String helpString) {
//                        ToastUtils.showShortSafe(helpString);
                }

                @Override
                public void onAuthenticationFailed() {
                    authFailed();
                }

                @Override
                public void onAuthenticationError(int errMsgId, String errString) {
                    if (errMsgId == 7 || errMsgId == 9) {//不能短时间内调用指纹验证
//                        Intent intent = new Intent(mContext, PassUnlockActivity.class);
//                        intent.putExtra(PassUnlockActivity.KEY_TYPE, PassUnlockActivity.TYPE_RESET_FINGERPRINT_LOCK);
//                        gotoActivity(intent);
                        isSnquirySensor = true;
                        snquiryInterval();
                        setHint(R.string.text_fingerprint_error_nimiety);
                        AppLog.e("onAuthenticationError>>>>>>>失误次数过多");
                    }

//
                }

                @Override
                public void onAuthenticationSucceeded(boolean isAuthSuccess) {
                    if (isAuthSuccess) {
                        //这里更新APP进入后台的时间,是防止 一段时间内每次都要解锁
                        AccountLockManagerUtil.getInstance().saveAppBackgroundTime();
                        finish();
                    } else {
                        authFailed();
                    }
                }

                @Override
                public void onCryptoObjectCreateComplete() {
//                        ToastUtils.showShortSafe("初始化成功，");
                    isSnquirySensor = false;
                    setHint(R.string.text_unlock_enter);
                    snquiryInterval();
                }
            });
        }

    }

    private void snquiryInterval() {
//        while (isSnquirySensor) {
//            tvPassUnlock.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    beginAuthenticate();
//                }
//            }, snquiryIntervalTime);
//        }
        if (isSnquirySensor) {
            disposable = Observable
//                每隔一秒递增,一次递增1直至无穷大
                    .interval(snquiryIntervalTime, TimeUnit.SECONDS)
                    .takeWhile(aLong -> !isDestroyed())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            beginAuthenticate();
                            AppLog.e("轮询");
                        }
                    });
        } else {
            if (disposable != null) {
                disposable.dispose();
            }
        }

    }

    private void setHint(int hint) {
        tvHint1.setText(getStr(hint));
    }

}
