package com.coldlar.hotwallet.ui.smart.bean;

/**
 * <AUTHOR>
 * @Date：2019/11/28 on 11:09
 * desc:
 */
public class WalletStatus {
    /**
     * success : 1
     * payload : {"request":2}
     */

    private int success;
    private PayloadBean payload;

    public int getSuccess() {
        return success;
    }

    public void setSuccess(int success) {
        this.success = success;
    }

    public PayloadBean getPayload() {
        return payload;
    }

    public void setPayload(PayloadBean payload) {
        this.payload = payload;
    }

    public static class PayloadBean {
        /**
         * request :
         * 1：正在确认交易信息
         * 2：正在输入PIN码
         * 3：PIN码正确，等待in数据，专用于伪验签
         */

        private int request;//todo 下版 改为status

        public int getRequest() {
            return request;
        }

        public void setRequest(int request) {
            this.request = request;
        }
    }
}
