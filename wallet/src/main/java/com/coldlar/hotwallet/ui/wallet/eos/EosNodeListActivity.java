package com.coldlar.hotwallet.ui.wallet.eos;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bitfork.core.coins.EosMain;
import com.chad.library.adapter.old.BaseQuickAdapter;
import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.CoinBase;
import com.coldlar.hotwallet.base.activity.BaseTileActivity;
import com.coldlar.hotwallet.dialog.EosNodeSelectDialog;
import com.coldlar.hotwallet.eos.Action;
import com.coldlar.hotwallet.eos.EosUtil;
import com.coldlar.hotwallet.eos.HexUtils;
import com.coldlar.hotwallet.eos.SignedTransaction;
import com.coldlar.hotwallet.eos.TypeChainId;
import com.coldlar.hotwallet.eos.bean.EosChainInfo;
import com.coldlar.hotwallet.greendao.AddressModel;
import com.coldlar.hotwallet.greendao.CoinMessageModel;
import com.coldlar.hotwallet.greendao.ColdlarMsgModel;
import com.coldlar.hotwallet.greendao.base.DaoHelper;
import com.coldlar.hotwallet.model.EosAccount;
import com.coldlar.hotwallet.model.EventMsg;
import com.coldlar.hotwallet.model.eos.NodeBean;
import com.coldlar.hotwallet.network.OkHttpUtils;
import com.coldlar.hotwallet.network.UiResultCallback;
import com.coldlar.hotwallet.network.newapi.NEWAPI;
import com.coldlar.hotwallet.network.newapi.RequestCallback;
import com.coldlar.hotwallet.network.request.CoinRequestUtil;
import com.coldlar.hotwallet.network.request.EosRequest;
import com.coldlar.hotwallet.ui.adapter.EosNodeListAdapter;
import com.coldlar.hotwallet.ui.prose.coldrequest.EosSendRequest;
import com.coldlar.hotwallet.ui.wallet.BroadcastEosActivity;
import com.coldlar.hotwallet.ui.wallet.CommonQrActivity;
import com.coldlar.hotwallet.utils.DialogHelper;
import com.coldlar.hotwallet.utils.EventBusUtil;
import com.coldlar.hotwallet.utils.TimeUtil;
import com.coldlar.hotwallet.utils.UmengUtils;
import com.coldlar.hotwallet.utils.coin.NumberUtil;
import com.coldlar.hotwallet.utils.view.AppLog;
import com.umeng.analytics.MobclickAgent;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * author wang zhao
 * date 2018/9/26 15:13
 * description Eos节点列表
 */
public class EosNodeListActivity extends BaseTileActivity {
    public static final String KEY_EOS_ACCOUNT = "EOS_ACCOUNT ";
    public static final String KEY_ADDRESS_MODE = "ADDRESS_MODE";
    public static final String KEY_NEW_NODE_LIST = "new_node_list";
    private EditText etSearch;
    private TextView tvCancel;
    private TextView tvSelNum;
    private TextView tvVote;
    private LinearLayout llSelNum;
    private TextView tvSearch;
    private RecyclerView rvNodeList;
    private RelativeLayout llEmptyData;
    private EosNodeListAdapter mEosNodeListAdapter;
    private String mEosAccount_name;
    private EosAccount mEosAccount;
    private EosAccount.EosVoter mEosVoter;
    private List<String> mAlreadyVoteNodeList = new ArrayList<>();
    private List<NodeBean> mSelectList = new ArrayList<>();
    private List<NodeBean> mNodeList = new ArrayList<>();
    private ArrayList<String> nodeNmaeList = new ArrayList<>();
    private AddressModel mAddressModel;
    private ColdlarMsgModel mColdlarMsgModel;
    private int mBatch;
    /**
     * 初始是否有投票，P4有添加抵押的逻辑，需要用到这个字段
     */
    private boolean isHaveVote = false;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_eos_node_list;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        // 进入界面后不让软键盘弹出
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        //进制键盘顶起布局
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        setTtle(getResources().getString(R.string.text_eos_vote_title));
        setRightImage(R.mipmap.icon_vote_history);
        etSearch = (EditText) findViewById(R.id.et_search);
        tvCancel = (TextView) findViewById(R.id.tv_cancel);
        tvSelNum = (TextView) findViewById(R.id.tv_sel_num);
        tvVote = (TextView) findViewById(R.id.tv_vote);
        tvSearch = (TextView) findViewById(R.id.tv_search);
        llSelNum = (LinearLayout) findViewById(R.id.ll_sel_num);
        rvNodeList = (RecyclerView) findViewById(R.id.rv_node_list);
        rvNodeList.setLayoutManager(new LinearLayoutManager(mContext));
        llEmptyData = (RelativeLayout) findViewById(R.id.ll_empty_data);
        llSelNum.setOnClickListener(this);
        tvSearch.setOnClickListener(this);
        tvCancel.setOnClickListener(this);
        tvVote.setOnClickListener(this);
        rvNodeList.setAdapter(mEosNodeListAdapter = new EosNodeListAdapter());
        mEosNodeListAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                NodeBean bean = (NodeBean) adapter.getData().get(position);
                if (view.getId() == R.id.iv_add) {
                    if (bean.getIsSelect() == NodeBean.NO_SELECT) {
                        if (mSelectList.size() >= Constant.EOS_NODE_SELECT_SIZE) {
                            showToast(getResources().getString(R.string.text_eos_node_num_beyond_hint));
                            return;
                        }
                        bean.setIsSelect(NodeBean.SELECT);
                        mSelectList.add(bean);
                        showToast(getResources().getString(R.string.text_eos_add_succeed));
                    } else if (bean.getIsSelect() == NodeBean.SELECT) {
                        mSelectList.remove(bean);
                        bean.setIsSelect(NodeBean.NO_SELECT);
                        showToast(getResources().getString(R.string.text_eos_sub_succeed));
                    }
                    adapter.notifyDataSetChanged();
                    handleSelectNode();
                }

            }
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().length() == 0) {
                    mEosNodeListAdapter.setNewData(mNodeList);
                    handleEmptyData(false);
                    tvCancel.setVisibility(View.GONE);
                } else {
                    tvCancel.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @Override
    protected void initData() {
        MobclickAgent.onEvent(mContext, UmengUtils.EOS_VOTE_ACTIVITY);
        mEosAccount = (EosAccount) getIntent().getSerializableExtra(KEY_EOS_ACCOUNT);
        mAddressModel = (AddressModel) getIntent().getSerializableExtra(KEY_ADDRESS_MODE);
        mEosAccount_name = mEosAccount.getAccount_name();
        mEosVoter = mEosAccount.getVoter();
        mAlreadyVoteNodeList.clear();
        mAlreadyVoteNodeList.addAll(mEosVoter.getProducers());
        if (mAddressModel != null) {
            mColdlarMsgModel = DaoHelper.getColdlarMsgManager().getAccount(mAddressModel.getWalletID());
        }
        if (mColdlarMsgModel != null) {
            mBatch = mColdlarMsgModel.getBatch();
        } else {
            mBatch = 4;
        }
        if(mAlreadyVoteNodeList.size()>0){
            isHaveVote=true;
        }
        getNodeList();
    }


    private void handleSelectNode() {
        tvSelNum.setText(mSelectList.size() + "/" + Constant.EOS_NODE_SELECT_SIZE);
    }

    @Override
    protected void onRightClick(View v) {
        super.onRightClick(v);
        startActivity(new Intent(mContext, EosVoteHistoryActivity.class)
                .putExtra(KEY_EOS_ACCOUNT, mEosAccount));
    }

    @Override
    protected void handleClick(View v) {
        super.handleClick(v);
        switch (v.getId()) {
            case R.id.ll_sel_num:
                if (mSelectList.size() > 0) {
                    EosNodeSelectDialog selectDialog = new EosNodeSelectDialog(this, mSelectList)
                            .setDatas(mSelectList)
                            .setSubListener(mSubListener);
                    selectDialog.show();
                }
                break;
            case R.id.tv_cancel:
                etSearch.setText("");
                break;
            case R.id.tv_search:
                if (!TextUtils.isEmpty(etSearch.getText().toString().trim())) {
                    searchNode(etSearch.getText().toString().trim());
                }
                break;
            case R.id.tv_vote:
                MobclickAgent.onEvent(mContext, UmengUtils.EOS_VOTE_SUBMIT_BTN);
                if (mSelectList.size() <= 0) {
                    showToast(getResources().getString(R.string.text_eos_node_nosel_hint));
                    return;
                }
                if (CoinBase.isProSeByBatchid(mColdlarMsgModel.getBatch()) || CoinBase.isUltraSeries(mColdlarMsgModel.getBatch())) {
                    if (isHaveVote) {//已有投票提示框
                        DialogHelper.nomalRemind(mContext, getStr(R.string.text_eos_vote_remind), getStr(R.string.text_sure), new DialogHelper.onNomalClickListener() {
                            @Override
                            public void onSure() {
                                intentToVote();
                            }

                            @Override
                            public void onCancel() {

                            }
                        });
                    } else {
                        intentToVote();
                    }
                } else {
                    createVoteAction(mEosAccount_name, EosRequest.ACTION_TYPE_VOTE, mSelectList);
                }
                break;
            default:
                break;
        }
    }


    private void intentToVote(){
        Intent intent = new Intent(mContext, EosVoteMortgageActivity.class);
        intent.putExtra(EosNodeListActivity.KEY_EOS_ACCOUNT, mEosAccount);
        intent.putExtra(EosNodeListActivity.KEY_ADDRESS_MODE, mAddressModel);
        intent.putExtra(KEY_NEW_NODE_LIST, (Serializable) mSelectList);
        startActivity(intent);
    }


    /**
     * 获取EOS节点列表
     */
    private void getNodeList() {
        showLoadingDialog();
        EosRequest.getEosCandidateNodeList("200", new OkHttpUtils.ResultCallback<String>() {
            @Override
            public void onSuccess(String response) {
                dismissLoadingDialog();
                AppLog.e("Node", response);
                if (!TextUtils.isEmpty(response)) {
                    try {
                        formJson(response);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        showToast(getResources().getString(R.string.text_getdata_fail));
                        handleEmptyData(true);
                    }
                } else {
                    showToast(getResources().getString(R.string.text_getdata_fail));
                    handleEmptyData(true);
                }
            }

            @Override
            public void onFailure(Exception e) {
                dismissLoadingDialog();
                showToast(getResources().getString(R.string.text_getdata_fail));
                handleEmptyData(true);
            }
        });

    }


    private void formJson(String json) throws JSONException {
        JSONObject jsonObject = new JSONObject(json).getJSONObject("result");
        JSONArray array = jsonObject.optJSONArray("rows");
        String total_producer_vote_weight = jsonObject.optString("total_producer_vote_weight");
        String more = jsonObject.optString("more");

        mNodeList.clear();
        if (array != null) {
            for (int i = 0; i < array.length(); i++) {
                NodeBean bean = new NodeBean();
                JSONObject object = array.getJSONObject(i);
                bean.setOwner(object.optString("owner"));
                bean.setTotal_votes(object.optString("total_votes"));
                bean.setProducer_key(object.optString("producer_key"));
                bean.setIs_active(object.optInt("is_active"));
                bean.setUrl(object.optString("url"));
                bean.setUnpaid_blocks(object.optInt("unpaid_blocks"));
                bean.setLast_claim_time(object.getString("last_claim_time"));
                bean.setLocation(object.optInt("location"));
                bean.setIndex(i + 1);
                if (Double.parseDouble(bean.getTotal_votes()) == 0) {
                    bean.setRate("0");
                } else {
                    String dou = NumberUtil.div(NumberUtil.mul(Double.parseDouble(bean.getTotal_votes()), 100), Double.parseDouble(total_producer_vote_weight), 2) + "";
                    bean.setRate(NumberUtil.setFormate(dou));
                }

//                if (mAlreadyVoteNodeList != null && mAlreadyVoteNodeList.size() > 0) {
//                    for (String s : mAlreadyVoteNodeList) {
//                        if (s.equalsIgnoreCase(bean.getOwner())) {
////                            bean.setIsSelect(NodeBean.SELECT);
////                            mSelectList.add(bean);
//
//                            break;
//                        }
//                    }
//                }
                mNodeList.add(bean);
            }
        }
        handleSelectNode();
        mEosNodeListAdapter.setNewData(mNodeList);


    }

    private void searchNode(String nodeName) {
        showLoadingDialog();
        List<NodeBean> nodeBeans = new ArrayList<>();
        for (NodeBean bean : mNodeList) {
            if (bean.getOwner().contains(nodeName)) {
                nodeBeans.add(bean);
            }
        }
        if (nodeBeans.size() > 0) {
            handleEmptyData(false);
            mEosNodeListAdapter.setNewData(nodeBeans);
        } else {
            handleEmptyData(true);
        }
        dismissLoadingDialog();
    }

    private void handleEmptyData(boolean isShow) {
        llEmptyData.setVisibility(isShow ? View.VISIBLE : View.GONE);

    }

    private EosNodeSelectDialog.SubListener mSubListener = new EosNodeSelectDialog.SubListener() {
        @Override
        public void sub(NodeBean bean) {
            showToast(getResources().getString(R.string.text_eos_sub_succeed));
            mEosNodeListAdapter.notifyDataSetChanged();
            handleSelectNode();
        }
    };


    /**
     * 创建投票action
     *
     * @param accountName
     * @param actionType
     * @param nodeList
     */
    private void createVoteAction(String accountName, int actionType, List<NodeBean> nodeList) {
        nodeNmaeList.clear();
        for (NodeBean bean : nodeList) {
            nodeNmaeList.add(bean.getOwner());
        }
        final List<Action> actions = new ArrayList<>();
        String actionStr = EosRequest.getVoteActionMsg(EosMain.get(),actionType, accountName, nodeNmaeList);
        EosUtil.netDataPost(NEWAPI.COIN_HTTP_HEADER, actionStr, new UiResultCallback() {
            @Override
            public void onSuccessed(String response) {
                try {
                    JSONObject json = new JSONObject(response).getJSONObject("result");
                    String binargs = json.getString("binargs");
                    Action actioncpu = new Action(EosMain.get().getUriScheme(), EosRequest.getActionName(EosMain.get(),actionType));
                    actioncpu.setAuthorization(EosUtil.getActivePermission(accountName));
                    actioncpu.setData(binargs);
                    actions.add(actioncpu);
                    String s = EosUtil.prettyPrintJson(actioncpu);
                    getBlockNode(actions);
                } catch (Exception e) {
                    e.printStackTrace();
                    showToast(getResources().getString(R.string.text_eos_server_error));
                }
                dismissLoadingDialog();
            }

            @Override
            public void onFailured(String response) {
                dismissLoadingDialog();
                showToast(getResources().getString(R.string.text_getdata_fail));
            }
        });
    }

    /**
     * 查询节点信息
     *
     * @param actions
     */
    private void getBlockNode(List<Action> actions) {
        CoinMessageModel coinMessageModel = DaoHelper.getCoinMsgManager().getCoinMsgByCoinID(EosMain.get().getCoinID());
        new CoinRequestUtil(coinMessageModel).getNewBlock(new RequestCallback() {
            @Override
            public void onSucess(String result) {
                try {
                    JSONObject json = new JSONObject(result);
                    JSONObject jsonObject = json.getJSONObject("result");
                    long height = jsonObject.optLong("node_height");
                    if (height > 0) {
                        long blockTime = jsonObject.optLong("block_time");
                        EosChainInfo info = new EosChainInfo();
                        JSONObject jsonObject1 = jsonObject.getJSONObject("extra");
                        info.setHeadBlockId(jsonObject1.optString("head_block_id"));
                        info.setHeadBlockTime(TimeUtil.getEosUtcTime(blockTime));
                        info.setChain_id(jsonObject1.optString("chain_id"));
                        if (null != info) {
                            final SignedTransaction transaction = EosUtil.createTransaction(actions, info);
                            transaction.setReferenceBlock(info.getHeadBlockId());
                            transaction.setExpiration(info.getTimeAfterHeadBlockTime(Constant.EOS_ACTION_DEADLING));
                            toSmartSign(transaction, info);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(String message) {
                showToast(getResources().getString(R.string.text_getdata_fail));
            }
        });

    }


    private void dealAction(final SignedTransaction transaction, EosChainInfo info) {
        EosSendRequest eosTxInfo = new EosSendRequest();
        eosTxInfo.setSymbol(EosMain.get().getSymbol());
        eosTxInfo.setAccess(Constant.EOS_TRANSFER_OUT);
        eosTxInfo.setValidate(mEosAccount.getPubkey().substring(0, 11));
        eosTxInfo.setFrom(mEosAccount_name);

//        eosTxInfo.setTo(mEosAccount_name);
        eosTxInfo.setAmount("0");
        eosTxInfo.setAction(Constant.EOS_ACTION_REGIS_VOTE);
        //组装节点数据传给冷端
        StringBuffer buffer = new StringBuffer();
        for (String s : nodeNmaeList) {
            buffer.append(s + "<br>");
        }
        eosTxInfo.setTime(TimeUtil.getCurrentTimeSecond());//账户创建时间
        eosTxInfo.setOther(buffer.toString());//拓展信息字段
        eosTxInfo.setSign(EosUtil.getSignID());
        eosTxInfo.setSignID(EosUtil.getSignID());

        String s1 = EosUtil.prettyPrintJson(transaction);
        Intent intent = new Intent(mContext, CommonQrActivity.class);
        intent.putExtra("eos_trans_info", eosTxInfo);
        intent.putExtra("chain_id", info.getChain_id());
        intent.putStringArrayListExtra(BroadcastEosActivity.KEY_NDOE_LIST, nodeNmaeList);
        intent.putExtra(CommonQrActivity.CODE_QT, Constant.QT_TYPE_TX);
        startActivity(intent);
        tvCancel.postDelayed(new Runnable() {
            @Override
            public void run() {
                EventBusUtil.sendEvent(new EventMsg(EventMsg.CODE_INTENT_EOS_UNSIGN_TRANSACTION,
                        transaction));
            }
        }, 500);
    }


    /**
     * 跳转到Smart二维码界面
     *
     * @param transaction
     * @param info
     */
    private void toSmartSign(final SignedTransaction transaction, EosChainInfo info) {
        EosSendRequest eosTxInfo = new EosSendRequest();

        String signHash = transaction.getSignHash(new TypeChainId(info.getChain_id()));
        eosTxInfo.setSign(signHash);
        eosTxInfo.setSignID(EosUtil.getSignID());
        eosTxInfo.setAction(Constant.EOS_ACTION_REGIS_VOTE);

        EosSendRequest eosSignRequest = new EosSendRequest();
        eosSignRequest.setSymbol(EosMain.get().getSymbol());
        eosSignRequest.setWaID(mColdlarMsgModel.getWalletID());
        eosSignRequest.setTime(System.currentTimeMillis() / 1000);
        eosSignRequest.setPath(mAddressModel.getAddressPath());
        eosSignRequest.setCoin("EOS");
        eosSignRequest.setValidate(mEosAccount.getPubkey().substring(0, 11));
        eosSignRequest.setSign(eosTxInfo.getSign());
        eosSignRequest.setAction(Constant.EOS_ACTION_REGIS_VOTE);
        eosSignRequest.setFrom(mAddressModel.getAddress());
        StringBuffer buffer = new StringBuffer();
        for (String s : nodeNmaeList) {
            buffer.append(s + "<br>");
        }
        eosSignRequest.setOther(buffer.toString());
        eosSignRequest.setSign(eosTxInfo.getSign());
        eosSignRequest.setVoterNumber(mEosAccount.getVoter().getProducers().size());
        eosSignRequest.setChainID(info.getChain_id());
        eosSignRequest.setAmount("0");
        List<String> nodeList = new ArrayList<>();
        for (String s : nodeNmaeList) {
            nodeList.add(s);
        }
        eosSignRequest.setNodeList(nodeList);
        eosSignRequest.setmBatch(mColdlarMsgModel.getBatch());
        eosSignRequest.setSignID(eosTxInfo.getSignID());
        eosSignRequest.setCoinID(mAddressModel.getCoinID());

        int block_num = new BigInteger(1, HexUtils.toBytes(info.getHeadBlockId().substring(0, 8))).intValue();
        long ref_block_prefix = new BigInteger(1, HexUtils.toBytesReversed(info.getHeadBlockId().substring(16, 24))).longValue();
        eosSignRequest.setEosNodeHeight(block_num);
        eosSignRequest.setEosHeadBlockId(ref_block_prefix);
        eosSignRequest.setEosExpiration(TimeUtil.eosDataToLong(transaction.getExpiration()));

        Intent intent = new Intent(mContext, EosConfirmActivity.class);
        intent.putExtra(Constant.EOS_DATA_KEY, eosSignRequest);
        startActivity(intent);
        tvCancel.postDelayed(new Runnable() {
            @Override
            public void run() {
                EventBusUtil.sendEvent(new EventMsg(EventMsg.CODE_INTENT_EOS_UNSIGN_TRANSACTION, transaction));
            }
        }, 500);
    }

}
