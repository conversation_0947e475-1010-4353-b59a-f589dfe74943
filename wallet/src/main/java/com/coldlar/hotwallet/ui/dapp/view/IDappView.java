package com.coldlar.hotwallet.ui.dapp.view;

import com.coldlar.hotwallet.greendao.DappModel;
import com.coldlar.hotwallet.model.BannerModel;

import java.util.List;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 */
public interface IDappView {



    void updateView();

    void addDisposableView(Disposable disposable);


    void onLoadStart();

    void onLoadFinish();

    void refreshBlockBrowser(List<DappModel> dateList);

    void refreshBanner(List<BannerModel> dateList);

    void refreshRecentlyList(List<DappModel> dateList);

    void refreshFavoriteList(List<DappModel> dateList);


}
