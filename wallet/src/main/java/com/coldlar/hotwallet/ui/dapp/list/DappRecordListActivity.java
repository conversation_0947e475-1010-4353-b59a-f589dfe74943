package com.coldlar.hotwallet.ui.dapp.list;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.activity.BaseTileActivity;
import com.coldlar.hotwallet.greendao.DappModel;
import com.coldlar.hotwallet.greendao.base.DaoHelper;
import com.coldlar.hotwallet.greendao.greenUtils.DappModelManager;
import com.coldlar.hotwallet.ui.dapp.adapter.DappAdapter;
import com.coldlar.hotwallet.ui.dapp.browser.DappWeb3Activity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class DappRecordListActivity extends BaseTileActivity {

    @BindView(R.id.recyclerview)
    RecyclerView resultRecyclerview;

    @BindView(R.id.tv_right_text)
    TextView tvRightText;

    private DappAdapter mAdapter;

    List<DappModel> resultDataList = new ArrayList<>();


    @Override
    protected int getLayoutId() {
        return R.layout.activity_dapp_record_list;
    }


    int mode;

    boolean isEditMode = false;

    @Override
    protected void initView(Bundle savedInstanceState) {
        mode = getIntent().getIntExtra("mode", DappModelManager.Recently_mode);
        setTtle(mode == DappModelManager.Favorite_mode ? getString(R.string.string_favorite) : getString(R.string.string_recently_use));
        setRightText(getResources().getString(R.string.text_edit), 14f, getResources().getColor(R.color.color_orange, null));
        setupRecyclerview();
    }

    @Override
    protected void onRightClick(View v) {
        super.onRightClick(v);
        isEditMode = !isEditMode;
        String text = isEditMode ? getString(R.string.text_complte) : getString(R.string.text_edit);
        setRightText(text);
        mAdapter.setEditMode(isEditMode);

    }


    @Override
    protected void initData() {
        resultDataList = DaoHelper.getDappModelManager().getDappList(mode);
        mAdapter.setList(resultDataList);
    }

    private void setupRecyclerview() {
        resultRecyclerview.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new DappAdapter(R.layout.item_dapp_item, resultDataList);
        resultRecyclerview.setAdapter(mAdapter);
        mAdapter.addChildClickViewIds(R.id.delete_dapp);
        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.delete_dapp) {
                DappModel item = (DappModel) adapter.getItem(position);
                if (mode == DappModelManager.Favorite_mode) {
                    DaoHelper.getDappModelManager().deleteDapp(item, DappModelManager.Favorite_mode);
                } else {
                    DaoHelper.getDappModelManager().deleteDapp(item, DappModelManager.Recently_mode);

                }
                initData();
            }
        });

        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            DappModel item = (DappModel) adapter.getItem(position);
            startActivity(new Intent(mContext, DappWeb3Activity.class)
                    .putExtra(Constant.DAPP_MODEL, item)
            );
        });


    }


}
