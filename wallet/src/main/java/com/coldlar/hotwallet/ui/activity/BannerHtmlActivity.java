package com.coldlar.hotwallet.ui.activity;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Html;
import android.text.method.LinkMovementMethod;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.activity.BaseTileActivity;
import com.coldlar.hotwallet.model.BannerInfo;

import java.net.URL;

public class BannerHtmlActivity extends BaseTileActivity {

    private TextView tv_html;
    private String urls;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_banner_html;
    }



    @Override
    protected void initView(Bundle savedInstanceState) {
        TextView title = (TextView) findViewById(R.id.title);
        title.setText(getResources().getString(R.string.text_coldlar_news));
        RelativeLayout right_img_lay = (RelativeLayout) findViewById(R.id.right_img_lay);
        right_img_lay.setVisibility(View.GONE);
        tv_html = (TextView) findViewById(R.id.tv_html);
    }

    @Override
    protected void initListener() {

    }

    @Override
    protected void initData() {
        BannerInfo bannerInfo = (BannerInfo) getIntent().getSerializableExtra("bannerInfo");
        String info = bannerInfo.getHrefurl();
        urls = bannerInfo.getUrl();
        tv_html.setMovementMethod(ScrollingMovementMethod.getInstance());
        tv_html.setMovementMethod(LinkMovementMethod.getInstance());
        tv_html.setText(Html.fromHtml(info,imageGetter,null));




    }
    //解析图片
    final Html.ImageGetter imageGetter = new Html.ImageGetter() {
        @Override
        public Drawable getDrawable(String source) {
            Drawable drawable = null;
            URL url;
            try {
                url = new URL(urls);
                drawable = Drawable.createFromStream(url.openStream(), ""); // 获取网路图片
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            return drawable;
        }

    };

}
