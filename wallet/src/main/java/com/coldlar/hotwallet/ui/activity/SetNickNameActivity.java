package com.coldlar.hotwallet.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.R;
import com.coldlar.hotwallet.base.activity.BaseTileActivity;
import com.coldlar.hotwallet.model.request.UserNameModel;
import com.coldlar.hotwallet.network.API;
import com.coldlar.hotwallet.network.OkHttpCallback;
import com.coldlar.hotwallet.network.OkHttpUtils;
import com.coldlar.hotwallet.utils.RegexUtils;
import com.coldlar.hotwallet.utils.SpfHelper;
import com.coldlar.hotwallet.utils.StringUtil;
import com.coldlar.hotwallet.utils.view.Keyboard;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;

public class SetNickNameActivity extends BaseTileActivity {

    private static final int SUCCESS = 0X11;
    private static final int FAIL = 0X12;

    @BindView(R.id.username_editor)
    EditText usernameEditor;
    @BindView(R.id.id_btn)
    Button userEditorKeep;
    private String username;
    private String mToken;


    @Override
    protected int getLayoutId() {
        return R.layout.activity_user_name_editor;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        userEditorKeep.setOnClickListener(this);
        Keyboard.filterEmojiAndSpace(usernameEditor, 20);
    }

    @Override
    protected void initData() {
        setTtle(getResources().getString(R.string.text_user_name_editor));
        userEditorKeep.setText(R.string.text_txdetail_confrim);
        mToken = (String) SpfHelper.getParam(mContext, Constant.ACCOUNT_TOKEN, "");
        String nickname = (String) SpfHelper.getParam(mContext, Constant.ACCOUNT_NICKNAME, "");
        usernameEditor.setText(nickname);
        usernameEditor.setSelection(usernameEditor.getText().toString().length());
        usernameEditor.addTextChangedListener(textWatcher);
    }


    @Override
    public void handleClick(View v) {
        super.handleClick(v);
        switch (v.getId()) {
            case R.id.id_btn:
                String userName = usernameEditor.getText().toString();
                username = userName;
                if (StringUtil.isEmpty(userName)) {
                    showToast(getResources().getString(R.string.text_username_edit_commit));
                } else if (!RegexUtils.isNickname(userName)) {
                    showToast(getResources().getString(R.string.text_nickname_error));
                } else {
                    UserNameModel userNameModel = new UserNameModel();
                    userNameModel.setNickName(userName);
                    Gson gson = new Gson();
                    String json = gson.toJson(userNameModel);
                    getOk(json);
                }
                break;
        }
    }


    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case SUCCESS:
                    String result = (String) msg.obj;
                    try {
                        JSONObject jsonObject = new JSONObject(result);
                        int code = jsonObject.getInt("code");
                        if (code == Constant.REQUEST_SUCESS) {
                            SpfHelper.setParam(mContext, Constant.ACCOUNT_NICKNAME, username);
                            Intent intent = new Intent();
                            intent.putExtra("edit", username);
                            setResult(RESULT_OK, intent);
                            finish();
                        } else {
                            showToast(getResources().getString(R.string.text_update_username_fail));
                        }

                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    break;
                case FAIL:
                    showToast(getResources().getString(R.string.text_update_username_fail));
                    break;
            }
        }
    };

    private void getOk(String str) {
        String url = API.API_USERNAME;
        OkHttpUtils.post(url, str, mToken, new OkHttpCallback(new OkHttpCallback.OkHttpListener() {
            @Override
            public void onSuccess(String message, String response) {
                try {
                    JSONObject jsonObject = new JSONObject(response);
                    if (!StringUtil.isEmpty(response)) {
                        Message msg = handler.obtainMessage();
                        msg.what = SUCCESS;
                        msg.obj = response;
                        handler.sendMessage(msg);
                    } else {
                        handler.sendEmptyMessage(FAIL);
                    }

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(String errorMsg) {
                handler.sendEmptyMessage(FAIL);
            }
        }));
    }

    private TextWatcher textWatcher = new TextWatcher() {
        private int editStart;
        private int editEnd;
        private int maxLen = 12;

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            editStart = usernameEditor.getSelectionStart();
            editEnd = usernameEditor.getSelectionEnd();
            usernameEditor.removeTextChangedListener(textWatcher);
            if (!TextUtils.isEmpty(usernameEditor.getText())) {
                String etstring = usernameEditor.getText().toString().trim();
                while (calculateLength(s.toString()) > maxLen) {
                    s.delete(editStart - 1, editEnd);
                    editStart--;
                    editEnd--;
                }
            }
            usernameEditor.setText(s);
            usernameEditor.setSelection(editStart);

//            }
            usernameEditor.addTextChangedListener(textWatcher);
        }

        private int calculateLength(String etstring) {
            char[] ch = etstring.toCharArray();
            int varlength = 0;
            for (int i = 0; i < ch.length; i++) {
                if ((ch[i] >= 0x2E80 && ch[i] <= 0xFE4F) || (ch[i] >= 0xA13F
                        && ch[i] <= 0xAA40) || ch[i] >= 0x80) {
                    varlength = varlength + 2;
                } else {
                    varlength++;
                }
            }
            return varlength;
        }
    };
}
