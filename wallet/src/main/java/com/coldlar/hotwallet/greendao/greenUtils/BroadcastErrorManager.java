package com.coldlar.hotwallet.greendao.greenUtils;

import com.coldlar.hotwallet.db.dao.BroadcastErrorModelDao;
import com.coldlar.hotwallet.greendao.BroadcastErrorModel;
import com.coldlar.hotwallet.greendao.base.BaseDao;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

/**
 * author wang<PERSON><PERSON>
 * date 2018/6/20 15:56
 * FileName:  BroadcastErrorManager
 * description 广播错误表管理
 */
public class BroadcastErrorManager extends BaseDao<BroadcastErrorModel> {

    private BroadcastErrorModelDao getBroadMsgDao() {
        return daoSession.getBroadcastErrorModelDao();
    }


    /**
     * 查询所有数据
     * @return
     */
    public List<BroadcastErrorModel> getAllData(){
        List<BroadcastErrorModel> list= getBroadMsgDao().loadAll();
        if(list==null){
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 保存错误信息model
     * @param model
     */
    public void saveErrorMsg(BroadcastErrorModel model){
        List<BroadcastErrorModel> list = getBroadMsgDao().queryBuilder().where(
                BroadcastErrorModelDao.Properties.RawTx.eq(model.getRawtx()),
                BroadcastErrorModelDao.Properties.ErrorMsg.eq(model.getErrorMsg())).list();
        if(list==null||list.size()==0){
            getBroadMsgDao().insert(model);
        }
    }


    /**
     * 查询错误信息列表
     * @param
     */
    public String  getErrorMsg(){
      List<BroadcastErrorModel> list=getBroadMsgDao().queryBuilder().list();
      if(list!=null&&list.size()>0){
          return new Gson().toJson(list);
      }
    return "";
    }



    /**
     * 上传成功后清除本地数据
     */
    public void clearData() {
        getBroadMsgDao().deleteAll();
    }

}
