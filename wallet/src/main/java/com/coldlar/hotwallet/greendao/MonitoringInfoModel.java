package com.coldlar.hotwallet.greendao;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;

/**
 * author wang<PERSON><PERSON>
 * date 2018/6/20 15:17
 * FileName:  MonitoringInfoModel
 * description 监控信息表
 */
@Entity(nameInDb = "monitoring_infoe_book")
public class MonitoringInfoModel {
    @Id(autoincrement = true)
    private Long id;

    @Property(nameInDb = "time")
    private long time;//时间

    @Property(nameInDb = "coin_amount")
    private String coinNumber;//币种数量

    @Property(nameInDb = "add_amount")
    private String addressNumber;//地址数量

    @Property(nameInDb = "datch_id")
    private int batch;//批次

    @Property(nameInDb = "coldlar_id")
    private String devicesCode;//设备ID

    @Property(nameInDb = "coldlar_model")
    private String devicesModel;//设备型号

    @Property(nameInDb = "wallet_id")
    private String walletAccountId;//钱包账户ID

    @Property(nameInDb = "wallet_name")
    private String walletAccount;//钱包账户名

    @Property(nameInDb = "phone_id")
    private String phoneInfo;//手机唯一标识

    @Property(nameInDb = "user_id")
    private String uid;//用户ID

    @Property(nameInDb = "sign")
    private String sign;
    //唯一标示
    @Property(nameInDb = "uuid")
    private String uuid;
    //经度
    @Property(nameInDb = "lng")
    private String lng;
    //维度
    @Property(nameInDb = "lat")
    private String lat;

    @Generated(hash = *********)
    public MonitoringInfoModel(Long id, long time, String coinNumber,
            String addressNumber, int batch, String devicesCode,
            String devicesModel, String walletAccountId, String walletAccount,
            String phoneInfo, String uid, String sign, String uuid, String lng,
            String lat) {
        this.id = id;
        this.time = time;
        this.coinNumber = coinNumber;
        this.addressNumber = addressNumber;
        this.batch = batch;
        this.devicesCode = devicesCode;
        this.devicesModel = devicesModel;
        this.walletAccountId = walletAccountId;
        this.walletAccount = walletAccount;
        this.phoneInfo = phoneInfo;
        this.uid = uid;
        this.sign = sign;
        this.uuid = uuid;
        this.lng = lng;
        this.lat = lat;
    }

    @Generated(hash = *********)
    public MonitoringInfoModel() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getTime() {
        return this.time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public String getCoinNumber() {
        return this.coinNumber;
    }

    public void setCoinNumber(String coinNumber) {
        this.coinNumber = coinNumber;
    }

    public String getAddressNumber() {
        return this.addressNumber;
    }

    public void setAddressNumber(String addressNumber) {
        this.addressNumber = addressNumber;
    }

    public int getBatch() {
        return this.batch;
    }

    public void setBatch(int batch) {
        this.batch = batch;
    }

    public String getDevicesCode() {
        return this.devicesCode;
    }

    public void setDevicesCode(String devicesCode) {
        this.devicesCode = devicesCode;
    }

    public String getDevicesModel() {
        return this.devicesModel;
    }

    public void setDevicesModel(String devicesModel) {
        this.devicesModel = devicesModel;
    }

    public String getWalletAccountId() {
        return this.walletAccountId;
    }

    public void setWalletAccountId(String walletAccountId) {
        this.walletAccountId = walletAccountId;
    }

    public String getWalletAccount() {
        return this.walletAccount;
    }

    public void setWalletAccount(String walletAccount) {
        this.walletAccount = walletAccount;
    }

    public String getPhoneInfo() {
        return this.phoneInfo;
    }

    public void setPhoneInfo(String phoneInfo) {
        this.phoneInfo = phoneInfo;
    }

    public String getUserId() {
        return this.uid;
    }

    public void setUserId(String userId) {
        this.uid = userId;
    }

    public String getUid() {
        return this.uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSign() {
        return this.sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    //    @Property(nameInDb = "phone_type")
//    private String phoneType;//手机系统


}
