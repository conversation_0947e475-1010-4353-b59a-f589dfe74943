package com.coldlar.hotwallet.view.loading;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;

/**
 * 
* @ClassName: IndicatorController 
* @Description: TODO(这里用一句话描述这个类的作用) 
* <AUTHOR>
* @date 2016年4月26日 下午4:59:36 
*
 */
public abstract class IndicatorController {

    private View mTarget;


    public void setTarget(View target){
        this.mTarget=target;
    }

    public View getTarget(){
        return mTarget;
    }


    public int getWidth(){
        return mTarget.getWidth();
    }

    public int getHeight(){
        return mTarget.getHeight();
    }

    public void postInvalidate(){
        mTarget.postInvalidate();
    }

    /**
     * draw indicator what ever
     * you want to draw
     * @param canvas
     * @param paint
     */
    public abstract void draw(Canvas canvas, Paint paint);

    /**
     * create animation or animations
     * ,and add to your indicator.
     */
    public abstract void createAnimation();


}
