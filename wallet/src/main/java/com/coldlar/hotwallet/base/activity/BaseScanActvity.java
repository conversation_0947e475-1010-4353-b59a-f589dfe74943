package com.coldlar.hotwallet.base.activity;

import static android.Manifest.permission.CAMERA;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;

import androidx.annotation.NonNull;

import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.R;

import java.util.List;

import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.AppSettingsDialog;
import pub.devrel.easypermissions.EasyPermissions;
import pub.devrel.easypermissions.PermissionRequest;

/**
 * <AUTHOR>
 * @date 2020/4/1 13:52
 * description 扫一扫页面基类 申请相机权限
 */
public abstract class BaseScanActvity extends BaseActivity implements EasyPermissions.PermissionCallbacks
        , EasyPermissions.RationaleCallbacks {
    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
        if (requestCode == Constant.PERMISSIONS_REQUEST_CAMERA) {
            //永久拒绝权限
            if (EasyPermissions.somePermissionPermanentlyDenied(this, perms)) {
                AppSettingsDialog.Builder builder = new AppSettingsDialog.Builder(this);
                builder.setTitle(getResources().getString(R.string.notice))// todo
                        .setRationale(R.string.text_camera_permission_hint)
                        .setRequestCode(300)
                        .build()
                        .show();
            } else {
                PermissionRequest.Builder builder = new PermissionRequest.Builder(this, Constant.PERMISSIONS_REQUEST_CAMERA, Manifest.permission.CAMERA);
                builder.setRationale(R.string.req_camera_setting_ask);
                EasyPermissions.requestPermissions(builder.build());
            }

        }
    }

    private boolean hasCameraPermission() {
        return EasyPermissions.hasPermissions(this, CAMERA);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        //允许相机权限
        if (Constant.PERMISSIONS_REQUEST_CAMERA == requestCode && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            onResume();
            openCamera();
        } else {
            EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
        }

    }

    @Override
    protected boolean isImmersionBarEnabled() {
        return false;
    }


    @AfterPermissionGranted(Constant.PERMISSIONS_REQUEST_CAMERA)
    public void cameraTask() {
        if (hasCameraPermission()) {
            openCamera();
        } else {

            PermissionRequest.Builder builder = new PermissionRequest.Builder(this, Constant.PERMISSIONS_REQUEST_CAMERA, Manifest.permission.CAMERA);
            builder.setRationale(R.string.req_camera_setting_ask);
            EasyPermissions.requestPermissions(builder.build());
        }
    }


    @Override
    public void onRationaleAccepted(int requestCode) {
        if (requestCode == Constant.PERMISSIONS_REQUEST_CAMERA) {

        }
    }

    @Override
    public void onRationaleDenied(int requestCode) {
        if (requestCode == Constant.PERMISSIONS_REQUEST_CAMERA) {
            finish();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 300) {
            if (hasCameraPermission()) {
                onResume();
                openCamera();
            } else {
                finish();

            }
        }
    }


    /**
     * 打开相机
     */
    protected void openCamera() {

    }

    @Override
    protected boolean translucentStatusBar() {
        return true;
    }


}
