package com.coldlar.hotwallet.jpush.receiver;

import android.app.ActivityManager;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.coldlar.hotwallet.Constant;
import com.coldlar.hotwallet.jpush.JPushMsgUtils;
import com.coldlar.hotwallet.utils.SpfHelper;
import com.coldlar.hotwallet.utils.view.AppLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

import cn.jpush.android.api.JPushInterface;

public class MyReceiver extends BroadcastReceiver {

    private static final String TAG = "MyReceiver";
    private NotificationManager nm;
    int index = 0;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (null == nm) {
            nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        }
        Bundle bundle = intent.getExtras();
        if (JPushInterface.ACTION_REGISTRATION_ID.equals(intent.getAction())) {
            index++;
            String registrationID = JPushInterface.getRegistrationID(context);
            SpfHelper.saveString(context, Constant.REGIDSRATION_ID, registrationID);
            AppLog.e(TAG, "[MyReceiver] 接收Registration Id : " + registrationID);
        } else if (JPushInterface.ACTION_MESSAGE_RECEIVED.equals(intent.getAction())) {
            AppLog.e(TAG, "[MyReceiver] 接收到推送下来的自定义消息: " + bundle.getString(JPushInterface.EXTRA_EXTRA));

        } else if (JPushInterface.ACTION_NOTIFICATION_RECEIVED.equals(intent.getAction())) {
            String title = bundle.getString(JPushInterface.EXTRA_NOTIFICATION_TITLE);
            String message = bundle.getString(JPushInterface.EXTRA_ALERT);
            String msgID = bundle.getString(JPushInterface.EXTRA_MSG_ID);
            String bundles = bundle.toString();
            AppLog.e(TAG, "[MyReceiver] 接收到推送下来的通知>>" + title, message, bundles);


            try {
                String extra = bundle.getString(JPushInterface.EXTRA_EXTRA);
                AppLog.e(TAG, "[MyReceiver] 用户点击打开了通知" + extra);
                JSONObject jsonextra = new JSONObject(extra);
                String masterMsgType = jsonextra.optString("msgType");

//                if (Integer.parseInt(masterMsgType) == MessageModel.TYPE_TX) {
//                    //处理交易类型
//                    JPushMsgUtils.disposeTx(context, extra, false);
//                } else {
//                    JPushMsgUtils.saveMessage(MessageModel.UNREAD, extra);
//                }

            } catch (JSONException e) {

                e.printStackTrace();
            }
        } else if (JPushInterface.ACTION_NOTIFICATION_OPENED.equals(intent.getAction())) {
            Bundle bundle1 = intent.getExtras();
            String msgID = bundle1.getString(JPushInterface.EXTRA_MSG_ID);
            String extra = bundle1.getString(JPushInterface.EXTRA_EXTRA);
            AppLog.e(TAG, "[MyReceiver] 用户点击打开了通知" + extra);
            JPushMsgUtils.disposeMsg(context, extra);
        }
    }

    /**
     * 判断 APP是前台还是后台,还是未启动
     *
     * @param context
     * @return 1 前台 2后台 3未启动
     */
    public int getAppSatus(Context context) {

        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> list = am.getRunningTasks(20);

        //判断程序是否在栈顶
        if (list.get(0).topActivity.getPackageName().equals(context.getPackageName())) {
            return 1;
        } else {
            //判断程序是否在栈里
            for (ActivityManager.RunningTaskInfo info : list) {
                if (info.topActivity.getPackageName().equals(context.getPackageName())) {
                    return 2;
                }
            }
            return 3;//栈里找不到，返回3
        }
    }


}
