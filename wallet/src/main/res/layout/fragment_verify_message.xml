<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_notice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_vertical_margin"
                    android:gravity="bottom"
                    android:text="@string/signing_info"
                    android:textColor="@color/text_color_6f"
                    android:textSize="@dimen/text_size_14" />

                <View style="@style/wire_horizontal_bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="@dimen/activity_vertical_margin"
                    android:layout_marginTop="@dimen/activity_vertical_margin"
                    android:text="@string/text_choose_zhulian"
                    android:textColor="@color/color_text_main"
                    android:textSize="@dimen/text_size_16"
                    />

                <RelativeLayout
                    android:id="@+id/rl_chain"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="@dimen/activity_vertical_margin"
                    android:layout_marginTop="@dimen/common_margin_10dp"
                    android:layout_marginRight="@dimen/activity_vertical_margin"
                    android:background="@drawable/shape_btn_bg_line">

                    <TextView
                        android:id="@+id/tv_chain"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:drawableEnd="@mipmap/arrow"
                        android:gravity="center_vertical"
                        android:hint="@string/text_choose_chain1"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_16" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="@string/signing_address_label"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_16" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true">

                        <RelativeLayout
                            android:id="@+id/rl_choice_address"
                            android:layout_width="44dp"
                            android:layout_height="match_parent"
                            android:visibility="gone">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_centerVertical="true"
                                android:src="@mipmap/icon_contact_address" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_scan_address"
                            android:layout_width="40dp"
                            android:layout_height="match_parent"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:visibility="visible">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:src="@mipmap/icon_sao" />
                        </RelativeLayout>

                    </LinearLayout>


                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="@dimen/activity_vertical_margin"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="@dimen/activity_vertical_margin"
                    android:background="@drawable/shape_btn_bg_line"
                    android:gravity="center_vertical"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp">

                    <AutoCompleteTextView
                        android:id="@+id/signing_address"
                        style="@style/NormalText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:completionThreshold="1"
                        android:hint="@string/text_insert_address"
                        android:imeOptions="actionNext|flagNoExtractUi"
                        android:inputType="textNoSuggestions"
                        android:singleLine="true"
                        android:textSize="@dimen/text_size_16" />

                </LinearLayout>


                <TextView
                    android:id="@+id/address_error_message"
                    style="@style/ErrorText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="16dp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical"
                        android:text="@string/message_to_sign_label"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_16" />

                    <RelativeLayout
                        android:id="@+id/rl_swipe_message"
                        android:layout_width="40dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:src="@mipmap/icon_sao" />
                    </RelativeLayout>
                </RelativeLayout>


                <EditText
                    android:id="@+id/message"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginLeft="@dimen/activity_vertical_margin"
                    android:layout_marginRight="@dimen/activity_vertical_margin"
                    android:background="@drawable/shape_input_bg"
                    android:gravity="top"
                    android:hint="@string/text_hint_input_sign_content"
                    android:inputType="textMultiLine"
                    android:padding="10dp"
                    android:textColor="@color/color_text_main"
                    android:textColorHint="@color/color_text_assist"
                    android:textSize="@dimen/text_size_16" />


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical"
                        android:text="@string/signature_label"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_16"/>

                    <RelativeLayout
                        android:id="@+id/rl_show_qr"
                        android:layout_width="40dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:gravity="center_vertical"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp">

                        <ImageView
                            android:id="@+id/iv_show_qr"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_centerHorizontal="true"
                            android:src="@mipmap/icon_sao" />
                    </RelativeLayout>
                </RelativeLayout>

                <EditText
                    android:id="@+id/signature"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginLeft="@dimen/activity_vertical_margin"
                    android:layout_marginRight="@dimen/activity_vertical_margin"
                    android:background="@drawable/shape_input_bg"
                    android:ems="10"
                    android:gravity="top"
                    android:hint="@string/text_hint_input_sign"
                    android:inputType="textMultiLine|textNoSuggestions"
                    android:padding="10dp"
                    android:textColor="@color/color_text_main"
                    android:textColorHint="@color/color_text_assist"
                    android:textSize="@dimen/text_size_16" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_horizontal">

                    <TextView
                        android:id="@+id/signature_ok"
                        style="@style/OkText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        tools:text="@string/message_signed"
                        tools:visibility="gone" />
                </LinearLayout>


                <TextView
                    android:id="@+id/signature_error"
                    style="@style/ErrorText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="16dp"
                    android:visibility="gone" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="25dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingLeft="16dp"
                android:paddingRight="16dp">

                <Button
                    android:id="@+id/button_clear"
                    style="@style/bottom_button_white"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="@dimen/common_margin_20dp"
                    android:layout_weight="1"
                    android:text="@string/button_clear" />
                <Button
                    android:id="@+id/button_ok"
                    style="@style/bottom_button_black"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="@dimen/common_margin_20dp"
                    android:layout_weight="1"
                    android:text="@string/action_verify_message" />


            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
