<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dia_log_bg"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/common_margin_30dp"
        android:text="@string/text_safe_remind_title"
        android:textColor="@color/color_text_main"
        android:textSize="@dimen/text_size_17"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/common_margin_25dp"
        android:layout_marginTop="@dimen/common_margin_30dp"
        android:layout_marginRight="@dimen/common_margin_25dp"
        android:lineSpacingExtra="5dp"
        android:text="@string/text_eos_deposit_security_hint"
        android:textColor="@color/color_text_main"
        android:textSize="@dimen/text_size_14" />

    <TextView
        android:layout_marginBottom="@dimen/common_margin_40dp"
        android:id="@+id/tvKnow"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="@dimen/common_margin_30dp"
        android:background="@drawable/ripple_btn_black_gradient"
        android:gravity="center"
        android:paddingLeft="@dimen/common_padding_50dp"
        android:paddingRight="@dimen/common_padding_50dp"
        android:text="@string/text_know"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_14" />

</LinearLayout>
