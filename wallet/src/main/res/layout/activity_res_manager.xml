<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:paddingLeft="@dimen/common_padding_25dp"
                android:paddingRight="@dimen/common_padding_25dp"
                android:paddingTop="@dimen/common_margin_20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_total_assets"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_14" />

                    <TextView
                        android:id="@+id/tvTotalBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/common_margin_5dp"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_16"
                        android:textStyle="bold"
                        tools:text="0.123456789 EOS" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/common_margin_20dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="5dp"
                            android:layout_height="5dp"
                            android:background="@color/color_res_balance" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/common_margin_10dp"
                            android:layout_marginEnd="@dimen/common_margin_5dp"
                            android:text="@string/text_available_balance"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12" />

                        <TextView
                            android:id="@+id/tvBalance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12"
                            tools:text="0.123456" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/common_margin_10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="5dp"
                            android:layout_height="5dp"
                            android:background="@color/color_res_pledge" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/common_margin_10dp"
                            android:layout_marginEnd="@dimen/common_margin_5dp"
                            android:text="@string/text_eos_mortgaged"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12" />

                        <TextView
                            android:id="@+id/tvPledge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12"
                            tools:text="0.123456" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/common_margin_10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="5dp"
                            android:layout_height="5dp"
                            android:background="@color/color_res_redeem" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/common_margin_10dp"
                            android:layout_marginEnd="@dimen/common_margin_5dp"
                            android:text="@string/text_eos_refunding"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12" />

                        <TextView
                            android:id="@+id/tvRedeem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_6f"
                            android:textSize="@dimen/text_size_12"
                            tools:text="0.123456" />

                        <TextView
                            android:id="@+id/tvRedeemTime"
                            android:layout_width="wrap_content"
                            android:layout_height="24dp"
                            android:layout_marginLeft="@dimen/common_margin_10dp"
                            android:background="@drawable/shape_btn_bg_line"
                            android:drawableLeft="@mipmap/icon_clock"
                            android:drawablePadding="3dp"
                            android:gravity="center"
                            android:paddingStart="@dimen/common_margin_10dp"
                            android:paddingEnd="@dimen/common_margin_10dp"
                            android:textColor="#F29500"
                            android:textSize="@dimen/text_size_12"
                            tools:text="71h 48m" />
                    </LinearLayout>
                </LinearLayout>

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/chart"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_vertical"
                   />
            </LinearLayout>

            <View style="@style/wire_horizontal_bold" />


            <RelativeLayout
                android:id="@+id/layoutRam"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_margin_80dp"
                android:layout_marginStart="@dimen/activity_vertical_margin"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="@dimen/activity_vertical_margin"
                android:background="@drawable/shape_pup_bg_f9"
                android:orientation="vertical"
                android:paddingStart="@dimen/common_padding_20dp"
                android:paddingEnd="@dimen/common_padding_20dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/text_eos_ram"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_14" />

                    <ProgressBar
                        android:id="@+id/pbRam"
                        style="@style/progressBarHorizontal"
                        android:layout_width="190dp"
                        android:layout_height="4dp" />


                    <TextView
                        android:id="@+id/tvRam"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_12"
                        tools:text="3.86 KB / 6.82 KB" />
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/icon_arror_right" />
            </RelativeLayout>

            <TextView
                android:id="@+id/resource_remind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_vertical_margin"
                android:layout_marginTop="4dp"
                android:text="@string/text_ram_used_eos"
                android:textColor="@color/color_text_assist"
                android:textSize="@dimen/text_size_12" />

            <RelativeLayout
                android:id="@+id/layoutCpu"
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:layout_marginStart="@dimen/activity_vertical_margin"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="@dimen/activity_vertical_margin"
                android:background="@drawable/shape_pup_bg_f9"
                android:orientation="vertical"
                android:paddingStart="@dimen/common_padding_20dp"
                android:paddingEnd="@dimen/common_padding_20dp">

                <LinearLayout
                    android:id="@+id/llCpu"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/common_margin_80dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/text_eos_cpu"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_14" />

                    <ProgressBar
                        android:id="@+id/pbCpu"
                        style="@style/progressBarHorizontal"
                        android:layout_width="190dp"
                        android:layout_height="4dp" />


                    <TextView
                        android:id="@+id/tvCpu"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_12"
                        tools:text="0 ms / 0 ms" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/common_margin_80dp"
                    android:layout_below="@+id/llCpu"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/text_eos_net"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_14" />

                    <ProgressBar
                        android:id="@+id/pbNet"
                        style="@style/progressBarHorizontal"
                        android:layout_width="190dp"
                        android:layout_height="4dp" />


                    <TextView
                        android:id="@+id/tvNet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/color_text_main"
                        android:textSize="@dimen/text_size_12"
                        tools:text="3.86 KB / 6.82 KB" />
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/icon_arror_right" />
            </RelativeLayout>

            <TextView
                android:id="@+id/resource_remind_net"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_vertical_margin"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="@dimen/common_margin_20dp"
                android:text="@string/text_net_used_eos"
                android:textColor="@color/color_text_assist"
                android:textSize="@dimen/text_size_12" />


        </LinearLayout>


    </ScrollView>

    <LinearLayout
        android:id="@+id/llHelp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">
        <View style="@style/wire_horizontal" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:drawableStart="@mipmap/icon_question_orange"
            android:gravity="center"
            android:text="@string/text_why_buy_net"
            android:textColor="@color/color_text_assist"
            android:textSize="@dimen/text_size_12" />

        <Button
            android:id="@+id/cpu_rental"
            style="@style/bottom_button_line_white"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/dimen_20dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:text="@string/cpu_rental" />



    </LinearLayout>


</LinearLayout>