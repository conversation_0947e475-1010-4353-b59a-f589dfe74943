<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:background="@color/white"
    android:orientation="vertical"
    >

    <LinearLayout
        android:id="@+id/ll_no_nfc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:src="@mipmap/icon_nfc_close" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_margin_30dp"
            android:gravity="center"
            android:text="@string/text_to_nfc_setting"
            android:textColor="@color/color_text_red"
            android:textSize="@dimen/text_size_14"

            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/common_margin_50dp"
            android:text="@string/text_nfc_tosetting"
            android:textColor="@color/color_text_main"
            android:textSize="@dimen/text_size_14" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/common_margin_30dp">

            <TextView
                android:id="@+id/tv_set"
                style="@style/bottom_button_black"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"

                android:layout_marginTop="32dp"
                android:text="@string/text_enter_set" />
        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_nfc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/nfc_open_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/common_margin_20dp"
            android:layout_marginRight="@dimen/common_margin_20dp"
            android:layout_marginTop="@dimen/common_margin_10dp"
            android:textColor="@color/text_color_6f"
            android:textSize="@dimen/text_size_14"
            tools:text="1.打开库神硬件钱包并解锁进入。" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/common_margin_30dp"
            android:src="@mipmap/icon_nfc_close" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/iv_gif"
            android:layout_width="230dp"
            android:layout_height="230dp"
            android:src="@mipmap/icon_nfc_gif" />
        <TextView
            android:id="@+id/tv_transfer"
            android:layout_marginTop="32dp"
            style="@style/bottom_button_black"
            android:layout_marginBottom="20dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:text="@string/text_transfer" />
    </LinearLayout>

</LinearLayout>
