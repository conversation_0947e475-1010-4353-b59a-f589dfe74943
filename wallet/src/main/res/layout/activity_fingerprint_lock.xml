<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="40dp"
        android:drawablePadding="@dimen/common_padding_12dp"
        android:ellipsize="end"
        android:gravity="center"
        android:textAppearance="@style/TextAppearance.PFMediumFont"
        android:textColor="@color/color_text_main"
        android:textSize="@dimen/text_size_18"
        app:drawableLeftCompat="@mipmap/icon_dunpai"
        tools:text="@string/text_unlock_enter" />

    <TextView
        android:id="@+id/tv_hint1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/common_margin_60dp"
        android:text="@string/text_fingerprint_lock"
        android:textAppearance="@style/TextAppearance.PFMediumFont"
        android:textColor="@color/color_text_main"
        android:textSize="@dimen/text_size_16" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="100dp"
        android:drawablePadding="@dimen/common_padding_20dp"
        android:textSize="@dimen/text_size_14"
        android:textColor="@color/colorTextPrimarySub"
        android:text="@string/text_press_hint"
        app:drawableTopCompat="@mipmap/icon_fingerprint" />

    <TextView
        android:id="@+id/tv_fingerprint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/common_margin_20dp"
        android:layout_marginRight="@dimen/common_margin_20dp"
        android:textSize="@dimen/text_size_14"
        android:textColor="@color/colorTextPrimarySub"
        android:text="@string/text_fingerprint_prompt"
        android:visibility="gone" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_pass_unlock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/common_margin_30dp"
            android:padding="@dimen/common_padding_5dp"
            android:text="@string/text_pass_unlock"
            android:visibility="gone" />
    </RelativeLayout>
</LinearLayout>
