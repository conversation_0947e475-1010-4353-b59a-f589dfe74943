<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
       android:shape="rectangle"
        >
    <!-- 填充颜色 -->
    <!--<solid android:color="@color/colorPrimary"/>-->
    <solid android:color="@color/gray"/>
    <gradient
            android:angle="180"
            android:endColor="@color/gray"
            android:startColor="@color/gray"/>
    <!-- 矩形的圆角半径 -->
    <corners android:radius="25dp"/>
    <size
            android:height="32dp"
            android:width="30dp"/>
    <stroke
            android:color="@color/colorAccent"
            android:width="0dp"/>
</shape>
