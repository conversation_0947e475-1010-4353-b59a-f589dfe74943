package com.example.wallet_core;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;

import com.coldlar.coin.bch.BitcoinCashAddressFormatter;
import com.coldlar.coin.bitcoin.psbt.BtcImpl;
import com.coldlar.coin.bitcoin.psbt.PsbtHandler;
import com.coldlar.coin.eos.core.HexUtils;
import com.coldlar.coin.eos.core.ec.Sha256;
import com.coldlar.coin.eos.core.ec.SignedTransaction;
import com.coldlar.coin.eos.core.ec.TypeChainId;
import com.coldlar.coin.ethereum.CallTransaction;
import com.coldlar.coin.ethereum.EthereumMessage;
import com.coldlar.coin.ethereum.TransactionType;
import com.coldlar.coin.ethereum.entity.SignMessageType;
import com.coldlar.coin.ethereum.transaction.FunctionUtil;
import com.coldlar.utils.SeedUtil;
import com.coldlar.utils.encrypt.AesUtil;
import com.coldlar.v8.Coinlib;

import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import wallet.core.jni.Hash;

/**
 * WalletCorePlugin
 */
@SuppressWarnings("unchecked")
public class WalletCorePlugin implements FlutterPlugin, MethodCallHandler, MethodCallInterface {

    private MethodChannel channel;

    public static String TAG = "| WalletCorePlugin | Flutter | Android | ";
    Handler handler = new Handler(Looper.getMainLooper());

    {
        System.loadLibrary("TrustWalletCore");

    }

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "wallet_core");
        channel.setMethodCallHandler(this);
        Coinlib.init(flutterPluginBinding.getApplicationContext());
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        if (call.method.equals(MethodCallInterface.getPlatformVersion)) {
            getPlatformVersion(call, result);
        } else if (call.method.equals(MethodCallInterface.createAddress)) {
            createAddress(call, result);
        } else if (call.method.equals(MethodCallInterface.verifyAddress)) {
            verifyAddress(call, result);
        } else if (call.method.equals(MethodCallInterface.getPublicKey)) {
            getPublicKey(call, result);
        } else if (call.method.equals(MethodCallInterface.decrypt)) {
            decrypt(call, result);
        } else if (call.method.equals(MethodCallInterface.getMnemonics)) {
            getMnemonics(call, result);
        } else if (call.method.equals(MethodCallInterface.getPhrases)) {
            getPhrases(call, result);
        } else if (call.method.equals(MethodCallInterface.getEthereumSeriesWaitingSignatureList)) {
            getEthereumSeriesWaitingSignatureList(call, result);
        } else if (call.method.equals(MethodCallInterface.getEthereumSeriesBuildRawtx)) {
            getEthereumSeriesBuildRawtx(call, result);
        } else if (call.method.equals(MethodCallInterface.getBtcWaitingSignatureList)) {
            getBtcWaitingSignatureList(call, result);
        } else if (call.method.equals(MethodCallInterface.getBitcoinSeriesFeeOrBestUtxos)) {
            getBitcoinSeriesFeeOrBestUtxos(call, result);
        } else if (call.method.equals(MethodCallInterface.getBtcBuildRawtx)) {
            getBtcBuildRawtx(call, result);
        } else if (call.method.equals(MethodCallInterface.getTronBuildRawtx)) {
            getTronBuildRawtx(call, result);
        } else if (call.method.equals(MethodCallInterface.verityTronRawDataHex)) {
            verityTronRawDataHex(call, result);
        } else if (call.method.equals(MethodCallInterface.getErc20Data)) {
            getErc20Data(call, result);
        } else if (call.method.equals(MethodCallInterface.trc20Parameter)) {
            trc20Parameter(call, result);
        } else if (call.method.equals(MethodCallInterface.getEntropy)) {
            getEntropy(call, result);
        } else if (call.method.equals(MethodCallInterface.getEosBlockInfo)) {
            getEosBlockInfo(call, result);
        } else if (call.method.equals(MethodCallInterface.isBech32Address)) {
            isBech32Address(call, result);
        } else if (call.method.equals(MethodCallInterface.isTapRootAddress)) {
            isTapRootAddress(call, result);
        } else if (call.method.equals(MethodCallInterface.isLegacyAddress)) {
            isLegacyAddress(call, result);
        } else if (call.method.equals(MethodCallInterface.getSignPersonalMessage)) {
            getSignPersonalMessage(call, result);
        } else if (call.method.equals(MethodCallInterface.parseRawPsbt)) {
            parseRawPsbt(call, result);
        } else if (call.method.equals(MethodCallInterface.isEip1559Transaction)) {
            isEip1559Transaction(call, result);
        } else if (call.method.equals(MethodCallInterface.safeTransferFrom)) {
            safeTransferFrom(call, result);
        } else if (call.method.equals(MethodCallInterface.createAddressOrPublicKeyByBase58)) {
            createAddressOrPublicKeyByBase58(call, result);
        } else if (call.method.equals(MethodCallInterface.parseEthereumTransactionData)) {
            parseEthereumTransactionData(call, result);
        } else if (call.method.equals(MethodCallInterface.parseBitcoinTransactionData)) {
            parseBitcoinTransactionData(call, result);
        } else if (call.method.equals(MethodCallInterface.convertBitcoinCashAddress)) {
            convertBitcoinCashAddress(call, result);
        } else if (call.method.equals(MethodCallInterface.getEosTransactionOrSignHash)) {
            getEosTransactionOrSignHash(call, result);
        } else if (call.method.equals(MethodCallInterface.buildTransactionData)) {
            buildTransactionData(call, result);
        } else if (call.method.equals(MethodCallInterface.splitTransactionSignature)) {
            splitTransactionSignature(call, result);
        } else {
            result.notImplemented();
        }

    }

    public void runMainThread(final Map<String, Object> map, final Result result, final String method) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(() -> {
            if (result == null && method != null) {
                channel.invokeMethod(method, map);
            } else {
                result.success(map);
            }
        });
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    @Override
    public void getPlatformVersion(MethodCall call, Result result) {
        result.success("Android " + android.os.Build.VERSION.RELEASE);
    }

    @Override
    public void createAddress(MethodCall call, Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String value = "";

            try {
                value = CoreCoinBase.getInstance().createAddressOrPublicKey(map, true);
            } catch (Exception ignored) {

            }
            result.success(value);
        });

    }

    @Override
    public void createAddressOrPublicKeyByBase58(MethodCall call, Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String value = "";

            try {
                value = CoreCoinBase.getInstance().createAddressOrPublicKeyByBase58(map);
            } catch (Exception ignored) {

            }
            result.success(value);
        });

    }

    @Override
    public void getPublicKey(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String value = "";

            CoinType coinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
            if (coinType == null) {
                result.success(value);
                return;
            }
            try {
                value = CoreCoinBase.getInstance().createAddressOrPublicKey(map, false);
            } catch (Exception ignored) {

            }
            result.success(value);
        });

    }

    @Override
    public void verifyAddress(MethodCall call, Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success(false);
            return;
        }

        String address = (String) map.get(MethodCallInterface.argAddress);
        if (address == null) {
            result.success(false);
            return;
        }

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            result.success(false);
            return;
        }

        handler.post(() -> {

            CoinType coinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
            if (coinType == null) {
                result.success(false);
                return;
            }
            boolean isVerify = false;
            try {
                isVerify = coinType.validateAddress(address);
            } catch (Exception e) {
                Log.d(WalletCorePlugin.TAG, e.getMessage());

            }
            result.success(isVerify);

        });

    }

    @Override
    public void decrypt(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        String key = (String) map.get(MethodCallInterface.argKey);
        if (key == null) {
            result.success("");
            return;
        }

        String iv = (String) map.get(MethodCallInterface.argIV);
        if (iv == null) {
            result.success("");
            return;
        }

        String data = (String) map.get(MethodCallInterface.argData);
        if (data == null) {
            result.success("");
            return;
        }

        handler.post(() -> {

            result.success(AesUtil.decrypt(key, iv, data));

        });

    }

    @Override
    public void getErc20Data(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String data = null;
            try {
                data = CoreCoinBase.getInstance().getErc20Data(map);
            } catch (Exception ignored) {
            }
            result.success(data);
        });

    }

    @Override
    public void getMnemonics(MethodCall call, MethodChannel.Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        byte[] data = (byte[]) map.get(MethodCallInterface.argData);
        if (data == null) {
            result.success("");
            return;
        }

        int type = (int) map.get(MethodCallInterface.argType);

        handler.post(() -> {
            List<String> mnemonicsList = null;

            try {
                mnemonicsList = SeedUtil.getMnemonic(data, type);
            } catch (Exception e) {
            }
            result.success(mnemonicsList);
        });

    }

    @Override
    public void getPhrases(MethodCall call, MethodChannel.Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        int type = (int) map.get(MethodCallInterface.argType);

        handler.post(() -> {
            List<String> mnemonicsList = null;
            try {
                mnemonicsList = SeedUtil.getPhrases(type);
            } catch (Exception e) {
            }
            result.success(mnemonicsList);
        });

    }

    @Override
    public void getEthereumSeriesWaitingSignatureList(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            byte[] sighHash = null;
            try {
                sighHash = CoreCoinBase.getInstance().getEthereumSignHash(map);
            } catch (Exception e) {
                e.fillInStackTrace();
            }
            result.success(sighHash);
        });
    }

    @Override
    public void getEthereumSeriesBuildRawtx(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String rawTx = null;
            try {
                rawTx = CoreCoinBase.getInstance().getEthereumRawTx(map);
            } catch (Exception e) {
                e.fillInStackTrace();
            }
            result.success(rawTx);
        });
    }

    @Override
    public void getBtcWaitingSignatureList(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            List<String> signData = null;
            try {
                signData = CoreCoinBase.getInstance().getBtcWaitingSignatureList(map, false);
            } catch (Exception e) {
                e.fillInStackTrace();
            }
            result.success(signData);
        });
    }

    @Override
    public void getBtcBuildRawtx(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            String rawTx = null;
            try {
                rawTx = CoreCoinBase.getInstance().getBtcBuildRawTx(map);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(rawTx);
        });
    }

    @Override
    public void getBitcoinSeriesFeeOrBestUtxos(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            boolean success = false;
            String utxoJson = null;
            try {
                utxoJson = CoreCoinBase.getInstance().getBitcoinSeriesFeeOrBestUtxos(map);
                success = true;
            } catch (Exception ignored) {
            } finally {
                result.success(success ? utxoJson : "");

            }

        });
    }

    @Override
    public void getTronBuildRawtx(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String rawTx = "";
            try {
                rawTx = CoreCoinBase.getInstance().getTronBuildRawTx(map);
            } catch (Exception e) {
                e.fillInStackTrace();
            }
            result.success(rawTx);
        });
    }

    @Override
    public void verityTronRawDataHex(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            boolean isVerify = false;
            try {
                isVerify = CoreCoinBase.getInstance().verifyRawDataHex(map);
            } catch (Exception e) {
                e.fillInStackTrace();
            }
            result.success(isVerify);
        });

    }

    @Override
    public void trc20Parameter(MethodCall call, MethodChannel.Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String rawTx = "";
            try {
                rawTx = CoreCoinBase.getInstance().trc20Parameter(map);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(rawTx);
        });

    }

    @Override
    public void getEntropy(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            byte[] seed = null;
            try {
                List<String> mnemonic = (List<String>) map.get(MethodCallInterface.argMnemonics);
                seed = SeedUtil.getseedEntropy(mnemonic);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(seed);
        });
    }

    @Override
    public void isBech32Address(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);

        handler.post(() -> {
            boolean isTapRootAddress = false;
            try {
                isTapRootAddress = BtcImpl.isBech32Address(toAddress);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(isTapRootAddress);
        });
    }

    @Override
    public void isTapRootAddress(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);

        handler.post(() -> {
            boolean isTapRootAddress = false;
            try {
                isTapRootAddress = BtcImpl.isTaprootAddress(toAddress);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(isTapRootAddress);
        });
    }

    @Override
    public void isLegacyAddress(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);

        String chain = (String) map.get(MethodCallInterface.argChain);
        CoinType mCoinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);

        handler.post(() -> {
            boolean isLegacyAddress = false;
            try {
                isLegacyAddress = BtcImpl.isLegacyAddress(mCoinType, toAddress);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(isLegacyAddress);
        });
    }

    @Override
    public void getSignPersonalMessage(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            String waiteHash = null;
            try {
                String message = (String) map.get(MethodCallInterface.argSignMessage);
                EthereumMessage ethereumMessage = new EthereumMessage(message, SignMessageType.SIGN_PERSONAL_MESSAGE);
                byte[] digest = Hash.keccak256(ethereumMessage.getPrehash());
                waiteHash = Numeric.toHexString(digest);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(waiteHash);
        });
    }

    @Override
    public void parseRawPsbt(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            String psbtRawJson = "";
            try {

                String psbtHex = (String) map.get(MethodCallInterface.argPsbtHex);
                boolean isTestNetwork = false;
                if (map.get(MethodCallInterface.argIsTestNetWork) != null) {
                    isTestNetwork = (boolean) map.get(MethodCallInterface.argIsTestNetWork);
                }
                psbtRawJson = PsbtHandler.parseRawPsbtJson(psbtHex, isTestNetwork);

            } catch (Exception e) {

            }
            result.success(psbtRawJson);
        });
    }

    @Override
    public void isEip1559Transaction(MethodCall call, MethodChannel.Result result) {

        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        String payload = (String) map.get(MethodCallInterface.argPayload);

        handler.post(() -> {
            boolean isUse1559Transaction = false;
            try {
                isUse1559Transaction = TransactionType.getTransactionType(payload).isEip1559();
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(isUse1559Transaction);
        });

    }

    @Override
    public void getEosBlockInfo(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            long refBlockPrefix = 0;
            try {
                String blockId = (String) map.get(MethodCallInterface.argBlockInfo);

                refBlockPrefix = new BigInteger(1, HexUtils.toBytesReversed(blockId)).longValue();

            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(String.valueOf(refBlockPrefix));
        });
    }

    @Override
    public void safeTransferFrom(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            String transactionBytes = "";
            try {
                String tokenId = (String) map.get(MethodCallInterface.argTokenId);

                String ercType = (String) map.get(MethodCallInterface.argErcType);

                String from = (String) map.get(MethodCallInterface.argFrom);

                String to = (String) map.get(MethodCallInterface.argTo);

                BigInteger tokenIdBI = new BigInteger(tokenId);
                BigInteger amountBI = new BigInteger("1");

                if (ercType.contains("1155")) {
                    transactionBytes = CallTransaction.getERC1155TransferBytes(from, to, tokenIdBI, amountBI);
                } else {
                    transactionBytes = CallTransaction.getERC721TransferBytes(from, to, "", tokenIdBI);
                }

            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(transactionBytes);
        });
    }

    @Override
    public void parseEthereumTransactionData(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String rawTx = "";
            try {
                String rawData = (String) map.get(MethodCallInterface.argRawData);
                rawTx = CoreCoinBase.getInstance().parseEthereumTransactionData(rawData);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(rawTx);
        });
    }

    @Override
    public void parseBitcoinTransactionData(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }
        handler.post(() -> {
            String rawTx = "";
            try {
                String rawData = (String) map.get(MethodCallInterface.argRawData);
                String chain = (String) map.get(MethodCallInterface.argChain);
                int hasChangeZero = (int) map.get(MethodCallInterface.argHasChangeZero);

                rawTx = CoreCoinBase.getInstance().parseBitcoinTransactionData(rawData, chain, hasChangeZero);
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(WalletCorePlugin.TAG, e.getMessage());

            }
            result.success(rawTx);
        });
    }

    @Override
    public void convertBitcoinCashAddress(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String rawTx = "";
            try {
                String address = (String) map.get(MethodCallInterface.argAddress);
                rawTx = BitcoinCashAddressFormatter.convertAddress(address);
            } catch (Exception e) {
                e.fillInStackTrace();

            }
            result.success(rawTx);
        });
    }

    @Override
    public void getEosTransactionOrSignHash(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        handler.post(() -> {
            String signHash = "";
            try {

                signHash = CoreCoinBase.getInstance().getEosTransactionOrSignHash(map);

            } catch (Exception e) {
                e.printStackTrace();


            }
            result.success(signHash);
        });

    }

    @Override
    public void buildTransactionData(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            result.success("");
            return;
        }

        String json = (String) map.get(MethodCallInterface.argBuildJson);
        if (json == null) {
            result.success("");
            return;
        }

        handler.post(() -> {

            CoinType coinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
            if (coinType == null) {
                result.success("");
                return;
            }
            Log.d(WalletCorePlugin.TAG, coinType.getChain());

            String rawData = "";
            try {
                rawData = coinType.buildTransactionData(json);
            } catch (Exception e) {
                Log.d(WalletCorePlugin.TAG, e.getMessage());

            }
            result.success(rawData);

        });

    }

    @Override
    public void splitTransactionSignature(MethodCall call, MethodChannel.Result result) {
        HashMap<String, Object> map = call.arguments();
        if (map == null) {
            result.success("");
            return;
        }


        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            result.success("");
            return;
        }

        String signatures = (String) map.get(MethodCallInterface.argSignatures);
        if (signatures == null) {
            result.success("");
            return;
        }

        String waitSignature = (String) map.get(MethodCallInterface.argWaitSignature);
        if (waitSignature == null) {
            result.success("");
            return;
        }

        handler.post(() -> {

            CoinType coinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
            if (coinType == null) {
                result.success("");
                return;
            }
            String rawData = "";
            try {
                rawData = coinType.splitTransactionSignature(waitSignature, signatures);
            } catch (Exception e) {
                Log.d(WalletCorePlugin.TAG, e.getMessage());

            }
            result.success(rawData);

        });

    }


}
