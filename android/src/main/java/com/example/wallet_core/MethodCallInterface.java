package com.example.wallet_core;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public interface MethodCallInterface {

    String getPlatformVersion = "getPlatformVersion";
    String createAddress = "createAddress";

    String verifyAddress = "verifyAddress";

    String getPublicKey = "getPublicKey";

    String decrypt = "decrypt";

    String getMnemonics = "getMnemonics";

    String getPhrases = "getPhrases";

    String getEthereumSeriesWaitingSignatureList = "getEthereumSeriesWaitingSignatureList";

    String getEthereumSeriesBuildRawtx = "getEthereumSeriesBuildRawtx";

    String getBtcWaitingSignatureList = "getBtcWaitingSignatureList";

    String getBitcoinSeriesFeeOrBestUtxos = "getBitcoinSeriesFeeOrBestUtxos";

    String getBestUTXO = "getBestUTXO";

    String getBtcBuildRawtx = "getBtcBuildRawtx";

    String getTronBuildRawtx = "getTronBuildRawtx";

    String verityTronRawDataHex = "verityTronRawDataHex";

    String getErc20Data = "getErc20Data";

    String trc20Parameter = "trc20Parameter";

    String getEntropy = "getEntropy";

    String getEosBlockInfo = "eosTransfromBigOrSmall";

    String isTapRootAddress = "isTapRootAddress";

    String isBech32Address = "isBech32Address";

    String isLegacyAddress = "isLegacyAddress";

    String getSignPersonalMessage = "getSignPersonalMessage";

    /// parseRawPsbt
    String parseRawPsbt = "parseRawPsbt";

    String isEip1559Transaction = "isEip1559Transaction";

    String safeTransferFrom = "safeTransferFrom";

    String createAddressOrPublicKeyByBase58 = "createAddressOrPublicKeyByBase58";

    /// parseEthereumTransactionRawData
    String parseEthereumTransactionData = "parseEthereumTransactionData";

    /// parseBitcoinTransactionData
    String parseBitcoinTransactionData = "parseBitcoinTransactionData";

    ///convertBitcoinCashAddress
    String convertBitcoinCashAddress = "convertBitcoinCashAddress";

    ///getEosSignHash
    String getEosTransactionOrSignHash = "getEosTransactionOrSignHash";


    ///buildTransactionData
    String buildTransactionData = "buildTransactionData";

    //splitTransactionSignature
    String splitTransactionSignature = "splitTransactionSignature";


    String argMnemonics = "mnemonics";

    String argAddress = "address";

    String argChain = "chain";

    String argChainBase58 = "chainBase58";

    String argPublicKeyBase58 = "publicKeyBase58";

    String argIsAddress = "isAddress";

    String argPath = "path";

    String argPubData = "pub_data";

    String argBip44Index = "bip44Index";

    String argIsXpub = "is_xpub";

    String argIndex = "index";

    String argKey = "key";

    String argIV = "iv";

    String argData = "data";

    String argTaskId = "task_id";

    String argValue = "value";

    String argInputs = "inputs";

    String argOutputs = "outputs";

    String argVersion = "version";

    String argBranchId = "branch_id";

    String argPreHash = "pre_hash";

    String argPreIndex = "pre_index";

    String argNonce = "nonce";

    String argGasPrice = "gas_price";

    String argGasLimit = "gas_limit";

    String argGasUsed = "gas_used";

    String argTo = "to";

    /// symbol
    String argSymbol = "symbol";

    /// chain_id
    String argChainId = "chain_id";

    /// chain_name
    String argChainName = "chain_name";

    /// coin_symbol
    String argCoinSymbol = "coin_symbol";

    /// contract
    String argContract = "contract";

    /// decimal
    String argDecimal = "decimal";

    /// erc20
    String argErc20 = "erc20";

    /// cmd
    String argCmd = "cmd";

    /// wallet_id
    String argWalletId = "wallet_id";

    /// check
    String argCheck = "check";

    /// total
    String argTotal = "total";

    /// tx_data
    String argTxData = "tx_data";

    /// extra
    String argExtra = "extra";

    /// coin_type
    String argCoinType = "coin_type";

    /// action
    String argAction = "action";

    /// memo
    String argMemo = "memo";

    /// to_address
    String argToAddress = "to_address";

    /// fee
    String argFee = "fee";

    /// to_amount
    String argToAmount = "to_amount";

    /// gas_premium
    String argGasPremium = "gas_premium";

    /// gas_fee_cap
    String argGasFeeCap = "gas_fee_cap";

    /// account_number
    String argAccountNumber = "account_num";

    /// seq
    String argSeq = "seq";

    /// source
    String argSource = "source";

    /// stamp
    String argStamp = "stamp";

    /// dead
    String argDead = "dead";

    /// max_fee_per_gas
    String argMaxFeePerGas = "max_fee_per_gas";

    /// max_priority_fee_per_gas
    String argMaxPriorityFeePerGas = "max_priority_fee_per_gas";

    /// transaction_type
    String argTransactionType = "transaction_type";

    /// max_fee
    String argMaxFee = "max_fee";

    /// max_priority_fee
    String argMaxPriorityFee = "max_priority_fee";

    /// raw_data_hex
    String argRawDataHex = "raw_data_hex";

    /// raw_data
    String argRawData = "raw_data";

    /// transaction
    String argTransaction = "transaction";
    /// payer
    String argPayer = "payer";

    /// action_name
    String argActionName = "action_name";

    /// account
    String argAccount = "account";

    /// amount
    String argAmount = "amount";

    /// receiver
    String argReceiver = "receiver";

    /// block_pre_num
    String argBlockPreNum = "block_pre_num";

    /// block_pre_fix
    String argBlockPreFix = "block_pre_fix";

    /// expiration
    String argExpiration = "expiration";

    /// packed_trx
    String argPackedTrx = "packed_trx";

    /// compression
    String argCompression = "compression";

    /// packed_context_free_data
    String argPackedContextFreeData = "packed_context_free_data";

    /// signatures
    String argSignatures = "signature";

    /// send_address
    String argSendAddress = "send_address";

    /// chain_symbol
    String argChainSymbol = "chain_symbol";

    /// chains
    String argChains = "chains";

    /// device_id
    String argDeviceId = "device_id";

    /// challenge
    String argChallenge = "challenge";

    /// signature
    String argSignature = "signature";

    /// slip44
    String argSlip44 = "slip44";

    /// type
    String argType = "type";

    /// seedType
    String argSeedType = "seedType";

    /// signList
    String argSignList = "signList";

    /// signData
    String argSignData = "signData";

    /// utxoList
    String argUtxoList = "utxoList";

    /// changeAmount
    String argChangeAmount = "change_amount";

    /// fromAddress
    String argFromAddress = "from_address";

    /// xpub
    String argXpub = "xpub";

    /// txId
    String argTxId = "txId";

    /// block_info
    String argBlockInfo = "block_info";

    /// btcSatB
    String argBtcSatB = "btcSatB";

    String argSignMessage = "sign_message";

    // availableBalance
    String argAvailableBalance = "availableBalance";

    // argAddressPublicKey
    String argAddressPublicKey = "addressPublicKey";

    /// psbtHex
    String argPsbtHex = "psbtHex";

    /// isTestNetWork
    String argIsTestNetWork = "isTestNetWork";

    /// erc_type
    String argErcType = "erc_type";

    /// erc_type
    String argTokenId = "token_id";

    String argPayload = "payload";

    /// erc_type
    String argFrom = "from";

   ///permission
    String argPermission ="permission";

    /// hasChangeZero
    String argHasChangeZero = "hasChangeZero";

    String argActor = "actor";

    String argAermission = "permission";

   String argHeadBlockId = "headBlockId";

    String argHeadBlockTime = "headBlockTime";

    String argBlockHeight= "blockHeight";

    String argAccountContract = "accountContract";

    String eosPublicKey = "eosPublicKey";

    String argBuildJson = "buildJson";

    String argWaitSignature = "waitSignature";


    void getPlatformVersion(MethodCall call, MethodChannel.Result result);

    void createAddress(MethodCall call, MethodChannel.Result result);

    void getPublicKey(MethodCall call, MethodChannel.Result result);

    void verifyAddress(MethodCall call, MethodChannel.Result result);

    void decrypt(MethodCall call, MethodChannel.Result result);

    void getErc20Data(MethodCall call, MethodChannel.Result result);

    void trc20Parameter(MethodCall call, MethodChannel.Result result);

    void getEosBlockInfo(MethodCall call, MethodChannel.Result result);

    void getMnemonics(MethodCall call, MethodChannel.Result result);

    void getPhrases(MethodCall call, MethodChannel.Result result);

    void getEthereumSeriesWaitingSignatureList(MethodCall call, MethodChannel.Result result);

    void getEthereumSeriesBuildRawtx(MethodCall call, MethodChannel.Result result);

    void getBtcWaitingSignatureList(MethodCall call, MethodChannel.Result result);

    void getBtcBuildRawtx(MethodCall call, MethodChannel.Result result);

    void getBitcoinSeriesFeeOrBestUtxos(MethodCall call, MethodChannel.Result result);

    void getTronBuildRawtx(MethodCall call, MethodChannel.Result result);

    void verityTronRawDataHex(MethodCall call, MethodChannel.Result result);

    void getEntropy(MethodCall call, MethodChannel.Result result);

    void isBech32Address(MethodCall call, MethodChannel.Result result);

    void isTapRootAddress(MethodCall call, MethodChannel.Result result);

    void isLegacyAddress(MethodCall call, MethodChannel.Result result);

    void getSignPersonalMessage(MethodCall call, MethodChannel.Result result);

    void parseRawPsbt(MethodCall call, MethodChannel.Result result);

    void isEip1559Transaction(MethodCall call, MethodChannel.Result result);

    void safeTransferFrom(MethodCall call, MethodChannel.Result result);

    void createAddressOrPublicKeyByBase58(MethodCall call, MethodChannel.Result result);

    void parseEthereumTransactionData(MethodCall call, MethodChannel.Result result);

    void parseBitcoinTransactionData(MethodCall call, MethodChannel.Result result);

    void convertBitcoinCashAddress(MethodCall call, MethodChannel.Result result);

    void getEosTransactionOrSignHash(MethodCall call, MethodChannel.Result result);

    void buildTransactionData(MethodCall call, MethodChannel.Result result);

    void splitTransactionSignature(MethodCall call, MethodChannel.Result result);


}
