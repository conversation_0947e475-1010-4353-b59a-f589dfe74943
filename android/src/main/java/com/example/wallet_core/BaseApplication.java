package com.example.wallet_core;


import android.app.Application;
import android.content.Context;

import com.coldlar.coin.nem.nac.common.exceptions.NacRuntimeException;


public class BaseApplication extends Application {

    public static BaseApplication instance;

    public static BaseApplication getInstance() {
        return instance;
    }

    private static Context context;

    public static Context getAppContext() {
        if (null == context) {
            throw new NacRuntimeException("App context has not been initialized");
        }
        return context;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        context = getApplication();

    }

    private Application getApplication() {
        return this;
    }


}