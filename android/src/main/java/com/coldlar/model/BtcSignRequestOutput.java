package com.coldlar.model;

import java.io.Serializable;


public class  BtcSignRequestOutput implements Serializable {
    /**
     * 接收金额的地址
     */
    private String to;

    /**
     * 进行转账的金额
     */
    private long amount;

    /**
     * 接收地址的公钥，传入pubkey则说明脚本类型为P2PK。(非压缩格式65长度的公钥)
     */
    private String pubkey;

    /**
     * 输出类型，0正常输出  1 USDT 输出
     */
    private int type;

    /**
     * 输出类型，0普通地址 1，USDT接收地址
     */
    private int addressType;

    public int getAddressType() {
        return addressType;
    }

    public void setAddressType(int addressType) {
        this.addressType = addressType;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }

    public String getPubkey() {
        return pubkey;
    }

    public void setPubkey(String pubkey) {
        this.pubkey = pubkey;
    }




}
