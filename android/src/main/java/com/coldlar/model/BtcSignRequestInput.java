package com.coldlar.model;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018年10月27日17:41:59
 * description BTC系列单签输入
 */
public class BtcSignRequestInput implements Serializable {
    /**
     * Utxo 的 pre tx id
     */
    private String preTx;

    /**
     * Utxo 的 index
     */
    private int preN;

    /**
     * 转账的金额
     */
    private long amount;

    /**
     * 脚本类型，可选参数，默认为P2PKH。如果非P2PKH那么需要传入。
     */
    private String type;


    private String  script;


    private String  rawTx;

    public String getPreTx() {
        return preTx;
    }

    public void setPreTx(String preTx) {
        this.preTx = preTx;
    }

    public int getPreN() {
        return preN;
    }

    public void setPreN(int preN) {
        this.preN = preN;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getRawTx() {
        return rawTx;
    }

    public void setRawTx(String rawTx) {
        this.rawTx = rawTx;
    }


}
