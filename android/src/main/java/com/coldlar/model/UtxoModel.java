package com.coldlar.model;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class UtxoModel implements Serializable{



    @JsonProperty("height")
    private Integer height;
    @JsonProperty("script")
    private String script;
    @JsonProperty("tx_hash")
    private String txHash;
    @JsonProperty("tx_out_index")
    private Integer txOutIndex;
    @JsonProperty("value")
    private long value;



    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public Integer getTxOutIndex() {
        return txOutIndex;
    }

    public void setTxOutIndex(Integer txOutIndex) {
        this.txOutIndex = txOutIndex;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }
}
