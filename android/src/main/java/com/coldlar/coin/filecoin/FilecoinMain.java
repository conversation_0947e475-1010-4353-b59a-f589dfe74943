package com.coldlar.coin.filecoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import org.json.JSONException;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.Map;

public class FilecoinMain extends CoinType {
    public FilecoinMain() {
        addressHeader = 0;
        p2shHeader = 5;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_FIL;
        symbol = "FIL";
        chain ="fil";
        showSymbol= "FIL";
        name = "Filecoin";
        cnName = "Filecoin";
        trName = "Filecoin";
        uriScheme = "filecoin";
        bip44Index = 461;
        unitExponent = 18;

        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("5460");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Filecoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        confirm = 6;
        coinOrder=15;
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("10000000");
        maxSend=new BigDecimal("2000000000");
    }

    private static FilecoinMain instance = new FilecoinMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        boolean isAddress = false;
        try {
            String result = ColdLarLibFilecoin.checkAddress(address);
            JSONObject json = new JSONObject(result);
            isAddress = json.optBoolean("status", false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return isAddress;


    }
    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof FilecoinMain) {
            return true;
        } else {
            return false;
        }
    }


}
