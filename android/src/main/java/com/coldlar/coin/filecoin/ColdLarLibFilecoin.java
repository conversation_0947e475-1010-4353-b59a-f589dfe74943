package com.coldlar.coin.filecoin;

public class ColdLarLibFilecoin {

    static {
        System.loadLibrary("filecoin");
    }

    /**
     * A native method that is implemented by the 'native-lib' native library,
     * which is packaged with this application.
     */
    public static native String stringFromJNI();

    /***********************************************************
     * Function Name :      getLibVersion
     * Date          :      2020.4.2
     * Parameter     :      无
     * Return Code  :       json字符串，status、version
     * Description  :       获取库版本
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getLibVersion();

    /***********************************************************
     * Function Name :      getAddress
     * Date          :      2020.4.2
     * Parameter     :      public_key：地址对应的33字节的压缩公钥的字符串
     *                      is_test_network：是否生成测试地址 true表示生成测试地址， false 表示生成正式地址
     * Return Code  :       json字符串，status、address
     * Description  :       获取FIL的地址
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getAddress(String public_key, boolean is_test_network);

    /***********************************************************
     * Function Name :      getCheckAddress
     * Date          :      2020.4.2
     * Parameter     :      无
     * Return Code  :       json字符串，status
     * Description  :       校验FIL地址
     * Author  :            cuihuan
     ***********************************************************/
    public static native String checkAddress(String address);


}
