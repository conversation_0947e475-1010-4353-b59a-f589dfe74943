package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class BitcoinGoldMain extends CoinType {
    private static BitcoinGoldMain instance = new BitcoinGoldMain();

    private BitcoinGoldMain() {
        id = "btcgold.main";
        FORK_ID = Integer.valueOf(64);
        FORK_IN_USE = Integer.valueOf(79);
        BIP143 = true;
        addressHeader = 38;
        p2shHeader = 23;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_BTG;
        name = "Bitcoin Gold";
        symbol = "BTG";
        chain ="btg";
        showSymbol= "BTG";
        cnName = "比特黄金";
        uriScheme = "bitcoingold";
        isBitCoinFamily = true;
        bip44Index = Integer.valueOf(156);
        unitExponent = Integer.valueOf(8);
        feeValue = new BigDecimal("10000");
        minFeeValue =new BigDecimal("1000"); 
        minNonDust = new BigDecimal("5460");
        softDustLimit =new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = CoinType.toBytes("Bitcoin Gold Signed Message:\n");
        isSupportMsgSign = true;
        confirm=12;
        coinOrder=29f;
    }

    public static CoinType get() {
        synchronized (BitcoinGoldMain.class) {
            try {
                CoinType coinType = instance;
                return coinType;
            } finally {
                Object obj = BitcoinGoldMain.class;
            }
        }
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BitcoinGoldMain){
            return true;
        }else {
            return false;
        }
    }

}