package com.coldlar.coin.bitcoin.psbt;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.bitcoin.psbt.callback.SignPsbtCallBack;
import com.coldlar.coin.bitcoin.psbt.callback.SignPsbtCallBackImpl;
import com.coldlar.coin.bitcoin.psbt.model.Psbt;
import com.coldlar.coin.bitcoin.psbt.model.PsbtRawModel;
import com.coldlar.coin.bitcoin.psbt.model.PsbtTransaction;
import com.coldlar.coin.bitcoinj.wrap.NativeSecp256k1;

import com.coldlar.v8.ScriptLoader;
import com.eclipsesource.v8.V8;
import com.eclipsesource.v8.V8Object;
import com.google.common.primitives.Bytes;
import com.google.gson.Gson;

import org.bitcoinj.core.Sha256Hash;
import org.bitcoinj.core.Utils;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@SuppressWarnings("deprecation")
public class PsbtHandler {

    private V8 runtime = null;
    private SignPsbtCallBack signCallBack = null;

    public PsbtHandler(V8 v8, SignPsbtCallBack signCallBack) {
        this.runtime = v8;
        this.signCallBack = signCallBack;
    }


    private V8Object createSignerProvider(SignPsbtCallBack signCallBack) {
        V8Object signProvider = new V8Object(runtime);
        signProvider.registerJavaMethod(signCallBack, "isXOnlyPoint", "isXOnlyPoint", new Class[]{String.class});
        signProvider.registerJavaMethod(signCallBack, "xOnlyPointAddTweak", "xOnlyPointAddTweak", new Class[]{String.class, String.class});
        signProvider.add("publicKey", signCallBack.getPublicKey() == null ? "" : signCallBack.getPublicKey());
        signProvider.add("network", signCallBack.isTestNetWork() ? "testnet" : "mainnet");
        signProvider.add("tweakPublicKey", signCallBack.getTweakPublicKey() == null ? "" : signCallBack.getTweakPublicKey());
        signProvider.registerJavaMethod(signCallBack, "signSecp256k1", "sign", new Class[]{String.class});
        signProvider.registerJavaMethod(signCallBack, "signSchnorr", "signSchnorr", new Class[]{String.class});
        signProvider.registerJavaMethod(signCallBack, "signSchnorrByScript", "signSchnorrByScript", new Class[]{String.class});
        return signProvider;
    }


    public String toTaprootAddress() {
        V8Object signerProvider = createSignerProvider(signCallBack);
        String address = (String) runtime.executeJSFunction("g_toTaprootAddress", signCallBack.getPublicKey(), signerProvider);
        signerProvider.release();
        return address;
    }


    public PsbtTransaction signPsbt(Psbt psbt) {
        V8Object signerProvider = createSignerProvider(signCallBack);
        String tx = (String) runtime.executeJSFunction("g_signPsbt", new Gson().toJson(psbt), signerProvider);
        signerProvider.release();
        return new Gson().fromJson(tx, PsbtTransaction.class);
    }

    public String signRawPsbt(String psbtHex, String address) {
        V8Object signerProvider = createSignerProvider(signCallBack);
        String tx = (String) runtime.executeJSFunction("g_signRawPsbt", psbtHex, address, signerProvider);
        signerProvider.release();
        return tx;
    }

    public String deocdePsbtToJson(String psbtHex) {
        V8Object signerProvider = createSignerProvider(signCallBack);
        String psbtJson = (String) runtime.executeJSFunction("g_decodePsbt", psbtHex, signerProvider);
        signerProvider.release();
        return psbtJson;
    }

    public String signMessage(String message, String address, String signType) {
        V8Object signerProvider = createSignerProvider(signCallBack);
        String psbtJson = (String) runtime.executeJSFunction("g_coldlar_signMessage", message, address, signType, signerProvider);
        signerProvider.release();
        return psbtJson;
    }


    public static String getTaprootAddress(String pubKeyHex, boolean isTestNet) {
        V8 runtime = ScriptLoader.sInstance.loadByCoinCode("BTC");
        try {
            PsbtHandler btcTaproot = new PsbtHandler(runtime, new SignPsbtCallBackImpl(pubKeyHex, isTestNet));
            return btcTaproot.toTaprootAddress();
        } catch (Exception ignored) {
            return "";
        } finally {
            if (runtime != null) {
                runtime.release(false);
            }
        }
    }

    public static String taggedHashByNonScript(String pubKeyHex) {
        byte[] internalXOnlyPubKey = toXOnly(pubKeyHex);
        byte[] tap = Sha256Hash.hash("TapTweak".getBytes(StandardCharsets.UTF_8));
        byte[] t = Sha256Hash.hash(Bytes.concat(tap, tap, internalXOnlyPubKey));
        return Hex.toHexString(t);
    }

    public static byte[] toXOnly(String pubKeyHex) {
        try {
            byte[] pubKey = Utils.HEX.decode(pubKeyHex);
            if (pubKey.length == 32) {
                return pubKey;
            } else {
                return Arrays.copyOfRange(pubKey, 1, 33);
            }
        } catch (Exception ignored) {
        }
        return new byte[0];
    }



    public static PsbtRawModel parseRawPsbt(String psbtHex, CoinType coinType) throws Exception {

        V8 runtime = ScriptLoader.sInstance.loadByCoinCode("BTC");
        try {
            PsbtHandler psbtHandler = new PsbtHandler(runtime, new SignPsbtCallBackImpl("", coinType.isTestNetWork()));
            String psbtJson = psbtHandler.deocdePsbtToJson(psbtHex);
            return new Gson().fromJson(psbtJson, PsbtRawModel.class);
        } catch (Exception e) {
            throw new Exception("Failed to parse PSBT: " + e.getMessage(), e);
        } finally {
            if (runtime != null) {
                runtime.release(false);
            }
        }

    }

    public static String parseRawPsbtJson(String psbtHex, boolean isTestNetWork) throws Exception {

        V8 runtime = ScriptLoader.sInstance.loadByCoinCode("BTC");
        try {
            PsbtHandler psbtHandler = new PsbtHandler(runtime, new SignPsbtCallBackImpl("", isTestNetWork));
            return  psbtHandler.deocdePsbtToJson(psbtHex);
        } catch (Exception e) {
            throw new Exception("Failed to parse PSBT: " + e.getMessage(), e);
        } finally {
            if (runtime != null) {
                runtime.release(false);
            }
        }

    }

    public static Boolean isXOnlyPoint(String point) {
        try {
            boolean iXOnly = NativeSecp256k1.xOnlyPoint(com.coldlar.utils.Hex.hexStringToByteArray(point));
            return iXOnly;
        } catch (Exception e) {
            return false;
        }

    }

    public static String xOnlyPointAddTweak(String point, String tweak) {

        try {
            Object[] ret = NativeSecp256k1.xOnlyPubkeyTweakAdd(com.coldlar.utils.Hex.hexStringToByteArray(point), com.coldlar.utils.Hex.hexStringToByteArray(tweak));
            return new Gson().toJson(new XOnlyPointAddTweakResult((int) ret[0], (byte[]) ret[1]));
        } catch (Exception e) {
            return null;
        }
    }


}
