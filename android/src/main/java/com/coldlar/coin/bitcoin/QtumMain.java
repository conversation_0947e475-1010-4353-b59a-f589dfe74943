package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class QtumMain extends CoinType {
    private QtumMain() {
        id = "qtum.main";

        addressHeader = 58;
        p2shHeader = 50;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_QTUM;
        name = "Qtum";
        cnName = "量子链";
        trName="量子鏈";
        symbol = "QTUM";
        chain = "qtum";
        showSymbol = "QTUM";
        uriScheme = "qtum";
        bip44Index = 2301;
        unitExponent = 8; //小数位为8为小数
        feeValue = new BigDecimal("500000");//交易的默认手续费
        minNonDust = new BigDecimal("72800"); //比特币的粉尘大小
        softDustLimit = new BigDecimal("72800"); // 软件设置的粉尘大小
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Qtum Signed Message:\n");
        isSupportMsgSign=true;
        isBitCoinFamily = true;

        confirm=12;
        coinOrder=21;

        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
    }

    private static QtumMain instance = new QtumMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof QtumMain){
            return true;
        }else {
            return false;
        }
    }

}
