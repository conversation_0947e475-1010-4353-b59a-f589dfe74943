package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class UsdtMain extends CoinType {
    public static  final String CLOUD_REQUEST_ID="00031";
    public static  final String CLOUD_REQUEST_ID_ERC20="1127";
    private UsdtMain() {
        id = "tether.main";
        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = "31";
        assetID="31";
        name = "Tether USD";
        cnName = "泰达币";
        trName = "泰达幣";
        symbol = "USDT-OMNI";
        chain = "usdt";
        showSymbol = "USDT-OMNI";
        uriScheme = "tether";
        bip44Index = 0;
        unitExponent = 8;
        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit =new BigDecimal("1000");  // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = false;
        isBitCoinFamily = true;

        confirm=1;
        coinOrder=3.0f;
        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
    }

    private static UsdtMain instance = new UsdtMain();

    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof UsdtMain){
            return true;
        }else {
            return false;
        }
    }

}
