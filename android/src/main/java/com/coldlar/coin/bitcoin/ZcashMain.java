package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class ZcashMain extends CoinType {
    private static ZcashMain instance = new ZcashMain();

    private ZcashMain() {
        coinID =ColdlarCoinID.ID_ZCASH;
        cnName = "Zcash";
        trName="Zcash";
        id = "zcash.main";
        family = "zcash";
        addressHeader = 7352;
        p2shHeader = 7357;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        name = "Zcash";
        showSymbol = "ZEC";
        symbol = "ZEC";
        chain = "zec";
        uriScheme = "zcash";
        bip44Index = Integer.valueOf(133);
        unitExponent = Integer.valueOf(8);
        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("54");
        softDustLimit =new BigDecimal("54");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Zcash Signed Message:\n");
        isSupportMsgSign = true;
        confirm=12;
        coinOrder=18;
        isBitCoinFamily = true;

        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
    }
    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof ZcashMain){
            return true;
        }else {
            return false;
        }
    }

}
