package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class BitcoinGodMain extends CoinType {
    private static BitcoinGodMain instance = new BitcoinGodMain();

    private BitcoinGodMain() {
        id = "bitcoingod.main";
        FORK_ID = Integer.valueOf(8);
        FORK_IN_USE = Integer.valueOf(107);
        addressHeader = 97;
        p2shHeader = 23;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_GOD;
        name = "Bitcoin God";
        symbol = "GOD";
        chain ="god";
        showSymbol= "GOD";
        cnName = "比特上帝";
        uriScheme = "bitcoingod";
        isBitCoinFamily = true;
        bip44Index = Integer.valueOf(9999);
        unitExponent = Integer.valueOf(8);
        feeValue =new BigDecimal("1000");
        minFeeValue =new BigDecimal("1000"); 
        minNonDust = new BigDecimal("5460");
        softDustLimit = new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = CoinType.toBytes("Bitcoin Gold Signed Message:\n");
        isSupportMsgSign = true;
        confirm=12;
        coinOrder=29.3f;
    }

    public static CoinType get() {
        synchronized (BitcoinGodMain.class) {
            try {
                CoinType coinType = instance;
                return coinType;
            } finally {
                Object obj = BitcoinGodMain.class;
            }
        }
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }



    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BitcoinGodMain){
            return true;
        }else {
            return false;
        }
    }

}