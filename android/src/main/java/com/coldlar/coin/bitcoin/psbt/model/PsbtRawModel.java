package com.coldlar.coin.bitcoin.psbt.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;



@JsonIgnoreProperties(ignoreUnknown = true)
public class PsbtRawModel {


    @JsonProperty("psbt")
    private PsbtDTO psbt;
    @JsonProperty("txInputs")
    private List<TxInputsDTO> txInputs;
    @JsonProperty("txOutputs")
    private List<TxOutputsDTO> txOutputs;
    @JsonProperty("vInput")
    private List<VInputDTO> vInput;
    @JsonProperty("vOutput")
    private List<VOutputDTO> vOutput;

    public PsbtDTO getPsbt() {
        return psbt;
    }

    public void setPsbt(PsbtDTO psbt) {
        this.psbt = psbt;
    }

    public List<TxInputsDTO> getTxInputs() {
        return txInputs;
    }

    public void setTxInputs(List<TxInputsDTO> txInputs) {
        this.txInputs = txInputs;
    }

    public List<TxOutputsDTO> getTxOutputs() {
        return txOutputs;
    }

    public void setTxOutputs(List<TxOutputsDTO> txOutputs) {
        this.txOutputs = txOutputs;
    }

    public List<VInputDTO> getVInput() {
        return vInput;
    }

    public void setVInput(List<VInputDTO> vInput) {
        this.vInput = vInput;
    }

    public List<VOutputDTO> getVOutput() {
        return vOutput;
    }

    public void setVOutput(List<VOutputDTO> vOutput) {
        this.vOutput = vOutput;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PsbtDTO {
        @JsonProperty("data")
        private DataDTO data;

        public DataDTO getData() {
            return data;
        }

        public void setData(DataDTO data) {
            this.data = data;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class DataDTO {
            @JsonProperty("globalMap")
            private GlobalMapDTO globalMap;
            @JsonProperty("inputs")
            private List<InputsDTO> inputs;
            @JsonProperty("outputs")
            private List<OutputsDTO> outputs;

            public GlobalMapDTO getGlobalMap() {
                return globalMap;
            }

            public void setGlobalMap(GlobalMapDTO globalMap) {
                this.globalMap = globalMap;
            }

            public List<InputsDTO> getInputs() {
                return inputs;
            }

            public void setInputs(List<InputsDTO> inputs) {
                this.inputs = inputs;
            }

            public List<OutputsDTO> getOutputs() {
                return outputs;
            }

            public void setOutputs(List<OutputsDTO> outputs) {
                this.outputs = outputs;
            }

            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class GlobalMapDTO {
                @JsonProperty("unsignedTx")
                private UnsignedTxDTO unsignedTx;

                public UnsignedTxDTO getUnsignedTx() {
                    return unsignedTx;
                }

                public void setUnsignedTx(UnsignedTxDTO unsignedTx) {
                    this.unsignedTx = unsignedTx;
                }

                @JsonIgnoreProperties(ignoreUnknown = true)
                public static class UnsignedTxDTO {
                }
            }

            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class InputsDTO {
                @JsonProperty("witnessUtxo")
                private WitnessUtxoDTO witnessUtxo;
                @JsonProperty("tapInternalKey")
                private TapInternalKeyDTO tapInternalKey;
                @JsonProperty("tapLeafScript")
                private List<TapLeafScriptDTO> tapLeafScript;

                public WitnessUtxoDTO getWitnessUtxo() {
                    return witnessUtxo;
                }

                public void setWitnessUtxo(WitnessUtxoDTO witnessUtxo) {
                    this.witnessUtxo = witnessUtxo;
                }

                public TapInternalKeyDTO getTapInternalKey() {
                    return tapInternalKey;
                }

                public void setTapInternalKey(TapInternalKeyDTO tapInternalKey) {
                    this.tapInternalKey = tapInternalKey;
                }

                public List<TapLeafScriptDTO> getTapLeafScript() {
                    return tapLeafScript;
                }

                public void setTapLeafScript(List<TapLeafScriptDTO> tapLeafScript) {
                    this.tapLeafScript = tapLeafScript;
                }

                @JsonIgnoreProperties(ignoreUnknown = true)
                public static class WitnessUtxoDTO {
                    @JsonProperty("script")
                    private ScriptDTO script;
                    @JsonProperty("value")
                    private Integer value;

                    public ScriptDTO getScript() {
                        return script;
                    }

                    public void setScript(ScriptDTO script) {
                        this.script = script;
                    }

                    public Integer getValue() {
                        return value;
                    }

                    public void setValue(Integer value) {
                        this.value = value;
                    }

                    @JsonIgnoreProperties(ignoreUnknown = true)
                    public static class ScriptDTO {
                        @JsonProperty("type")
                        private String type;
                        @JsonProperty("data")
                        private List<Integer> data;

                        public String getType() {
                            return type;
                        }

                        public void setType(String type) {
                            this.type = type;
                        }

                        public List<Integer> getData() {
                            return data;
                        }

                        public void setData(List<Integer> data) {
                            this.data = data;
                        }
                    }
                }

                @JsonIgnoreProperties(ignoreUnknown = true)
                public static class TapInternalKeyDTO {
                    @JsonProperty("type")
                    private String type;
                    @JsonProperty("data")
                    private List<Integer> data;

                    public String getType() {
                        return type;
                    }

                    public void setType(String type) {
                        this.type = type;
                    }

                    public List<Integer> getData() {
                        return data;
                    }

                    public void setData(List<Integer> data) {
                        this.data = data;
                    }
                }

                @JsonIgnoreProperties(ignoreUnknown = true)
                public static class TapLeafScriptDTO {
                    @JsonProperty("controlBlock")
                    private ControlBlockDTO controlBlock;
                    @JsonProperty("script")
                    private ScriptDTOX script;
                    @JsonProperty("leafVersion")
                    private Integer leafVersion;

                    public ControlBlockDTO getControlBlock() {
                        return controlBlock;
                    }

                    public void setControlBlock(ControlBlockDTO controlBlock) {
                        this.controlBlock = controlBlock;
                    }

                    public ScriptDTOX getScript() {
                        return script;
                    }

                    public void setScript(ScriptDTOX script) {
                        this.script = script;
                    }

                    public Integer getLeafVersion() {
                        return leafVersion;
                    }

                    public void setLeafVersion(Integer leafVersion) {
                        this.leafVersion = leafVersion;
                    }

                    @JsonIgnoreProperties(ignoreUnknown = true)
                    public static class ControlBlockDTO {
                        @JsonProperty("type")
                        private String type;
                        @JsonProperty("data")
                        private List<Integer> data;

                        public String getType() {
                            return type;
                        }

                        public void setType(String type) {
                            this.type = type;
                        }

                        public List<Integer> getData() {
                            return data;
                        }

                        public void setData(List<Integer> data) {
                            this.data = data;
                        }
                    }

                    @JsonIgnoreProperties(ignoreUnknown = true)
                    public static class ScriptDTOX {
                        @JsonProperty("type")
                        private String type;
                        @JsonProperty("data")
                        private List<Integer> data;

                        public String getType() {
                            return type;
                        }

                        public void setType(String type) {
                            this.type = type;
                        }

                        public List<Integer> getData() {
                            return data;
                        }

                        public void setData(List<Integer> data) {
                            this.data = data;
                        }
                    }
                }
            }

            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class OutputsDTO {
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TxInputsDTO {
        @JsonProperty("hash")
        private HashDTO hash;
        @JsonProperty("index")
        private Integer index;
        @JsonProperty("sequence")
        private Long sequence;

        public HashDTO getHash() {
            return hash;
        }

        public void setHash(HashDTO hash) {
            this.hash = hash;
        }

        public Integer getIndex() {
            return index;
        }

        public void setIndex(Integer index) {
            this.index = index;
        }

        public Long getSequence() {
            return sequence;
        }

        public void setSequence(Long sequence) {
            this.sequence = sequence;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class HashDTO {
            @JsonProperty("type")
            private String type;
            @JsonProperty("data")
            private List<Integer> data;

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public List<Integer> getData() {
                return data;
            }

            public void setData(List<Integer> data) {
                this.data = data;
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TxOutputsDTO {
        @JsonProperty("script")
        private ScriptDTOXX script;
        @JsonProperty("value")
        private Integer value;
        @JsonProperty("address")
        private String address;

        public ScriptDTOXX getScript() {
            return script;
        }

        public void setScript(ScriptDTOXX script) {
            this.script = script;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ScriptDTOXX {
            @JsonProperty("type")
            private String type;
            @JsonProperty("data")
            private List<Integer> data;

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public List<Integer> getData() {
                return data;
            }

            public void setData(List<Integer> data) {
                this.data = data;
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VInputDTO {
        @JsonProperty("address")
        private String address;
        @JsonProperty("value")
        private Integer value;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VOutputDTO {
        @JsonProperty("address")
        private String address;
        @JsonProperty("value")
        private Integer value;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }
    }
}