package com.coldlar.coin.bitcoin.psbt.model;

import java.math.BigDecimal;


public  class Output {
    public String address;
    public BigDecimal value;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}