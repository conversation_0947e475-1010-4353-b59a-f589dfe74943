package com.coldlar.coin.bitcoin.engine;


import android.util.Log;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.bitcoin.BitcoinMain;
import com.coldlar.core.util.CoinUtil;
import com.coldlar.model.BaseSendRequest;
import com.coldlar.model.BtcSignRequestInput;
import com.coldlar.model.BtcSignRequestOutput;
import com.coldlar.model.UtxoModel;
import com.coldlar.utils.BigDecimalUtils;
import com.example.wallet_core.WalletCorePlugin;

import org.bitcoinj.core.Coin;
import org.bitcoinj.core.InsufficientMoneyException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
@SuppressWarnings("unchecked")
public class BtcTxEngine {


    public static BaseSendRequest getBitTxSignMsg(CoinType coinType, String fromAddress, BigDecimal totalValue, BigDecimal targetValue, BigDecimal feeValue, List<BtcSignRequestOutput> outList, List<UtxoModel> totalAllUtxoList) {
        BaseSendRequest mSignRequestModel = new BaseSendRequest();
        List<UtxoModel> useUtxoList;

        List<UtxoModel> totalUtxoList = new ArrayList<>();
        if (coinType.isBitcoinNetWork) {
            for (UtxoModel utxoModel : totalAllUtxoList) {
                long value = utxoModel.getValue();
                if (BigDecimalUtils.compare(value + "", "546")) {
                    totalUtxoList.add(utxoModel);
                }
            }
        } else {
            totalUtxoList = totalAllUtxoList;
        }

        totalUtxoList = sortList(totalUtxoList);
        BtcSignRequestOutput changeModel = null;
        //全部发送
        if (totalValue.compareTo(targetValue) == 0) {
            int lastCalculatedSizeInt = 148 * totalUtxoList.size() + 34 * totalUtxoList.size() + 10;
            BigDecimal lastCalculatedSize = new BigDecimal(lastCalculatedSizeInt + "");
            BigDecimal fees = new BigDecimal("0");
            BigDecimal feePerTxSize = new BigDecimal(feeValue + "");
            //比特币的手续费始终按照真实值来计算
            if (coinType.isBitcoinNetWork) {
                fees = fees.add(feePerTxSize.multiply(lastCalculatedSize).divide(new BigDecimal("1000")));
            } else {
                BigDecimal realkV1 = lastCalculatedSize.divide(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                BigDecimal realkbStr = realkV1.add(new BigDecimal("1"));
                BigDecimal realFeeValue = feeValue.multiply(realkbStr);
                fees = fees.add(realFeeValue);
            }
            BigDecimal sendValue = totalValue.subtract(fees);
            if (totalValue.compareTo(fees) < 0) {
                try {
                    throw new InsufficientMoneyException(Coin.ZERO);
                } catch (InsufficientMoneyException e) {
                    e.fillInStackTrace();
                    return null;
                }
            }

            outList.get(0).setAmount(sendValue.longValue());
            mSignRequestModel.setFee(CoinUtil.getCoinValueSatoshiToBtc(fees.longValue(), coinType.getUnitExponent()).toPlainString());
            mSignRequestModel.setIn(getInput(totalUtxoList));
            mSignRequestModel.setOut(outList);

        } else {
            Log.d(WalletCorePlugin.TAG,"76");

            BigDecimal totaluse = new BigDecimal("0");
            BigDecimal change = new BigDecimal("0");
            BigDecimal valueNeed = new BigDecimal("0");
            BigDecimal totavalue = new BigDecimal("0");
            BigDecimal lastCalculatedSize = new BigDecimal("0");
            if (totalUtxoList != null && totalUtxoList.size() > 0) {
                for (int i = 0; i < totalUtxoList.size(); i++) {
                    totavalue = totavalue.add(new BigDecimal(totalUtxoList.get(i).getValue()));
                }
            }

            while (true) {
                useUtxoList = new ArrayList<>();
                BigDecimal fees = new BigDecimal("0");
                //比特币的手续费始终按照真实值来计算，其他币种不足1KB按照1KB计算
                if (coinType.isBitcoinNetWork) {
                    fees = fees.add(feeValue.multiply(lastCalculatedSize).divide(new BigDecimal("1000")));
                } else {
                    BigDecimal realkV1 = lastCalculatedSize.divide(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                    BigDecimal realkbStr = realkV1.add(new BigDecimal("1"));
                    BigDecimal realFeeValue = feeValue.multiply(realkbStr);
                    fees = fees.add(realFeeValue);
                }
                valueNeed = targetValue.add(fees);
                if (totavalue.compareTo(valueNeed) < 0) {
                    try {
                        throw new InsufficientMoneyException(Coin.ZERO);
                    } catch (InsufficientMoneyException e) {
                        e.printStackTrace();
                        return null;
                    }
                }

                totaluse = chooseBestUtxo(totalUtxoList, valueNeed, totaluse, useUtxoList);
                change = totaluse.subtract(valueNeed);
                if (change.compareTo(coinType.getSoftDustLimit()) > 0) {
                    changeModel = new BtcSignRequestOutput();
                    changeModel.setAmount(change.longValue());
                    changeModel.setTo(fromAddress);
                } else {
                    fees = fees.add(change);
                }
                int size = 148 * useUtxoList.size() + 34 * (outList.size() + 1) + 10;
                //比特币更具size的大小出入来设置手续费
                if (coinType.isBitcoinNetWork) {
                    //如果是比特币，且第一次构建，则计算估计需要多少手续费
                    if ((size > lastCalculatedSize.add(new BigDecimal("100")).doubleValue())) {
                        //多加一个out，为了添加找零的数据大小
                        lastCalculatedSize = new BigDecimal(size + "");
                        changeModel = null;
                        totaluse = new BigDecimal("0");
                        continue;
                    }
                } else {// 其他币种根据/kb 跳动来进行设置手续费
                    if (size > lastCalculatedSize.doubleValue()) {
                        //多加一个out，为了添加找零的数据大小
                        lastCalculatedSize = new BigDecimal(size + "");
                        changeModel = null;
                        totaluse = new BigDecimal("0");
                        continue;
                    }
                }
                if (changeModel != null) {
                    outList.add(changeModel);
                }
                mSignRequestModel.setFee(CoinUtil.getCoinValueSatoshiToBtc(fees.longValue(), coinType.getUnitExponent()) + "");
                break;
            }


            mSignRequestModel.setIn(getInput(useUtxoList));
            mSignRequestModel.setOut(outList);

        }
        return mSignRequestModel;
    }


    /**
     * @param utxoInfos
     * @param target    目标金额
     * @param total     useTotal
     * @param useInfo
     * @return
     */
    public static BigDecimal chooseBestUtxo(List<UtxoModel> utxoInfos, BigDecimal target, BigDecimal total, List<UtxoModel> useInfo) {
        List<UtxoModel> newUtxoInfos = new ArrayList<>();
        for (int i = 0; i < utxoInfos.size(); i++) {
            UtxoModel input = utxoInfos.get(i);
            BigDecimal itemValue = new BigDecimal(input.getValue() + "");
            if (itemValue.compareTo(target) >= 0) {
                useInfo.add(input);
                total = total.add(itemValue);
                break;
            } else {
                if (i == utxoInfos.size() - 1) {//此处说明，最大的UTXO 不足以支付该笔交易，所有要重新在剩下的中继续查询
                    useInfo.add(input);
                    total = total.add(itemValue);
                    target = target.subtract(itemValue);//目标值去掉符合的值
                    total = chooseBestUtxo(newUtxoInfos, target, total, useInfo);
                } else {
                    newUtxoInfos.add(utxoInfos.get(i));
                }
            }
        }
        return total;
    }

    /**
     * UTXO排序
     *
     * @param list
     * @return
     */
    public static List<UtxoModel> sortList(List<UtxoModel> list) {
        Collections.sort(list, new Comparator<UtxoModel>() {
            @Override
            public int compare(UtxoModel a, UtxoModel b) {
                long aValue = a.getValue();
                long bValue = b.getValue();
                if (aValue > bValue) {
                    return 1;
                } else if (aValue == bValue) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
        return list;
    }


    /**
     * 转为Basic签名输入格式
     *
     * @param utxoInfos
     * @return
     */
    private static List<BtcSignRequestInput> getInput(List<UtxoModel> utxoInfos) {
        List<BtcSignRequestInput> inputList = new ArrayList<>();
        if (utxoInfos != null) {
            for (int i = 0; i < utxoInfos.size(); i++) {
                UtxoModel utxoModel = utxoInfos.get(i);
                BtcSignRequestInput btcSignRequestInput = new BtcSignRequestInput();
                btcSignRequestInput.setAmount(utxoModel.getValue());
                btcSignRequestInput.setPreN(utxoModel.getTxOutIndex());
                btcSignRequestInput.setPreTx(utxoModel.getTxHash());
                btcSignRequestInput.setScript(utxoModel.getScript());
                inputList.add(btcSignRequestInput);
            }
        }
        return inputList;
    }

    /**
     * 获取比特币需要交易需要的手续费
     *
     * @param addressModel 地址
     * @param targetValue  交易金额
     * @param feeValue     每KB手续费
     * @return
     */
        public static long getBitTxFee(CoinType coinType, String fromAddress, BigDecimal totalValue, BigDecimal targetValue, BigDecimal feeValue, List<BtcSignRequestOutput> outList, List<UtxoModel> totalUtxoList) {

        //需要的手续费
        long feeNeed = 0;
        List<UtxoModel> useUtxoList;
        //UTXO排序
        totalUtxoList = sortList(totalUtxoList);
        BtcSignRequestOutput changeModel = null;
        //全部发送
        if (totalValue.compareTo(targetValue) == 0) {
            int lastCalculatedSizeInt = 148 * totalUtxoList.size() + 34 * totalUtxoList.size() + 10;
            BigDecimal lastCalculatedSize = new BigDecimal(lastCalculatedSizeInt + "");
            BigDecimal fees = new BigDecimal("0");
            //比特币的手续费始终按照真实值来计算
            if (coinType.isBitcoinNetWork) {
                fees = fees.add(feeValue.multiply(lastCalculatedSize).divide(new BigDecimal("1000")));
            } else {
                BigDecimal realkV1 = lastCalculatedSize.divide(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                BigDecimal realkbStr = realkV1.add(new BigDecimal("1"));
                BigDecimal realFeeValue = feeValue.multiply(realkbStr);
                fees = fees.add(realFeeValue);
            }
            BigDecimal sendValue = totalValue.subtract(fees);
            if (totalValue.compareTo(fees) < 0) {
                try {
                    throw new InsufficientMoneyException(Coin.ZERO);
                } catch (InsufficientMoneyException e) {
                    e.printStackTrace();
                    return 0;
                }
            }

            outList.get(0).setAmount(sendValue.longValue());

            feeNeed = fees.longValue();

        } else {
            BigDecimal totaluse = new BigDecimal("0");
            BigDecimal change = new BigDecimal("0");//找零
            BigDecimal valueNeed = new BigDecimal("0");
            BigDecimal totavalue = new BigDecimal("0");
            //计算数据的大小
            BigDecimal lastCalculatedSize = new BigDecimal("0");
            if (totalUtxoList != null && totalUtxoList.size() > 0) {
                for (int i = 0; i < totalUtxoList.size(); i++) {
                    totavalue = totavalue.add(new BigDecimal(totalUtxoList.get(i).getValue()));
                }
            }

            while (true) {
                useUtxoList = new ArrayList<>();
                BigDecimal fees = new BigDecimal("0");
                //比特币的手续费始终按照真实值来计算
                if (coinType.isBitcoinNetWork) {
                    fees = fees.add(feeValue.multiply(lastCalculatedSize).divide(new BigDecimal("1000")));
                } else {
                    BigDecimal realkV1 = lastCalculatedSize.divide(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                    BigDecimal realkbStr = realkV1.add(new BigDecimal("1"));
                    BigDecimal realFeeValue = feeValue.multiply(realkbStr);
                    fees = fees.add(realFeeValue);
                }
                valueNeed = targetValue.add(fees);
                if (totavalue.compareTo(valueNeed) < 0) {
                    try {
                        throw new InsufficientMoneyException(Coin.ZERO);
                    } catch (InsufficientMoneyException e) {
                        e.printStackTrace();
                        return 0;
                    }
                }
                totaluse = chooseBestUtxo(totalUtxoList, valueNeed, totaluse, useUtxoList);
                change = totaluse.subtract(valueNeed);
                if (change.compareTo(coinType.getSoftDustLimit()) > 0) {
                    changeModel = new BtcSignRequestOutput();
                    changeModel.setAmount(change.longValue());
                    changeModel.setTo(fromAddress);
                } else {
                    fees = fees.add(change);
                }
                // TODO: 2019/7/23  这里应该是outList.size()+1,现在是因为只有单个地址,所以写死为1 后期改为多地址的话需要修改
                int size = 148 * useUtxoList.size() + 34 * (outList.size() + 1) + 10;
                //比特币更具size的大小出入来设置手续费
                if (coinType.isBitcoinNetWork) {
                    //如果是比特币，且第一次构建，则计算估计需要多少手续费
                    if ((size > lastCalculatedSize.add(new BigDecimal("100")).doubleValue())) {
                        //多加一个out，为了添加找零的数据大小
                        lastCalculatedSize = new BigDecimal(size + "");
                        changeModel = null;
                        totaluse = new BigDecimal("0");
                        continue;
                    }
                } else {// 其他币种根据/kb 跳动来进行设置手续费//多加一个out，为了添加找零的数据大小
                    if (size > lastCalculatedSize.doubleValue()) {
                        lastCalculatedSize = new BigDecimal(size + "");
                        changeModel = null;
                        totaluse = new BigDecimal("0");
                        continue;
                    }
                }
                if (changeModel != null) {
                    outList.add(changeModel);
                }
                feeNeed = fees.longValue();

                break;
            }


        }
        return feeNeed;
    }


}