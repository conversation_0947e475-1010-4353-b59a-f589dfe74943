package com.coldlar.coin.bitcoin.psbt;


public  class XOnlyPointAddTweakResult {
    public int parity;
    public byte[] xOnlyPubkey;

    public XOnlyPointAddTweakResult(int parity, byte[] xOnlyPubkey) {
        this.parity = parity;
        this.xOnlyPubkey = xOnlyPubkey;
    }

    public int getParity() {
        return parity;
    }

    public void setParity(int parity) {
        this.parity = parity;
    }

    public byte[] getxOnlyPubkey() {
        return xOnlyPubkey;
    }

    public void setxOnlyPubkey(byte[] xOnlyPubkey) {
        this.xOnlyPubkey = xOnlyPubkey;
    }
}
