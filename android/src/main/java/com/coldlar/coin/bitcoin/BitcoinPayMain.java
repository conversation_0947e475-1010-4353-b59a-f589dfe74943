package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class BitcoinPayMain extends CoinType {
    private static BitcoinPayMain instance = new BitcoinPayMain();

    private BitcoinPayMain() {
        id = "bitcoinpay.main";
        FORK_ID = Integer.valueOf(64);
        FORK_IN_USE = Integer.valueOf(0x50);
        BIP143 = true;
        addressHeader = 56;
        p2shHeader = 23;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_BTP;
        name = "Bitcoin Pay";
        symbol = "BTP";
        chain ="btp";
        showSymbol= "BTP";
        cnName = "比特支付";
        uriScheme = "bitconpay";
        bip44Index = 8999;
        unitExponent = 7;
        isBitCoinFamily = true;
        feeValue = new BigDecimal("10000");
        minFeeValue =new BigDecimal("1000"); 
        minNonDust = new BigDecimal("5460");
        softDustLimit = new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = CoinType.toBytes("Bitcoin Pay Signed Message:\n");
        isSupportMsgSign = true;
        confirm=12;
    }

    public static CoinType get() {
        synchronized (BitcoinPayMain.class) {
            return instance;
        }
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }



    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BitcoinPayMain){
            return true;
        }else {
            return false;
        }
    }

}