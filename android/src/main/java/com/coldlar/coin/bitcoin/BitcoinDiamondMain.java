package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class BitcoinDiamondMain extends CoinType {
    private BitcoinDiamondMain() {
        id = "bitcoinDiamond.main";
        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_BCD;
        cnName = "比特钻石";
        trName = "比特鑽石";
        name = "Bitcoin Diamond";
        symbol = "BCD";
        chain = "bcd";
        showSymbol = "BCD";
        uriScheme = "BitcoinDiamond";
        bip44Index = 999;
        unitExponent = 7;
        isBitCoinFamily = true;
        feeValue = new BigDecimal("10000");
        minNonDust = new BigDecimal("5460");
        softDustLimit = new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Diamond Signed Message:\n");
        isSupportMsgSign = true;
        confirm = 12;
        coinOrder = 29.2f;
    }

    private static BitcoinDiamondMain instance = new BitcoinDiamondMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address, this);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof BitcoinDiamondMain) {
            return true;
        } else {
            return false;
        }
    }

}
