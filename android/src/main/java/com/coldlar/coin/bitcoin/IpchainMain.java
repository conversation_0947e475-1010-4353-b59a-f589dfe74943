package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by Administrator on 2017/8/16 0016.
 */

public class IpchainMain extends CoinType {
    private IpchainMain() {
        id = "ipchain.main";
        addressHeader = 269140;
        spendableCoinbaseDepth = 100;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_IPCHAIN;
        name = "IPChain";
        chain ="ipc";
        symbol = "IPC";
        showSymbol = "IPC";
        cnName = "IPChain";
        uriScheme = "ipchain";
        bip44Index = 272;
        unitExponent = 8;
        feeValue = new BigDecimal("100000");
        minFeeValue = new BigDecimal("100000");
        minNonDust =new BigDecimal("5460");
        softDustLimit =new BigDecimal("100000");
        softDustPolicy = SoftDustPolicy.NO_POLICY;
        isBitCoinFamily = true;

        signedMessageHeader = toBytes("Ipchain Signed Message:\n");
        confirm=20;
        coinOrder=22;
    }

    private static IpchainMain instance = new IpchainMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof IpchainMain){
            return true;
        }else {
            return false;
        }
    }

}
