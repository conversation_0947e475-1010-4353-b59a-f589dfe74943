package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class SuperBitcoinMain extends CoinType {
    private SuperBitcoinMain() {
        id = "superbitcoin.main";
        FORK_ID = Integer.valueOf(64);
        FORK_IN_USE = Integer.valueOf(0);
        FORK_TAG = "sbtc";
        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        isBitCoinFamily = true;

        coinID = ColdlarCoinID.ID_SBTC;
        cnName = "超级比特币";
        name = "Super Bitcoin";
        symbol = "SBTC";
        chain = "sbtc";
        showSymbol = "SBTC";
        uriScheme = "superbitcoin";
        bip44Index = 8888;
        unitExponent = 8;
        feeValue = new BigDecimal("10000");
        minFeeValue = new BigDecimal("1000");
        minNonDust =new BigDecimal("5460"); 
        softDustLimit =new BigDecimal("1000000");  // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = true;
        confirm=12;
        coinOrder=29.1f;
    }

    private static SuperBitcoinMain instance = new SuperBitcoinMain();

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    private static byte[] getSignMessageHeader() {
        byte[] data1 = hexStr2Bytes("18");
        byte[] data2 = toBytes("Bitcoin Signed Message:\n");
        byte[] data3 = new byte[data1.length + data2.length];
        System.arraycopy(data1, 0, data3, 0, data1.length);
        System.arraycopy(data2, 0, data3, data1.length, data2.length);
        return data3;
    }

    public static byte[] hexStr2Bytes(String src) {
        String[] src_sp = src.split(" ");
        byte[] ret = new byte[src_sp.length];
        for (int i = 0; i < src_sp.length; i++) {
            ret[i] = Byte.decode("0x" + src_sp[i]);
        }
        return ret;
    }

    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof SuperBitcoinMain){
            return true;
        }else {
            return false;
        }
    }

}

