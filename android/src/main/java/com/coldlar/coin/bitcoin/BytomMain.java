package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class BytomMain extends CoinType {
    private BytomMain() {
        id = "bytom.main";

        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_BTM;
        name = "Bytom";
        cnName = "比原链";
        trName = "比原链";
        symbol = "BTM";
        chain ="btm";
        showSymbol= "BTM";
        uriScheme = "bytom";
        bip44Index = 153;
        unitExponent = 8;
        feeValue = new BigDecimal("5000000");

        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee =new BigDecimal("1000"); 
        softDustLimit = new BigDecimal("1"); // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        isBitCoinFamily = true;

        confirm=12;
        coinOrder=24;
    }

    private static BytomMain instance = new BytomMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BytomMain){
            return true;
        }else {
            return false;
        }
    }

}
