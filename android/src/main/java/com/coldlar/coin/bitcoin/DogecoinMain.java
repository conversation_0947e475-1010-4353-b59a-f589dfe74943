package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * +
 */
public class DogecoinMain extends CoinType implements Serializable {
    private DogecoinMain() {
        id = "dogecoin.main";

        addressHeader = 30;
        p2shHeader = 22;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 240; // COINBASE_MATURITY_NEW
        dumpedPrivateKeyHeader = 158;


        coinID = ColdlarCoinID.ID_DOGE;
        cnName = "狗狗币";
        trName = "狗狗幣";
        name = "Dogecoin";
        symbol = "DOGE";
        chain ="doge";
        showSymbol= "DOGE";
        uriScheme = "dogecoin";
        bip44Index = 3;
        unitExponent = 8;
        feeValue =new BigDecimal("100000000");
        minNonDust = new BigDecimal("1");
        softDustLimit = new BigDecimal("1");
        softDustPolicy = SoftDustPolicy.BASE_FEE_FOR_EACH_SOFT_DUST_TXO;
        signedMessageHeader = toBytes("Dogecoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        isBitCoinFamily = true;

        confirm= 12;
        coinOrder=5;

        minFeeSat=new BigDecimal("100000000");
        maxFeeSat=new BigDecimal("2000000000");
        maxSend=new BigDecimal("10000000000");
    }

    private static DogecoinMain instance = new DogecoinMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }



    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof DogecoinMain){
            return true;
        }else {
            return false;
        }
    }

}
