package com.coldlar.coin.bitcoin.psbt.model;


import java.util.List;


public class Psbt {
    public List<Input> input;
    public List<Output> output;

    public List<Input> getInput() {
        return input;
    }

    public void setInput(List<Input> input) {
        this.input = input;
    }

    public List<Output> getOutput() {
        return output;
    }

    public void setOutput(List<Output> output) {
        this.output = output;
    }

    public static Input fromTaprootInput(String hash, Long index, WitnessUtxo witnessUtxo, String tapInternalKeyHex) {
        Input input = new Input();
        input.hash = hash;
        input.index = index;
        input.witnessUtxo = witnessUtxo;
        input.tapInternalKey = tapInternalKeyHex;
        return input;
    }

    public static Input fromNestedSegwitToInput(String hash, Long index, String redeemScript, WitnessUtxo witnessUtxo) {
        Input input = new Input();
        input.hash = hash;
        input.index = index;
        input.redeemScript = redeemScript;
        input.witnessUtxo = witnessUtxo;
        return input;
    }

    public static Input fromNativeSegwitToInput(String hash, Long index, WitnessUtxo witnessUtxo) {
        Input input = new Input();
        input.hash = hash;
        input.index = index;
        input.witnessUtxo = witnessUtxo;
        return input;
    }


    public static Input fromP2pkhToInput(String hash, Long index, String rawTxHex) {
        Input input = new Input();
        input.hash = hash;
        input.index = index;
        input.nonWitnessUtxo = rawTxHex;
        return input;
    }


}

