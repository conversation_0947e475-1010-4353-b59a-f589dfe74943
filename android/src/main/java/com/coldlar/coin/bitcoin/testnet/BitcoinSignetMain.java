package com.coldlar.coin.bitcoin.testnet;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;


public class BitcoinSignetMain extends CoinType implements Serializable {
    private BitcoinSignetMain() {
        id = "bitcoin.main";
        coinOrder=1;

        addressHeader =111;
        p2shHeader = 196;
        segwitAddressHrp = "tb";
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 239;

        coinID = ColdlarCoinID.ID_BTCSIGNTEST;
        name = "Bitcoin Signet";
        cnName = "比特币测试网络";
        trName = "比特幣";
        symbol = "btcsignet";
        chain ="btcsignet";
        showSymbol= "sBTC";
        uriScheme = "bitcoin";
        bip44Index = 10002;
        chainId = 10002;
        unitExponent = 8;
        isBitCoinFamily =true;
        isBitcoinNetWork = true;
        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000"); // 0.01 BTC
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        confirm=1;
        //1sat/b 1000sat/kb
        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
        this.bip32HeaderP2PKHpub = 70617039;
        this.bip32HeaderP2PKHpriv = 70615956;
        bip32HeaderP2WPKHpub = bip32HeaderP2PKHpub;
        bip32HeaderP2WPKHpriv = bip32HeaderP2PKHpriv;

        isTestNetWork = true;
        bip49Path="m/49'/0'/0'/0";
        bip84Path="m/84'/0'/0'/0";
        this.rpcUrl="https://mempool.space";

        maxSend=new BigDecimal("21000000");

    }

    private static BitcoinSignetMain instance = new BitcoinSignetMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {

        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPaymentProtocolId() {
        return PAYMENT_PROTOCOL_ID_MAINNET;
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BitcoinSignetMain){
            return true;
        }else {
            return false;
        }
    }

}
