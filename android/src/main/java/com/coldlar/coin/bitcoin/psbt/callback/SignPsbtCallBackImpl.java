package com.coldlar.coin.bitcoin.psbt.callback;


import com.coldlar.coin.bitcoin.psbt.PsbtHandler;

public class SignPsbtCallBackImpl implements SignPsbtCallBack {


    private String password;
    private String path;
    private String tweak;
    private boolean isTestNet;

    private String publicKey;

    private String tweakPublicKey;
    private boolean  isSignMessage;


    public SignPsbtCallBackImpl(String publicKey, boolean isTestNet) {
        this.publicKey = publicKey;
        this.isTestNet = isTestNet;
    }


    public SignPsbtCallBackImpl(String password, String publicKey, String tweak, String tweakPublicKey, String path, boolean isTestNet) {
        this.publicKey = publicKey;
        this.isTestNet = isTestNet;
        this.password = password;
        this.path = path;
        this.tweak = tweak;
        this.tweakPublicKey = tweakPublicKey;
    }

    public SignPsbtCallBackImpl(String password, String publicKey, String tweak, String tweakPublicKey, String path, boolean isTestNet, boolean isSignMessage) {
        this.publicKey = publicKey;
        this.isTestNet = isTestNet;
        this.password = password;
        this.path = path;
        this.tweak = tweak;
        this.tweakPublicKey = tweakPublicKey;
        this.isSignMessage  =isSignMessage;
    }





    @Override
    public String getPublicKey() {
        return publicKey;
    }


    @Override
    public boolean isTestNetWork() {
        return isTestNet;
    }

    @Override
    public String getTweakPublicKey() {
        return tweakPublicKey;
    }

    @Override
    public boolean isSignMessage() {
        return isSignMessage;
    }


    @Override
    public Boolean isXOnlyPoint(String point) {
        return PsbtHandler.isXOnlyPoint(point);
    }

    @Override
    public String xOnlyPointAddTweak(String point, String tweak) {
        return PsbtHandler.xOnlyPointAddTweak(point, tweak);
    }

    @Override
    public String signSecp256k1(String hashStr) {
        return null;
    }


    public String signSchnorr(String hashStr) {
        return null;

    }

    @Override
    public String signSchnorrByScript(String hash) {
            return null;

    }


}