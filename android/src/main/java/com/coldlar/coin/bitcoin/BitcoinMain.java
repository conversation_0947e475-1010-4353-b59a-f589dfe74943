/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-26 10:34:10
 */
package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

public class BitcoinMain extends CoinType implements Serializable {
    private BitcoinMain() {
        id = "bitcoin.main";
        coinOrder = 1;

        addressHeader = 0;
        p2shHeader = 5;
        segwitAddressHrp = "bc";
        acceptableAddressCodes = new int[] { addressHeader, p2shHeader };
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;
        coinID = ColdlarCoinID.ID_BTC;
        name = "Bitcoin";
        cnName = "比特币";
        trName = "比特幣";
        symbol = "BTC";
        chain = "btc";
        showSymbol = "BTC";
        uriScheme = "bitcoin";
        bip44Index = 0;
        unitExponent = 8;
        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000"); // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        isBitCoinFamily = true;
        isBitcoinNetWork = true;
        confirm = 1;
        // 1sat/b 1000sat/kb
        minFeeSat = new BigDecimal("1000");
        maxFeeSat = new BigDecimal("2000000");

        bip49Path = "m/49'/0'/0'/0";
        bip84Path = "m/84'/0'/0'/0";

        maxSend = new BigDecimal("21000000");

    }

    private static BitcoinMain instance = new BitcoinMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address, this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof BitcoinMain) {
            return true;
        } else {
            return false;
        }
    }

}
