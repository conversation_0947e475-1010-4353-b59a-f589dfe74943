package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class BitcoinXMain extends CoinType {
    private BitcoinXMain() {
        id = "bitcoinx.main";
        FORK_ID = Integer.valueOf(16);
        FORK_IN_USE = Integer.valueOf(0);
        BIP143 = true;
        addressHeader = 75;
        p2shHeader = 63;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_BCX;
        cnName = "比特无限";
        name = "BitcoinX";
        symbol = "BCX";
        chain ="bcx";
        showSymbol= "BCX";
        uriScheme = "bitcoinx";
        bip44Index = 1688;
        unitExponent = Integer.valueOf(4);
        feeValue = new BigDecimal("10000");
        minFeeValue = new BigDecimal("1000");
        minNonDust = new BigDecimal("5460");
        softDustLimit = new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("BitcoinX Signed Message:\n");
        isSupportMsgSign = true;
        isBitCoinFamily = true;

        confirm=6;
        coinOrder=29.5f;
    }

    private static BitcoinXMain instance = new BitcoinXMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BitcoinXMain){
            return true;
        }else {
            return false;
        }
    }

}
