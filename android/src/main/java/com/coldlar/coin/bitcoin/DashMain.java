package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class DashMain extends CoinType {
    private DashMain() {
        id = "dash.main"; // Do not change this id as wallets serialize this string

        addressHeader = 76;
        p2shHeader = 16;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 204;

        coinID = ColdlarCoinID.ID_DASH;
        cnName = "达世币";
        trName = "達世幣";
        name = "Dash";
        chain ="dash";
        symbol = "DASH";
        showSymbol= "DASH";
        uriScheme = "dash"; // TODO add multi uri, darkcoin
        bip44Index = 5;
        unitExponent = 8;
        feeValue = new BigDecimal("1000");
        minNonDust = new BigDecimal("1000"); // 0.00001 DASH mininput
        softDustLimit = new BigDecimal("1000"); // 0.00001 DASH
        softDustPolicy = SoftDustPolicy.BASE_FEE_FOR_EACH_SOFT_DUST_TXO;
        signedMessageHeader = toBytes("DarkCoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        confirm=12;
        coinOrder=14;
        isBitCoinFamily = true;
        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
        maxSend=new BigDecimal("18900000");
    }

    private static DashMain instance = new DashMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof DashMain){
            return true;
        }else {
            return false;
        }
    }

}
