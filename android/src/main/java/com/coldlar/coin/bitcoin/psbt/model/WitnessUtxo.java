package com.coldlar.coin.bitcoin.psbt.model;

import java.math.BigDecimal;


public  class WitnessUtxo {
    public String script;
    public BigDecimal value;

    public WitnessUtxo(String scriptHex,BigDecimal value){
        this.script = scriptHex;
        this.value = value;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}