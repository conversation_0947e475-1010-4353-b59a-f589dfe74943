package com.coldlar.coin.bitcoin.psbt;





import com.coldlar.coin.CoinType;
import com.coldlar.coin.ethereum.transaction.HashUtil;
import com.coldlar.core.wallet.AbstractAddress;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */
public final class TaprootAddress implements AbstractAddress {
    private final String address;
    private final CoinType type;

    public TaprootAddress(CoinType paramCoinType, String pubKeyHex) {
        this.type = paramCoinType;
        this.address = PsbtHandler.getTaprootAddress(pubKeyHex,paramCoinType.isTestNetWork());
    }

    private byte[] getHash160() {
        return HashUtil.ripemd160(this.address.getBytes(Charset.forName("utf-8")));
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    @Override
    public CoinType getType() {
        return this.type;
    }

    @Override
    public String toString() {
        return address;

    }








}

