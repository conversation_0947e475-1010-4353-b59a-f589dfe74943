package com.coldlar.coin.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class LitecoinMain extends CoinType {
    private LitecoinMain() {
        id = "litecoin.main";

        addressHeader = 48;
        p2shHeader = 50;//5>>50  58>>196
        segwitAddressHrp = "ltc";
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 176;

        coinID = ColdlarCoinID.ID_LTC;
        name = "Litecoin";
        cnName = "莱特币";
        trName = "萊特幣";
        chain ="ltc";
        symbol = "LTC";
        showSymbol = "LTC";
        uriScheme = "litecoin";
        bip44Index = 2;
        unitExponent = 8;
        feeValue = new BigDecimal("1000");
        isBitCoinFamily = true;

        minNonDust = new BigDecimal("1000");
        softDustLimit = new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.BASE_FEE_FOR_EACH_SOFT_DUST_TXO;
        signedMessageHeader = toBytes("Litecoin Signed Message:\n");
        isSupportMsgSign = true;
        isSupportMulSign = true;
        confirm=1;
        coinOrder=10;
        minFeeSat=new BigDecimal("1000");
        maxFeeSat=new BigDecimal("2000000");
        bip49Path="m/49'/2'/0'/0";
        bip84Path="m/84'/2'/0'/0";
        maxSend=new BigDecimal("84000000");
    }

    private static LitecoinMain instance = new LitecoinMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof LitecoinMain){
            return true;
        }else {
            return false;
        }
    }

}
