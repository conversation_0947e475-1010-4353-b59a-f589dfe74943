package com.coldlar.coin.bitcoin;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class BitcoinSvMain extends CoinType {
    private BitcoinSvMain() {
        id = "bitcoinsv.main";
        FORK_ID = Integer.valueOf(64);
        FORK_IN_USE = Integer.valueOf(0);
        BIP143 = true;
        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_BSV;
        name = "Bitcoin SV";
        cnName = "BSV";
        trName = "BSV";
        symbol = "BSV";
        chain = "bsv";
        showSymbol = "BSV";
        uriScheme = "bitcoinsv";
        bip44Index = 236;
        unitExponent = 8;
        feeValue = new BigDecimal("1000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = false;
        isBitCoinFamily = true;
        confirm = 6;
        coinOrder = 8;
        minFeeSat = new BigDecimal("1000");
        maxFeeSat = new BigDecimal("2000000");
    }

    private static BitcoinSvMain instance = new BitcoinSvMain();

    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address, this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof BitcoinSvMain) {
            return true;
        } else {
            return false;
        }
    }

}
