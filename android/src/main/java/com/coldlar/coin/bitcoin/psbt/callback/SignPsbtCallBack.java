package com.coldlar.coin.bitcoin.psbt.callback;

public interface SignPsbtCallBack {

    public Boolean isXOnlyPoint(String point);

    public String xOnlyPointAddTweak(String point, String tweak);

    public String signSecp256k1(String hash);

    public String signSchnorr(String hash);

    public String signSchnorrByScript(String hash);

    public String getPublicKey();


    public boolean isTestNetWork();

    public String getTweakPublicKey();

    public boolean isSignMessage();

}