package com.coldlar.coin.bitcoin.psbt.model;

import java.math.BigDecimal;

public class Input {
    public String hash;
    public Long index;
    public BigDecimal sequence;
    public String nonWitnessUtxo;

    public String tapInternalKey;


    public String witnessScript;

    public String redeemScript;


    public WitnessUtxo witnessUtxo;


    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public BigDecimal getSequence() {
        return sequence;
    }

    public void setSequence(BigDecimal sequence) {
        this.sequence = sequence;
    }

    public String getNonWitnessUtxo() {
        return nonWitnessUtxo;
    }

    public void setNonWitnessUtxo(String nonWitnessUtxo) {
        this.nonWitnessUtxo = nonWitnessUtxo;
    }

    public String getTapInternalKey() {
        return tapInternalKey;
    }

    public void setTapInternalKey(String tapInternalKey) {
        this.tapInternalKey = tapInternalKey;
    }

    public WitnessUtxo getWitnessUtxo() {
        return witnessUtxo;
    }

    public void setWitnessUtxo(WitnessUtxo witnessUtxo) {
        this.witnessUtxo = witnessUtxo;
    }


    public String getRedeemScript() {
        return redeemScript;
    }

    public void setRedeemScript(String redeemScript) {
        this.redeemScript = redeemScript;
    }

    public String getWitnessScript() {
        return witnessScript;
    }

    public void setWitnessScript(String witnessScript) {
        this.witnessScript = witnessScript;
    }

}