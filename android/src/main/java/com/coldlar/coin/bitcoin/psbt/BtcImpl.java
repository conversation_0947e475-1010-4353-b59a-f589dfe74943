package com.coldlar.coin.bitcoin.psbt;

import android.text.TextUtils;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.bitcoin.psbt.callback.SignPsbtCallBackImpl;
import com.coldlar.coin.bitcoin.psbt.model.Bech32Model;
import com.coldlar.v8.ScriptLoader;
import com.eclipsesource.v8.V8;
import com.eclipsesource.v8.V8Object;
import com.google.gson.Gson;

import org.bitcoinj.core.Address;
import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.script.Script;
@SuppressWarnings("deprecation")
public class BtcImpl {



    public static String getTaprootAddress(String pubKeyHex, boolean isTestNet) {
        V8 runtime = ScriptLoader.sInstance.loadByCoinCode("BTC");
        try {
            PsbtHandler btcTaproot = new PsbtHandler(runtime, new SignPsbtCallBackImpl(pubKeyHex, isTestNet));
            return btcTaproot.toTaprootAddress();
        } catch (Exception ignored) {
            return "";
        } finally {
            if (runtime != null) {
                runtime.release(false);
            }
        }
    }


    public static Bech32Model getBech32AddressVersion(String address) {

        V8 runtime = ScriptLoader.sInstance.loadByCoinCode("BTC");
        V8Object signProvider = null;
        try {
            signProvider = new V8Object(runtime);
            String bech32Json = (String) runtime.executeJSFunction("g_bech32Check", address, signProvider);
            if (TextUtils.isEmpty(bech32Json)) {
                return null;
            }

            return new Gson().fromJson(bech32Json, Bech32Model.class);
        } catch (Exception e) {
            return null;
        } finally {
            if (signProvider != null && !signProvider.isReleased()) {
                signProvider.release();
            }
        }
    }

    public static boolean isBech32Address(String address) {
        Bech32Model bech32Model = getBech32AddressVersion(address);
        if (bech32Model == null) {
            return false;
        }
        return bech32Model.getVersion() >= 0;
    }

    public static boolean isTaprootAddress(String address) {
        Bech32Model bech32Model = getBech32AddressVersion(address);
        if (bech32Model == null) {
            return false;
        }
        return bech32Model.getVersion() == 1 ;
    }


    public static boolean isSegWitOrNotTaprootAddress(CoinType coinType, String addressStr) {
        try {
            Address address = Address.fromString(coinType, addressStr);
            Script.ScriptType scriptType = address.getOutputScriptType();
            // 检查地址是否是隔离见证地址（包括P2SH和Bech32格式）
            return scriptType == Script.ScriptType.P2WPKH || scriptType == Script.ScriptType.P2WSH || scriptType == Script.ScriptType.P2SH;
        } catch (AddressFormatException e) {
            // 地址格式异常，返回false
            return false;
        }
    }

    public static boolean isLegacyAddress(CoinType coinType, String addressStr) {
        try {
            Address address = Address.fromString(coinType, addressStr);
            Script.ScriptType scriptType = address.getOutputScriptType();
            return scriptType == Script.ScriptType.P2PKH ;
        } catch (AddressFormatException e) {
            // 地址格式异常，返回false
            return false;
        }
    }






}
