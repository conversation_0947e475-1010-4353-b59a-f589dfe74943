package com.coldlar.coin;

import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class AllChainMain extends CoinType {
    private AllChainMain() {
        id = "all.main";

        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = "all";
        name = "All";
        cnName = "所有资产";
        symbol = "ALL";
        showSymbol = "ALL";
        chain = "all";
        uriScheme = "all";
        bip44Index = -1000;
        unitExponent = 8;
        feeValue = new BigDecimal("10000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000000");  // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        confirm = 12;
    }

    private static AllChainMain instance = new AllChainMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {

        return false;

    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof AllChainMain) {
            return true;
        } else {
            return false;
        }
    }

}
