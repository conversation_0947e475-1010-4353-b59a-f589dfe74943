package com.coldlar.coin.tron.utils;


import com.coldlar.coin.tron.TronMain;
import com.coldlar.coin.tron.crypto.TrxHash;
import com.coldlar.core.util.CoinUtil;
import com.coldlar.core.util.NumberUtil;
import com.google.protobuf.ByteString;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.spongycastle.math.ec.ECPoint;
import org.spongycastle.util.encoders.Hex;
import org.tronj.Parameter;

import java.math.BigInteger;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2019/10/21 17:28.
 * description 波场
 */
public class TrxHexUtils {

    /**
     * 获取波场地址hex
     *
     * @param address
     * @return
     */
    public static String getTrxHexString(String address) {
        byte[] addrByte = decodeFromBase58Check(address);
        ByteString bsTo = ByteString.copyFrom(addrByte);
        String addressHex = Hex.toHexString(bsTo.toByteArray());
        return addressHex;
    }

    public static byte[] decodeFromBase58Check(String addressBase58) {
        if (StringUtils.isEmpty(addressBase58)) {
            return null;
        }
        byte[] address = decode58Check(addressBase58);
        if (!isAddressValid(address)) {
            return null;
        }
        return address;
    }

    private static byte[] decode58Check(String input) {
        byte[] decodeCheck = Base58.decode(input);
        if (decodeCheck.length <= 4) {
            return null;
        }
        byte[] decodeData = new byte[decodeCheck.length - 4];
        System.arraycopy(decodeCheck, 0, decodeData, 0, decodeData.length);
        byte[] hash0 = TrxHash.sha256(decodeData);
        byte[] hash1 = TrxHash.sha256(hash0);
        if (hash1[0] == decodeCheck[decodeData.length] &&
                hash1[1] == decodeCheck[decodeData.length + 1] &&
                hash1[2] == decodeCheck[decodeData.length + 2] &&
                hash1[3] == decodeCheck[decodeData.length + 3]) {
            return decodeData;
        }
        return null;
    }

    public static String encode58Check(byte[] input) {
        byte[] hash0 = TrxHash.sha256(input);
        byte[] hash1 = TrxHash.sha256(hash0);
        byte[] inputCheck = new byte[input.length + 4];
        System.arraycopy(input, 0, inputCheck, 0, input.length);
        System.arraycopy(hash1, 0, inputCheck, input.length, 4);
        return Base58.encode(inputCheck);
    }

    public static boolean isAddressValid(byte[] address) {
        if (address == null || address.length == 0) {
            return false;
        }
        if (address.length != Parameter.CommonConstant.ADDRESS_SIZE) {
            return false;
        }
        byte preFixbyte = address[0];
        if (preFixbyte != Parameter.CommonConstant.ADD_PRE_FIX_BYTE) {
            return false;
        }

        return true;
    }

    public static byte[] computeAddress(ECPoint pubPoint) {
        byte[] pubBytes = pubPoint.getEncoded(/* uncompressed */ false);
        return computeAddress(pubBytes);
    }

    public static byte[] computeAddress(byte[] pubBytes) {
        return TrxHash.sha3omit12(Arrays.copyOfRange(pubBytes, 1, pubBytes.length));
    }

    /**
     * 验证地址
     *
     * @param addressBase58
     * @return
     */
    public static boolean addressValid(String addressBase58) {
        if (StringUtils.isEmpty(addressBase58)) {
            return false;
        }
        byte[] address = decode58Check(addressBase58);
        if (!addressValid(address)) {
            return false;
        }
        return true;
    }

    public static boolean addressValid(byte[] address) {
        if (ArrayUtils.isEmpty(address)) {
            System.out.println("Warning: Address is empty !!");
            return false;
        }
        if (address.length != Parameter.CommonConstant.ADDRESS_SIZE) {
            System.out.println(
                    "Warning: Address length need " + Parameter.CommonConstant.ADDRESS_SIZE + " but " + address.length
                            + " !!");
            return false;
        }
        byte preFixbyte = address[0];
        if (preFixbyte != Parameter.CommonConstant.ADD_PRE_FIX_BYTE) {
            System.out
                    .println("Warning: Address need prefix with " + Parameter.CommonConstant.ADD_PRE_FIX_BYTE + " but "
                            + preFixbyte + " !!");
            return false;
        }
        return true;
    }

    /**
     * 获取string 类型值
     *
     * @param value 最小单位
     * @return "1.234"
     */
    public static String getValueString(long value) {
        if (value == 0) {
            return "0";
        }
        return NumberUtil.setFormate(CoinUtil.getCoinValueSatoshiToBtc(value, TronMain.get().getUnitExponent()) + "");
    }

    public static String getValueString(String value) {
        if (StringUtils.isEmpty(value) ) {
            return "0";
        }
        return NumberUtil.setFormate(CoinUtil.getCoinValueSatoshiToBtc(value, TronMain.get().getUnitExponent()) + "");
    }

    /**
     * 获取 double 类型值
     *
     * @param value 最小单位
     * @return 1.234
     */
    public static double getValueDouble(long value) {
        if (value == 0) {
            return 0;
        }
        return CoinUtil.getCoinValueSatoshiToBtc(value, TronMain.get().getUnitExponent()).doubleValue();
    }

    /**
     * 获取 double 类型值
     *
     * @param value 最小单位
     * @return 1.234
     */
    public static double getTokenValueDouble(long value, String decimal) {
        int dec;
        if (value == 0) {
            return 0;
        }
        if (StringUtils.isEmpty(decimal)) {
            dec = TronMain.get().getUnitExponent();
        } else {
            dec = Integer.parseInt(decimal);
        }
        return CoinUtil.getCoinValueSatoshiToBtc(value, dec).doubleValue();
    }

    /**
     * ascii转String
     *
     * @param hex
     * @return
     */
    public static String convertHexToString(String hex) {

        StringBuilder sb = new StringBuilder();
        StringBuilder temp = new StringBuilder();

        //49204c6f7665204a617661 split into two characters 49, 20, 4c...
        for (int i = 0; i < hex.length() - 1; i += 2) {

            //grab the hex in pairs
            String output = hex.substring(i, (i + 2));
            //convert hex to decimal
            int decimal = Integer.parseInt(output, 16);
            //convert the decimal to character
            sb.append((char) decimal);

            temp.append(decimal);
        }

        return sb.toString();
    }

    /**
     * 获取tolen金额
     *
     * @param data
     */
    public static long getTokenAmount(String data) {
        try {
            String address = data.substring(0, data.length() / 2);
            int len = address.length();
            for (int i = 0; i < len; i += 2) {
                if (address.startsWith("00")) {
                    address = address.substring(2);
                } else {
                    break;
                }
            }
            String value = data.substring(data.length() / 2);
//
//        System.out.println("test address ; "+ TrxHexUtils.encode58Check(Hex.decode(address)));
//        System.out.println("test value ; "+ new BigInteger(Hex.decode(value)).longValue());
            return new BigInteger(Hex.decode(value)).longValue();
        } catch (Exception e) {
            return 0;
        }


    }


}
