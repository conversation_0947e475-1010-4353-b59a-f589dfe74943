package com.coldlar.coin.tron;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.coin.tron.utils.TrxHexUtils;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class TronMain extends CoinType {
    private TronMain() {
        id = "tron.main";

        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_TRX;
        name = "Tron";
        cnName = "波场";
        trName = "波场";
        symbol = "TRX";
        chain = "trx";

        showSymbol = "TRX";
        uriScheme = "bitcoin";
        bip44Index = 195;
        unitExponent = 6;
        feeValue = new BigDecimal("1000");

        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000");  // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm = 10;
        coinOrder = 4;
    }

    private static TronMain instance = new TronMain();

    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        try {
            return TrxHexUtils.addressValid(address);
        } catch (Exception e) {
            return false;
        }

    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof TronMain) {
            return true;
        } else {
            return false;
        }
    }

}
