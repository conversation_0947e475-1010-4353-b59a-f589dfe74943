package com.coldlar.coin.tron;

public class ColdLarLibTron {

    // Used to load the 'native-lib' library on application startup.
    static {
        System.loadLibrary("coldLarTron");
    }

    /**
     * A native method that is implemented by the 'native-lib' native library,
     * which is packaged with this application.
     */
    public static native String stringFromJNI();

    /***********************************************************
     * Function Name :      getLibVersion
     * Date          :      2020.3.26
     * Parameter     :      无
     * Return Code  :       json字符串，status、version
     * Description  :       获取库版本
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getLibVersion();


    /***********************************************************
     * Function Name :      GetAddress
     * Date          :      2020.3.16
     * Parameter     :      public_key：地址对应的65字节的非压缩公钥
     * Return Code  :       json字符串，status、address
     * Description  :       获取波场币TRON的地址
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getAddress(String public_key);

    /***********************************************************
     * Function Name :      getRawDataAndSignHash
     * Date          :      2020.3.26
     * Parameter     :      tx_json：原始json数据
     * Return Code  :       json字符串，status、hash、raw_data_hex
     * Description  :       获取波场币TRON的待签名数据和json字符串的序列化结果
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getRawDataAndSignHash(String tx_json);


    /***********************************************************
     * Function Name :      getSmartBuildSignData
     * Date          :      2020.3.26
     * Parameter     :      tx_json：原始json数据
     * Return Code  :       json字符串，status、smart_sign_json
     * Description  :       获取波场币TRON使用Smart构签需要的数据
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getSmartBuildSignData(String tx_json);


    /***********************************************************
     * Function Name :      getRawDataHexUnpackToJsonData
     * Date          :      2020.9.16
     * Parameter     :      raw_data_hex ：序列化后的raw_data十六进制字符串
     * Return Code  :       json字符串，status、raw_data原始j接送数据
     * Description  :       根据序列化的raw_data_hex十六进制字符串反推raw_data对应的json
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getRawDataHexUnpackToJsonData(String raw_data_hex);


    /***********************************************************
     * Function Name :      checkAddress
     * Date          :      2020.9.16
     * Parameter     :      address：地址字符串
     * Return Code  :       json字符串，status、
     * Description  :       校验波场币地址
     * Author  :            cuihuan
     ***********************************************************/
    public static native String checkAddress(String address);


    /***********************************************************
     * Function Name :      getSignHashFromRawData
     * Date          :      2020.9.18
     * Parameter     :      tx_json：原始json数据
     * Return Code  :       json字符串，status、hash
     * Description  :       根据序列化数据获取波场币TRON的待签名HASH
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getSignHashFromRawData(String raw_data_hex);


    /***********************************************************
     * Function Name :      getAddressHexToAddressStr
     * Date          :      2020.9.18
     * Parameter     :      tx_json：原始json数据
     * Return Code  :       json字符串，status、hash
     * Description  :       根据16进制地址数据计算地址字符串
     * Author  :            cuihuan
     ***********************************************************/
    public static native String getAddressHexToAddressStr(String raw_data_hex);


}
