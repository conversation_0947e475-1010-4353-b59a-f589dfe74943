package com.coldlar.coin;

import static com.coldlar.coin.tron.crypto.TrxHash.sha256;
import static com.coldlar.core.base.Chain.ADDRESS_BIP44;
import static com.coldlar.core.base.Chain.ADDRESS_BIP49;
import static com.coldlar.core.base.Chain.ADDRESS_BIP84;
import static com.coldlar.core.base.Chain.ADDRESS_BIP86;
import static org.web3j.crypto.Keys.ADDRESS_LENGTH_IN_HEX;
import static org.web3j.crypto.Sign.CHAIN_ID_INC;


import android.text.InputType;
import android.text.TextUtils;
import android.util.Log;

import com.coldlar.coin.atom.CosmosMain;
import com.coldlar.coin.atom.CosmosUtil;
import com.coldlar.coin.bch.BitcoinCashAddressFormatter;
import com.coldlar.coin.bch.BitcoinCashMain;
import com.coldlar.coin.bitcoin.BitcoinDiamondMain;
import com.coldlar.coin.bitcoin.BitcoinGodMain;
import com.coldlar.coin.bitcoin.testnet.BitcoinTestnetMain;
import com.coldlar.coin.bitcoin.testnet.BitcoinSignetMain;
import com.coldlar.coin.eos.SignEosTxPresenter;
import com.coldlar.coin.eos.bean.EosChainInfo;
import com.coldlar.coin.eos.core.ec.EosUtil;
import com.coldlar.coin.eos.core.ec.PackedTransaction;
import com.coldlar.coin.eos.core.ec.SignedTransaction;
import com.coldlar.coin.eos.core.ec.TypeChainId;
import com.coldlar.coin.eos.utils.TimeUtil;
import com.coldlar.coin.ethereum.EthParseUtil;
import com.coldlar.coin.ethereum.entity.EthTransactionInfo;
import com.coldlar.coin.ethereum.testnet.EthereumHoleskyTest;
import com.coldlar.coin.bitcoin.BitcoinMain;
import com.coldlar.coin.bitcoin.BitcoinPayMain;
import com.coldlar.coin.bitcoin.BitcoinSvMain;
import com.coldlar.coin.bitcoin.DashMain;
import com.coldlar.coin.bitcoin.DogecoinMain;
import com.coldlar.coin.bitcoin.LitecoinMain;
import com.coldlar.coin.bitcoin.QtumMain;
import com.coldlar.coin.bitcoin.UsdtMain;
import com.coldlar.coin.bitcoin.ZcashMain;
import com.coldlar.coin.bitcoin.engine.BtcTxEngine;
import com.coldlar.coin.bitcoin.psbt.TaprootAddress;
import com.coldlar.coin.bnb.BinanceMain;
import com.coldlar.coin.bnb.ColdLarLibBinanceCoin;
import com.coldlar.coin.eos.EosMain;
import com.coldlar.coin.eos.IostMain;
import com.coldlar.coin.eos.core.HexUtils;
import com.coldlar.coin.eos.core.ec.Base58;
import com.coldlar.coin.eos.core.ec.EosPublicKey;
import com.coldlar.coin.ethereum.CallTransaction;
import com.coldlar.coin.ethereum.ERC20_API;
import com.coldlar.coin.ethereum.EthClassicMain;
import com.coldlar.coin.ethereum.EthereumMain;
import com.coldlar.coin.ethereum.evm.BNBMain;
import com.coldlar.coin.ethereum.evm.HecoMain;
import com.coldlar.coin.ethereum.evm.OKTMain;
import com.coldlar.coin.ethereum.evm.PolygonMain;
import com.coldlar.coin.ethereum.layer.ArbitrumMain;
import com.coldlar.coin.ethereum.layer.OptimismMain;
import com.coldlar.coin.ethereum.layer.BaseMain;
import com.coldlar.coin.ethereum.layer.BlastMain;
import com.coldlar.coin.ethereum.layer.LineaMain;
import com.coldlar.coin.ethereum.layer.ZkSyncEraMain;
import com.coldlar.coin.atom.CosmosMain;
import com.coldlar.coin.atom.BabylonMain;
import com.coldlar.coin.atom.BabylonTestNetMain;
import com.coldlar.coin.ethereum.transaction.EthTransaction;
import com.coldlar.coin.ethereum.transaction.FunctionUtil;
import com.coldlar.coin.filecoin.ColdLarLibFilecoin;
import com.coldlar.coin.filecoin.FilecoinMain;
import com.coldlar.coin.nem.NemMain;
import com.coldlar.coin.nem.nac.models.NacPublicKey;
import com.coldlar.coin.nem.nac.models.account.PublicAccountData;
import com.coldlar.coin.neo.NeoMain;
import com.coldlar.coin.ripple.RippleMain;
import com.coldlar.coin.tron.ColdLarLibTron;
import com.coldlar.coin.tron.SolanaMain;
import com.coldlar.coin.tron.TronMain;
import com.coldlar.coin.tron.crypto.TrxECKey;
import com.coldlar.coin.tron.utils.TronAbiUtil;
import com.coldlar.coin.tron.utils.TrxHexUtils;

import com.coldlar.coin.xlm.XlmMain;
import com.coldlar.core.Constant;
import com.coldlar.core.exceptions.AddressMalformedException;
import com.coldlar.core.util.AddressUtil;
import com.coldlar.core.util.BitAddress;
import com.coldlar.core.util.BitAddressUtils;
import com.coldlar.core.util.BitSegwitAddress;
import com.coldlar.core.util.CoinUtil;
import com.coldlar.core.util.GsonUtils;
import com.coldlar.core.util.NumberUtil;
import com.coldlar.core.util.StringUtil;
import com.coldlar.core.wallet.families.ethereum.EthAddress;
import com.coldlar.core.wallet.families.ethereum.RippleAddress;
import com.coldlar.model.BaseSendRequest;
import com.coldlar.model.BtcSignRequestInput;
import com.coldlar.model.BtcSignRequestOutput;
import com.coldlar.model.SignData;
import com.coldlar.model.UtxoModel;
import com.coldlar.utils.BigDecimalUtils;
import com.coldlar.utils.ParseCommUtil;
import com.coldlar.utils.PubkeyAddressUtil;
import com.example.wallet_core.MethodCallInterface;
import com.example.wallet_core.WalletCorePlugin;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.ripple.config.RippleConfig;
import com.ripple.utils.HashUtils;
import com.coldlar.coin.bitcoin.psbt.BtcImpl;

import org.binancej.BaseChain;
import org.bitcoinj.core.Address;
import org.bitcoinj.core.Coin;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.Sha256Hash;
import org.bitcoinj.core.Transaction;
import org.bitcoinj.core.TransactionInput;
import org.bitcoinj.core.TransactionOutPoint;
import org.bitcoinj.core.TransactionOutput;
import org.bitcoinj.core.Utils;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.TransactionSignature;
import org.bitcoinj.script.Script;
import org.bitcoinj.script.ScriptBuilder;
import org.bouncycastle.util.BigIntegers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.ripple.bouncycastle.util.encoders.Hex;
import org.stellar.sdk.KeyPair;
import org.web3j.utils.Numeric;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;

@SuppressWarnings("unchecked")
public class CoreCoinBase {


    private static final CoreCoinBase INSTANCE = new CoreCoinBase();

    private CoreCoinBase() {
        if (INSTANCE != null) {
            throw new IllegalStateException("Instance already exists!");
        }
    }

    public static CoreCoinBase getInstance() {
        return INSTANCE;
    }

    public CoinType getCoinTypeByChain(String chain) {
        CoinType mCoinType = null;
        for (CoinType coinType : getAllCainList()) {
            if (coinType.getChain().equals(chain)) {
                mCoinType = coinType;
                break;
            }
        }
        return mCoinType;

    }


    public CoinType getCoinTypeBySlip44Id(int slip44Id) {
        CoinType mCoinType = null;
        for (CoinType coinType : getAllCainList()) {
            if (coinType.getBip44Index() == slip44Id) {
                mCoinType = coinType;
                break;
            }
        }
        return mCoinType;

    }

    public static List<CoinType> getAllCainList() {
        List<CoinType> items = new ArrayList<>();
        items.add(BitcoinMain.get());
        items.add(BitcoinTestnetMain.get());
        items.add(BitcoinSignetMain.get());
        items.add(BitcoinMain.get());
        items.add(EthereumMain.get());
        items.add(EthereumHoleskyTest.get());
        items.add(UsdtMain.get());
        items.add(BNBMain.get());
        items.add(SolanaMain.get());
        items.add(CosmosMain.get());
        items.add(BabylonMain.get());
        items.add(BabylonTestNetMain.get());
        items.add(TronMain.get());
        items.add(OKTMain.get());
        items.add(BaseMain.get());
        items.add(BlastMain.get());
        items.add(LineaMain.get());
        items.add(ZkSyncEraMain.get());
        items.add(HecoMain.get());
        items.add(DogecoinMain.get());
        items.add(PolygonMain.get());
        items.add(ArbitrumMain.get());
        items.add(OptimismMain.get());
        items.add(BitcoinCashMain.get());
        items.add(LitecoinMain.get());
        items.add(EosMain.get());
        items.add(IostMain.get());
        items.add(RippleMain.get());
        items.add(BinanceMain.get());
        items.add(EthClassicMain.get());
        items.add(FilecoinMain.get());
        items.add(ZcashMain.get());
        items.add(NemMain.get());
        items.add(DashMain.get());
        items.add(CosmosMain.get());
        items.add(QtumMain.get());
        items.add(BitcoinDiamondMain.get());
        items.add(BitcoinGodMain.get());
        items.add(BitcoinSvMain.get());
        items.add(BitcoinPayMain.get());
        items.add(XlmMain.get());
        items.add(NeoMain.get());

        return items;

    }

    public boolean validateBitcoinFamilyAddress(String address, CoinType type) {
        if (BtcImpl.isBech32Address(address)) {
            return BtcImpl.isBech32Address(address);
        }
        boolean isRightAddress = false;

        int version = 9999;
        try {
            version = AddressUtil.getExpectedVersion(20, address);
        } catch (Exception e) {

        }
        for (int addressCode : type.getAcceptableAddressCodes()) {
            if (addressCode == version) {
                isRightAddress = true;
                break;
            }
        }
        return isRightAddress;
    }

    public boolean validateEthereumFamilyAddress(String address) {

        String cleanInput = Numeric.cleanHexPrefix(address);

        try {
            Numeric.toBigIntNoPrefix(cleanInput);
        } catch (NumberFormatException e) {
            return false;
        }

        return cleanInput.length() == ADDRESS_LENGTH_IN_HEX;
    }

    public String createAddressOrPublicKeyByBase58(Map<String, Object> map) throws Exception {
        Log.d(WalletCorePlugin.TAG, map.toString());

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            return "";
        }
        CoinType mCoinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
        if (mCoinType == null) {
            return "";
        }
        String chainBase58 = (String) map.get(MethodCallInterface.argChainBase58);


        String publicKeyBase58 = (String) map.get(MethodCallInterface.argPublicKeyBase58);

        boolean isAddress = (boolean) map.get(MethodCallInterface.argIsAddress);

        int addressIndex = (int) map.get(MethodCallInterface.argIndex);

        byte[] chainBase = org.bitcoinj.core.Base58.decode(chainBase58);
        byte[] pubkey = org.bitcoinj.core.Base58.decode(publicKeyBase58);
        DeterministicKey masterPubkey = HDKeyDerivation.createMasterPubKeyFromBytes(pubkey, chainBase);//主密钥
        DeterministicKey ministicKey = HDKeyDerivation.deriveChildKey(masterPubkey, addressIndex);
        if (isAddress) {
            return BitAddress.from(mCoinType, ministicKey).toString();
        } else {
            return org.spongycastle.util.encoders.Hex.toHexString(ministicKey.getPubKey());
        }


    }

    /**
     * 创建地址
     */
    public String createAddressOrPublicKey(Map<String, Object> map, boolean isAddress) throws Exception {
        Log.d(WalletCorePlugin.TAG, map.toString());

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            return "";
        }
        CoinType mCoinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);
        if (mCoinType == null) {
            return "";
        }


        String xPub = (String) map.get(MethodCallInterface.argPubData);
        xPub = xPub.replace("??", "").replace(" ", "").replace("\u0000", "");


        boolean isXPub = false;
        if (map.get(MethodCallInterface.argIsXpub) != null) {
            isXPub = (boolean) map.get(MethodCallInterface.argIsXpub);
        }

        String path = (String) map.get(MethodCallInterface.argPath);

        int addressIndex = map.get(MethodCallInterface.argIndex) == null ? 0
                : (int) map.get(MethodCallInterface.argIndex);


        List<ChildNumber> pathList = CoinUtil.parsePath(path);
        int addressType = pathList.get(0).num();


        String address = "";
        String publicKey = "";
        if (mCoinType.isBitCoinFamily) {

            if (mCoinType.isBitcoinNetWork || mCoinType instanceof LitecoinMain) {

                switch (addressType) {
                    case ADDRESS_BIP44:
                        address = PubkeyAddressUtil.Companion.getAddressFromPub(mCoinType, xPub, addressIndex);
                        publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);

                        break;
                    case ADDRESS_BIP49:
                        DeterministicKey parentKey = DeterministicKey.deserializeB58(xPub);
                        DeterministicKey childKey = HDKeyDerivation.deriveChildKey(parentKey, addressIndex);
                        Script textRedeemScript = ScriptBuilder.createP2WPKHOutputScript(childKey);
                        byte[] p2wpkhHash = Utils.sha256hash160(textRedeemScript.getProgram());
                        Script scriptPubKey = ScriptBuilder.createP2SHOutputScript(p2wpkhHash);
                        address = BitAddress.fromScriptHash(mCoinType, true, scriptPubKey.getPubKeyHash()).toBase58();
                        publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);
                        break;
                    case ADDRESS_BIP84:
                        DeterministicKey parentKey1 = DeterministicKey.deserializeB58(xPub);
                        DeterministicKey childKey1 = HDKeyDerivation.deriveChildKey(parentKey1, addressIndex);
                        address = BitSegwitAddress.from(mCoinType, childKey1).toBech32();
                        publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);
                        break;
                    case ADDRESS_BIP86:
                        DeterministicKey parentKey2 = DeterministicKey.deserializeB58(xPub);
                        DeterministicKey childKey2 = HDKeyDerivation.deriveChildKey(parentKey2, addressIndex);
                        address = new TaprootAddress(mCoinType, childKey2.getPublicKeyAsHex()).toString();
                        publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);
                        break;
                    default:
                        break;

                }

            } else if (mCoinType instanceof BitcoinCashMain) {
                String addressStr = PubkeyAddressUtil.Companion.getAddressFromPub(mCoinType, xPub, addressIndex);
                address = BitcoinCashAddressFormatter.convertAddress(addressStr);
                publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);

            } else {
                address = PubkeyAddressUtil.Companion.getAddressFromPub(mCoinType, xPub, addressIndex);
                publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);

            }
        } else if (mCoinType.isEthereumFamily) {
            EthAddress ethAddresses = PubkeyAddressUtil.Companion.getEthAddressFromPub(mCoinType, xPub, addressIndex);
            address = ethAddresses.addressFormat();
            publicKey = PubkeyAddressUtil.Companion.getPubkeyFromxpub(xPub, addressIndex);


        } else if (mCoinType instanceof RippleMain) {
            if (isXPub) {
                DeterministicKey masterPubkey = DeterministicKey.deserializeB58(null, xPub);
                DeterministicKey addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, addressIndex);
                RippleAddress rippleAddress = new RippleAddress(mCoinType, RippleConfig.getB58IdentiferCodecs()
                        .encodeAddress(HashUtils.SHA256_RIPEMD160(addressPubkey.getPubKey())));
                address = rippleAddress.toString();
                publicKey = Hex.toHexString(addressPubkey.getPubKey());
            } else {
                RippleAddress rippleAddress = new RippleAddress(mCoinType, RippleConfig.getB58IdentiferCodecs()
                        .encodeAddress(HashUtils.SHA256_RIPEMD160(Hex.decode(xPub))));
                address = rippleAddress.toString();
                publicKey = xPub;
            }

        } else if (mCoinType instanceof NemMain) {
            NacPublicKey nacPblicKey = new NacPublicKey(Hex.decode(xPub));
            PublicAccountData pubkeyData = new PublicAccountData(nacPblicKey);
            address = pubkeyData.address.toString();
            publicKey = xPub;

        } else if (mCoinType instanceof XlmMain) {
            KeyPair keyPair = KeyPair.fromPublicKey(HexUtils.toBytes(xPub));
            address = keyPair.getAccountId();
            publicKey = xPub;
        } else if (mCoinType instanceof TronMain) {
            DeterministicKey masterPubkey = DeterministicKey.deserializeB58(null, xPub);
            DeterministicKey addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, addressIndex);
            TrxECKey key = TrxECKey.fromPublicOnly(addressPubkey.getPubKey());
            byte[] pubkeyPoint = TrxHexUtils.computeAddress(key.getPubKeyPoint());
            address = TrxHexUtils.encode58Check(pubkeyPoint);
            publicKey = Hex.toHexString(addressPubkey.getPubKey());

        } else if (mCoinType instanceof BinanceMain) {
            DeterministicKey parentKey = DeterministicKey.deserializeB58(xPub);
            DeterministicKey childKey = HDKeyDerivation.deriveChildKey(parentKey, addressIndex);
            String addressJson = ColdLarLibBinanceCoin.getAddress(childKey.getPublicKeyAsHex());
            address = new JSONObject(addressJson).getString("address");
            publicKey = Hex.toHexString(childKey.getPubKey());
        } else if (mCoinType instanceof CosmosMain) {
            DeterministicKey parentKey = DeterministicKey.deserializeB58(xPub);
            DeterministicKey childKey = HDKeyDerivation.deriveChildKey(parentKey, addressIndex);
            address = CosmosUtil.getDpAddress(childKey.getPublicKeyAsHex());
            publicKey = Hex.toHexString(childKey.getPubKey());

        } else if (mCoinType instanceof FilecoinMain) {
            DeterministicKey parentKey = DeterministicKey.deserializeB58(xPub);
            DeterministicKey childKey = HDKeyDerivation.deriveChildKey(parentKey, addressIndex);
            String hex = Hex.toHexString(childKey.getPubKeyPoint().getEncoded(false));
            String addressJson = ColdLarLibFilecoin.getAddress(hex, false);
            address = new JSONObject(addressJson).getString("address");
            publicKey = Hex.toHexString(childKey.getPubKey());
        } else if (mCoinType instanceof BabylonMain ||mCoinType instanceof BabylonTestNetMain  ) {
            DeterministicKey parentKey = DeterministicKey.deserializeB58(xPub);
            DeterministicKey childKey = HDKeyDerivation.deriveChildKey(parentKey, addressIndex);
            address = CosmosUtil.getBabylonDpAddress(childKey.getPublicKeyAsHex());
            publicKey = Hex.toHexString(childKey.getPubKey());
        } else if (mCoinType instanceof EosMain) {
            PubkeyAddressUtil.Companion.getAddressPubkey(xPub, 0);
            DeterministicKey masterPubkey = DeterministicKey.deserializeB58(xPub);
            DeterministicKey pubKey = HDKeyDerivation.deriveChildKey(masterPubkey, 0);
            EosPublicKey eosPublicKey = new EosPublicKey(pubKey.getPubKey());
            publicKey = eosPublicKey.toString();

        }else if (mCoinType instanceof IostMain) {
            publicKey = Base58.encode(Hex.decode(xPub));
        }

        if (isAddress) {
            return address;
        } else {
            return publicKey;
        }

    }

    public byte[] getEthereumSignHash(Map<String, Object> map) {

        return Objects.requireNonNull(getEthTransaction(map)).getHash();

    }


    public String getEthereumRawTx(Map<String, Object> map) throws Exception {
        Log.d(WalletCorePlugin.TAG, map.toString());
        long chainId = 1;
        if (map.get(MethodCallInterface.argChainId) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argChainId);
            if (integer != null)
                chainId = integer.longValue();
        }

        List<byte[]> signDataByteArray = (List<byte[]>) map.get(MethodCallInterface.argSignList);

        EthTransaction ethTransaction = getEthTransaction(map);
        List<String> signHashSource = new ArrayList<>();
        for (byte[] byteArray : signDataByteArray) {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArray);
            int count = inputStream.read();
            for (int j = 0; j < count; j++) {
                int hashLen = inputStream.read();
                byte[] value = new byte[hashLen];
                inputStream.read(value);
                signHashSource.add(org.spongycastle.util.encoders.Hex.toHexString(value));
            }
        }

        //讲签名信息放入交易
        String ret = signHashSource.get(0);
        SignData data = ParseCommUtil.parseSignData(ret);
        BigInteger r = new BigInteger(data.getR(), 16);
        BigInteger s = new BigInteger(data.getS(), 16);
        ECKey.ECDSASignature signature = new ECKey.ECDSASignature(r, s);
        BigInteger v = Numeric.toBigInt(data.getV());
        v = v.add(BigInteger.valueOf(chainId).multiply(BigInteger.valueOf(2)));
        v = v.add(BigInteger.valueOf(CHAIN_ID_INC));
        assert ethTransaction != null;
        ethTransaction.setSignData(signature);
        String rawTx = org.bouncycastle.util.encoders.Hex.toHexString(ethTransaction.getEncoded(BigIntegers.asUnsignedByteArray(v)));
        if (!rawTx.contains("0x")) {
            rawTx = "0x" + rawTx;
        }
        return rawTx;

    }


    private EthTransaction getEthTransaction(Map<String, Object> map) {
        Log.d(WalletCorePlugin.TAG, map.toString());

        String chain = (String) map.get(MethodCallInterface.argChain);
        if (chain == null) {
            return null;
        }

        CoinType mCointype = CoreCoinBase.getInstance().getCoinTypeByChain(chain);

        if (mCointype == null) {
            return null;
        }

        String gasPriceStr = (String) map.get(MethodCallInterface.argGasPrice);
        if (TextUtils.isEmpty(gasPriceStr)) {
            return null;
        }


        int decimal = 0;
        if (map.get(MethodCallInterface.argDecimal) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argDecimal);
            if (integer != null)
                decimal = integer.intValue();
        }

        String gasLimitStr = (String) map.get(MethodCallInterface.argGasLimit);
        if (TextUtils.isEmpty(gasLimitStr)) {
            return null;
        }


        String nonceStr = (String) map.get(MethodCallInterface.argNonce);

        if (TextUtils.isEmpty(nonceStr)) {
            return null;
        }

        String contractStr = (String) map.get(MethodCallInterface.argContract);

        long chainIdL = 1;
        if (map.get(MethodCallInterface.argChainId) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argChainId);
            if (integer != null)
                chainIdL = integer.longValue();
        }

        String amountStr = (String) map.get(MethodCallInterface.argAmount);
        String amountHex = FunctionUtil.balanceToHex(amountStr, decimal);

        String txAddressStr = (String) map.get(MethodCallInterface.argToAddress);

        if (TextUtils.isEmpty(txAddressStr)) {
            return null;
        }
        boolean isToken = !TextUtils.isEmpty(contractStr);

        EthTransaction ethTransaction;
        BigInteger nonce = new BigInteger(nonceStr.replace("0x", ""), 16);
        BigInteger gasLimit = new BigInteger(Objects.requireNonNull(FunctionUtil.toHexStr(gasLimitStr)).replace("0x", ""), 16);
        BigInteger gasPrice = new BigInteger(Objects.requireNonNull(FunctionUtil.gweiTohex(gasPriceStr)).replace("0x", ""), 16);
        BigInteger chainId = BigInteger.valueOf(chainIdL);
        BigInteger sendValue = new BigInteger(amountHex.replace("0x", ""), 16);


        EthAddress toModel = new EthAddress(mCointype, txAddressStr);
        if (isToken) {
            EthAddress contract = new EthAddress(mCointype, contractStr);
            CallTransaction.Function function = CallTransaction.Function.fromJsonInterface(ERC20_API.TRANSFER);
            byte[] contrastData = CallTransaction.getCallData(function, toModel.getHexString(), sendValue);
            ethTransaction = EthTransaction.create(contract.getHexString(), BigInteger.ZERO, nonce, gasPrice, gasLimit, contrastData, chainId);

            Log.d(WalletCorePlugin.TAG, "to=" + toModel.getHexString() + "_data=" + Hex.toHexString(contrastData) + "_contract=" + contract.getHexString() + "_nonce=" + nonce + "_gasLimit= " + gasLimit + "_gasPrice=" + gasPrice + "_chainId=" + chainId + "_sendValue=" + sendValue);

        } else {
            ethTransaction = EthTransaction.create(toModel.getHexString(), sendValue, nonce, gasPrice, gasLimit, null, chainId);
        }


        String rawTx2 = org.spongycastle.util.encoders.Hex.toHexString(ethTransaction.getEncodedRaw());
        Log.d("WalletCorePlugin=", rawTx2);
        return ethTransaction;
    }


    public String getErc20Data(Map<String, Object> map) {

        int decimal = 18;
        if (map.get(MethodCallInterface.argDecimal) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argDecimal);
            if (integer != null)
                decimal = integer.intValue();
        }
        String amountStr = (String) map.get(MethodCallInterface.argAmount);
        String amountHex = FunctionUtil.balanceToHex(amountStr, decimal);
        String txAddressStr = (String) map.get(MethodCallInterface.argToAddress);
        BigInteger sendValue = new BigInteger(amountHex.replace("0x", ""), 16);

        CoinType mCointype = EthereumMain.get();
        byte[] transactionBytes = null;
        EthAddress to = new EthAddress(mCointype, txAddressStr);
        String txData = "";
        CallTransaction.Function function = CallTransaction.Function.fromJsonInterface(ERC20_API.TRANSFER);
        transactionBytes = CallTransaction.getCallData(function, to.getHexString(), sendValue);
        if (transactionBytes != null && transactionBytes.length > 0) {
            txData = Numeric.toHexString(transactionBytes);
        }
        return txData;
    }


    public String getBtcBuildRawTx(Map<String, Object> map) throws Exception {
        Log.d(WalletCorePlugin.TAG, map.toString());

        List<String> rawTxs = getBtcWaitingSignatureList(map, true);

        String addressPublicKey = (String) map.get(MethodCallInterface.argAddressPublicKey);

        List<byte[]> signDataByteArray = (List<byte[]>) map.get(MethodCallInterface.argSignList);

        int slip44 = 1;
        if (map.get(MethodCallInterface.argSlip44) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argSlip44);
            if (integer != null)
                slip44 = integer;
        }

        CoinType cointype = CoreCoinBase.getInstance().getCoinTypeBySlip44Id(slip44);
        List<String> signHashSource = new ArrayList<>();
        for (byte[] byteArray : signDataByteArray) {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArray);
            int count = inputStream.read();
            for (int j = 0; j < count; j++) {
                int hashLen = inputStream.read();
                byte[] value = new byte[hashLen];
                inputStream.read(value);
                signHashSource.add(org.spongycastle.util.encoders.Hex.toHexString(value));
            }
        }

        byte[] txBytes = Utils.HEX.decode(rawTxs.get(0));
        Transaction transaction = new Transaction(cointype, txBytes);

        for (int i = 0; i < transaction.getInputs().size(); i++) {
            String signhash;
            signhash = signHashSource.get(i);
            String rsign = signhash.substring(0, 64);
            String ssign = signhash.substring(64, 128);
            BigInteger r = new BigInteger(rsign, 16);
            BigInteger s = new BigInteger(ssign, 16);
            ECKey.ECDSASignature signature = new ECKey.ECDSASignature(r, s);
            TransactionInput input = transaction.getInputs().get(i);
            TransactionSignature txSig = new TransactionSignature(signature, transaction.getSigHashType(Transaction.SigHash.ALL, false));
            input.setScriptSig(ScriptBuilder.createInputScript(txSig, ECKey.fromPublicOnly(org.spongycastle.util.encoders.Hex.decode(addressPublicKey))));

        }

        String result = org.spongycastle.util.encoders.Hex.toHexString(transaction.bitcoinSerialize());
        return result;
    }


    public String getBitcoinSeriesFeeOrBestUtxos(Map<String, Object> map) throws Exception {
        Log.d(WalletCorePlugin.TAG, map.toString());
        List<Map<String, Object>> utxoList = (List<Map<String, Object>>) map.get(MethodCallInterface.argUtxoList);
        String fromAddress = (String) map.get(MethodCallInterface.argFromAddress);
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);
        String amount = (String) map.get(MethodCallInterface.argAmount);
        String chain = (String) map.get(MethodCallInterface.argChain);
        String btcSatB = (String) map.get(MethodCallInterface.argBtcSatB);
        BigDecimal feeValue = new BigDecimal(btcSatB).multiply(new BigDecimal("1000"));
        String availableBalance = (String) map.get(MethodCallInterface.argAvailableBalance);

        if (TextUtils.isEmpty(amount)) {
            amount = "0";
        }

        if (TextUtils.isEmpty(availableBalance)) {
            availableBalance = "0";
        }


        if (TextUtils.isEmpty(toAddress)) {
            toAddress = "";
        }


        CoinType mCoinType = CoreCoinBase.getInstance().getCoinTypeByChain(chain);

        long sendValueLong = CoinUtil.getCoinValueBtcToSatoshi(amount, mCoinType.getUnitExponent()).longValue();


        List<BtcSignRequestOutput> outputList = new ArrayList<>();


        BtcSignRequestOutput data = new BtcSignRequestOutput();
        data.setTo(toAddress);
        data.setAmount(sendValueLong);
        outputList.add(data);


        List<UtxoModel> totalAllUtxoList = new ArrayList<>();

        for (Map<String, Object> utxo : utxoList) {
            String txOutIndex = (String) utxo.get("tx_out_index");
            String txHash = (String) utxo.get("tx_hash");
            String sourceValue = (String) utxo.get("sourceValue");
            String script = (String) utxo.get("script");
            UtxoModel utxoModel = new UtxoModel();
            utxoModel.setScript(script);
            utxoModel.setValue(Long.parseLong(sourceValue));
            utxoModel.setTxHash(txHash);
            utxoModel.setTxOutIndex(Integer.valueOf(txOutIndex));
            totalAllUtxoList.add(utxoModel);
        }

        BaseSendRequest mBaseSendRequest = BtcTxEngine.getBitTxSignMsg(mCoinType, fromAddress, new BigDecimal(availableBalance), new BigDecimal(sendValueLong + ""), feeValue, outputList, totalAllUtxoList);


        // 创建主 JSON 对象并添加费用信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fee", mBaseSendRequest.getFee());

        // 创建输入数组
        JSONArray inputs = createInputsJsonArray(mBaseSendRequest.getIn());
        jsonObject.put("inputs", inputs);

        // 创建输出数组
        JSONArray outputs = createOutputsJsonArray(mBaseSendRequest.getOut());
        jsonObject.put("outputs", outputs);

        // 将 JSON 对象转换为字符串并记录日志
        String utxoJsons = jsonObject.toString();
        Log.d(WalletCorePlugin.TAG, "utxoJson=" + utxoJsons);
        return utxoJsons;

    }

    private JSONArray createInputsJsonArray(List<BtcSignRequestInput> inputsList) throws Exception {
        JSONArray inputs = new JSONArray();
        for (BtcSignRequestInput input : inputsList) {
            JSONObject inputJson = new JSONObject();
            inputJson.put("pre_hash", input.getPreTx()); // Utxo 的 pre tx id
            inputJson.put("pre_index", input.getPreN()); // preIndex
            inputJson.put("script", input.getScript());
            inputJson.put("value", input.getAmount());
            inputs.put(inputJson);
        }
        return inputs;
    }

    private JSONArray createOutputsJsonArray(List<BtcSignRequestOutput> outputsList) throws Exception {
        JSONArray outputs = new JSONArray();
        for (BtcSignRequestOutput output : outputsList) {
            JSONObject outputJson = new JSONObject();
            outputJson.put("address", output.getTo());
            outputJson.put("value", output.getAmount());
            outputs.put(outputJson);
        }
        return outputs;
    }


    /**
     * 构建BTC签名hash 和 RawTX
     */
    public List<String> getBtcWaitingSignatureList(Map<String, Object> map, boolean isRawTx) {
        Log.d(WalletCorePlugin.TAG, map.toString());

        List<Map<String, Object>> utxoList = (List<Map<String, Object>>) map.get(MethodCallInterface.argUtxoList);
        String fromAddress = (String) map.get(MethodCallInterface.argFromAddress);
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);
        String amount = (String) map.get(MethodCallInterface.argAmount);
        String btcSatB = (String) map.get(MethodCallInterface.argBtcSatB);
        BigDecimal feeValue = new BigDecimal(btcSatB).multiply(new BigDecimal("1000"));
        String availableBalance = (String) map.get(MethodCallInterface.argAvailableBalance);

        int slip44 = 1;
        if (map.get(MethodCallInterface.argSlip44) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argSlip44);
            if (integer != null)
                slip44 = integer;
        }

        CoinType mCoinType = CoreCoinBase.getInstance().getCoinTypeBySlip44Id(slip44);

        long sendValueLong = CoinUtil.getCoinValueBtcToSatoshi(amount, mCoinType.getUnitExponent()).longValue();


        List<BtcSignRequestOutput> outputList = new ArrayList<>();


        BtcSignRequestOutput data = new BtcSignRequestOutput();
        data.setTo(toAddress);
        data.setAmount(sendValueLong);
        outputList.add(data);

        List<UtxoModel> totalAllUtxoList = new ArrayList<>();

        for (Map<String, Object> utxo : utxoList) {
            String txOutIndex = (String) utxo.get("tx_out_index");
            String txHash = (String) utxo.get("tx_hash");
            String sourceValue = (String) utxo.get("sourceValue");
            String script = (String) utxo.get("script");
            UtxoModel utxoModel = new UtxoModel();
            utxoModel.setScript(script);
            utxoModel.setValue(Long.parseLong(sourceValue));
            utxoModel.setTxHash(txHash);
            utxoModel.setTxOutIndex(Integer.valueOf(txOutIndex));
            totalAllUtxoList.add(utxoModel);
        }

        BaseSendRequest mBaseSendRequest = BtcTxEngine.getBitTxSignMsg(mCoinType, fromAddress, new BigDecimal(availableBalance), new BigDecimal(sendValueLong + ""), feeValue, outputList, totalAllUtxoList);

        Transaction tx = new Transaction(mCoinType);
        List<BtcSignRequestInput> inputList = mBaseSendRequest.getIn();
        List<BtcSignRequestOutput> outList = mBaseSendRequest.getOut();

        for (int i = 0; i < inputList.size(); i++) {
            BtcSignRequestInput requestInput = inputList.get(i);
            Sha256Hash preTxhash = Sha256Hash.wrap(requestInput.getPreTx());
            TransactionOutPoint outPoint = new TransactionOutPoint(mCoinType, requestInput.getPreN(), preTxhash);
            Coin coin = Coin.valueOf(requestInput.getAmount());
            TransactionInput input = new TransactionInput(mCoinType, null, org.spongycastle.util.encoders.Hex.decode(""), outPoint, coin);
            tx.addInput(input);
        }


        for (int i = 0; i < outList.size(); i++) {
            if (outList.get(i).getType() == 0) {
                BtcSignRequestOutput requestOutput = outList.get(i);
                Coin coin = Coin.valueOf(requestOutput.getAmount());
                try {
                    Address address = Address.fromString(mCoinType, requestOutput.getTo());
                    TransactionOutput output = new TransactionOutput(mCoinType, null, coin, address);
                    tx.addOutput(output);
                } catch (Exception ignored) {
                }

            }

        }

        List<String> hashs = new ArrayList<>();
        for (int i = 0; i < tx.getInputs().size(); i++) {
            try {
                Sha256Hash hash;
                Script script = ScriptBuilder.createOutputScript((Address) mCoinType.newAddress(fromAddress));
                hash = tx.hashForSignature(i, script, Transaction.SigHash.ALL, false);
                hashs.add(hash.toString());
            } catch (AddressMalformedException e) {
                e.printStackTrace();

            }

        }
        if (isRawTx) {
            String rawTx = org.spongycastle.util.encoders.Hex.toHexString(tx.bitcoinSerialize());
            List<String> rawTxs = new ArrayList<>();
            rawTxs.add(rawTx);
            return rawTxs;
        }
        return hashs;
    }


    public boolean verifyRawDataHex(Map<String, Object> map) {
        String rawDataHex = (String) map.get(MethodCallInterface.argRawDataHex);
        String fromAddress = (String) map.get(MethodCallInterface.argFromAddress);
        String toAddress = (String) map.get(MethodCallInterface.argToAddress);
        String contractAddress = (String) map.get(MethodCallInterface.argContract);
        String amount = (String) map.get(MethodCallInterface.argAmount);
        int decimal = 0;
        if (map.get(MethodCallInterface.argDecimal) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argDecimal);
            if (integer != null)
                decimal = integer.intValue();
        }


        boolean isVerify = false;

        String rawData;
        String to;
        String from;
        String balance;
        BigInteger bigInteger;
        try {
            String result = ColdLarLibTron.getRawDataHexUnpackToJsonData(rawDataHex);

            rawData = new JSONObject(result).getString("raw_data");

            JSONObject rawDataJson1 = new JSONObject(rawData);
            JSONObject transfer1 = rawDataJson1.getJSONArray("contract").getJSONObject(0);
            String type = transfer1.getString("type");
            JSONObject valueObj = transfer1.getJSONObject("parameter").getJSONObject("value");
            from = valueObj.getString("owner_address");
            from = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(from)).getString("address");

            switch (type) {
                case Constant.TrxType.TRANSFER:
                case Constant.TrxType.TRC_10:
                    to = valueObj.getString("to_address");
                    to = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(to)).getString("address");
                    bigInteger = new BigInteger(valueObj.optString("amount"));
                    balance = FunctionUtil.toAmountString(bigInteger, decimal);

                    if (NumberUtil.isNumeric(balance)) {
                        if (from.equals(fromAddress) && to.equals(toAddress) && BigDecimalUtils.isEquals(amount, balance)) {
                            isVerify = true;
                        }
                    } else {
                        if (from.equals(fromAddress) && to.equals(toAddress)) {
                            isVerify = true;
                        }

                    }

                    break;

                case Constant.TrxType.TRC_20:

                    String contract = valueObj.getString("contract_address");
                    contract = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(contract)).getString("address");

                    String data = valueObj.getString("data");
                    to = data.substring(8, 72);
                    to = org.bouncycastle.util.encoders.Hex.toHexString(new BigInteger(to, 16).toByteArray());
                    to = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(to)).getString("address");
                    String amountStr = data.substring(72);
                    bigInteger = new BigInteger(amountStr, 16);
                    balance = FunctionUtil.toAmountString(bigInteger, decimal);

                    if (NumberUtil.isNumeric(balance)) {
                        isVerify = from.equals(fromAddress) && to.equals(toAddress) && BigDecimalUtils.isEquals(amount, balance) && contract.equalsIgnoreCase(contractAddress);
                    } else {
                        isVerify = from.equals(fromAddress) && to.equals(toAddress) && contract.equalsIgnoreCase(contractAddress);
                    }

                    break;
                case Constant.TrxType.FROZEN_V2:
                    bigInteger = new BigInteger(valueObj.optString("frozen_balance"));
                    balance = FunctionUtil.toAmountString(bigInteger, decimal);
                    if (NumberUtil.isNumeric(balance)) {
                        isVerify = from.equals(fromAddress) && BigDecimalUtils.isEquals(amount, balance);
                    } else {
                        isVerify = from.equals(fromAddress);

                    }

                    break;

                case Constant.TrxType.UNFROZEN:

                    String receiverAddress = valueObj.optString("receiver_address");
                    if (TextUtils.isEmpty(receiverAddress)) {
                        if (from.equals(fromAddress)) {
                            isVerify = true;
                        }
                    } else {
                        receiverAddress = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(receiverAddress)).getString("address");
                        if (receiverAddress.equals(toAddress)) {
                            isVerify = true;
                        }
                    }


                    break;
                case Constant.TrxType.UNFROZEN_V2:
                    bigInteger = new BigInteger(valueObj.optString("unfreeze_balance"));
                    balance = FunctionUtil.toAmountString(bigInteger, decimal);
                    if (NumberUtil.isNumeric(balance)) {
                        isVerify = from.equals(fromAddress) && BigDecimalUtils.isEquals(amount, balance);
                    } else {
                        isVerify = from.equals(fromAddress);

                    }

                    break;
                case Constant.TrxType.WithdrawExpireUnfreeze:
                case Constant.TrxType.VOTE:
                    isVerify = from.equals(fromAddress);
                    break;

                case Constant.TrxType.DelegateResourceContract:
                case Constant.TrxType.UnDelegateResourceContract:
                    bigInteger = new BigInteger(valueObj.optString("balance"));
                    balance = FunctionUtil.toAmountString(bigInteger, decimal);
                    receiverAddress = valueObj.optString("receiver_address");
                    to = new JSONObject(ColdLarLibTron.getAddressHexToAddressStr(receiverAddress)).getString("address");
                    if (NumberUtil.isNumeric(balance)) {
                        isVerify = from.equals(fromAddress) && BigDecimalUtils.isEquals(amount, balance) && to.equals(toAddress);
                    } else {
                        isVerify = from.equals(fromAddress) && to.equals(toAddress);

                    }

                    break;

            }

        } catch (Exception exception) {
            isVerify = false;
        }


        return isVerify;

    }

    public String getTronBuildRawTx(Map<String, Object> map) throws Exception {

        String rawTx = (String) map.get(MethodCallInterface.argRawDataHex);
        String rawData = (String) map.get(MethodCallInterface.argRawData);
        List<byte[]> signDataByteArray = (List<byte[]>) map.get(MethodCallInterface.argSignList);

        List<String> signHashSource = new ArrayList<>();
        for (byte[] byteArray : signDataByteArray) {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArray);
            int count = inputStream.read();
            for (int j = 0; j < count; j++) {
                int hashLen = inputStream.read();
                byte[] value = new byte[hashLen];
                inputStream.read(value);
                signHashSource.add(org.spongycastle.util.encoders.Hex.toHexString(value));
            }
        }
        String signature = signHashSource.get(0);
        String hash = org.spongycastle.util.encoders.Hex.toHexString(sha256(org.spongycastle.util.encoders.Hex.decode(rawTx)));
        JSONObject rawTransaction = new JSONObject();
        JSONObject rawDataTransaction = new JSONObject(rawData);
        rawTransaction.put("raw_data_hex", rawTx);
        rawTransaction.put("txID", hash);
        rawTransaction.put("signature", signature);
        rawTransaction.put("raw_data", rawDataTransaction);
        rawTransaction.put("visible", false);
        return rawTransaction.toString();
    }


    public String trc20Parameter(Map<String, Object> map) throws Exception {

        String toAddress = (String) map.get(MethodCallInterface.argToAddress);
        String amount = (String) map.get(MethodCallInterface.argAmount);
        int decimal = 0;
        if (map.get(MethodCallInterface.argDecimal) != null) {
            Integer integer = (Integer) map.get(MethodCallInterface.argDecimal);
            if (integer != null)
                decimal = integer.intValue();
        }
        String TRANSFER_METHOD = "transfer(address,uint256)";
        String transferParams = "\"" + toAddress + "\"," + CoinUtil.getCoinValueBtcToSatoshi(amount, decimal);
        return TronAbiUtil.parseMethod(TRANSFER_METHOD, transferParams);
    }

    public String parseEthereumTransactionData(String rawData) throws Exception {
        EthTransactionInfo ethTransactionInfo = EthParseUtil.parseEIP155(rawData);

        return new Gson().toJson(ethTransactionInfo);
    }

    public String parseBitcoinTransactionData(String data, String chain, int hasChangeZero) throws Exception {
        Log.d(WalletCorePlugin.TAG, "parseBitcoinTransactionData");

        CoinType cointype = CoreCoinBase.getInstance().getCoinTypeByChain(chain);

        if (cointype == null) {
            return null;
        }


        byte[] txBytes = Utils.HEX.decode(data);
        Transaction transaction = new Transaction(cointype, txBytes);

        List<TransactionOutput> outputs = transaction.getOutputs();
        JSONArray toJsonArray = new JSONArray();

        for (TransactionOutput output : outputs) {

            toJsonArray.put(BitAddressUtils.getOutAddress(cointype, output));

        }

        JSONObject jsonObject = new JSONObject();

        Double outValue = getOutValue(cointype, outputs, hasChangeZero);
        jsonObject.put("toList", toJsonArray);
        jsonObject.put("value", NumberUtil.setFormate(outValue + "")); // 使用总转账金额


        return jsonObject.toString();


    }

    private double getOutValue(CoinType cointype, List<TransactionOutput> listOutput, int hasChangeZero) {
        double totalvalue = 0;
        if (listOutput != null && listOutput.size() > 0) {
            String key;
            String outadd = BitAddressUtils.getOutAddress(cointype, listOutput.get(0));
            int size = 0;
            for (int i = 0; i < listOutput.size(); i++) {
                key = listOutput.get(i).toString();
                String args[] = key.split(" ");
                String amount = args[2];
                String address = BitAddressUtils.getOutAddress(cointype, listOutput.get(i));
                double value = Double.parseDouble(amount);
                if (hasChangeZero != 0) {
                    if (i < listOutput.size() - 1) {
                        totalvalue = totalvalue + value;
                    }
                } else {
                    totalvalue = totalvalue + value;
                }
                if (address.equals(outadd)) {
                    size++;
                }
            }
        }
        return totalvalue;
    }


    public String getEosTransactionOrSignHash(Map<String, Object> map) throws Exception {

        String action = (String) map.get(MethodCallInterface.argAction);
        String accountContract = (String) map.get(MethodCallInterface.argAccountContract);
        String from = (String) map.get(MethodCallInterface.argFrom);
        String to = (String) map.get(MethodCallInterface.argTo);
        String amount = (String) map.get(MethodCallInterface.argAmount);
        String symbol = (String) map.get(MethodCallInterface.argSymbol);
        String memo = (String) map.get(MethodCallInterface.argMemo);

        String permission = (String) map.get(MethodCallInterface.argPermission);
;
        String chainId = (String) map.get(MethodCallInterface.argChainId);
        String headBlockId = (String) map.get(MethodCallInterface.argHeadBlockId);
        String headBlockTime = (String) map.get(MethodCallInterface.argHeadBlockTime);

        EosChainInfo info = new EosChainInfo();
        info.setHeadBlockId(headBlockId);
        info.setHeadBlockTime(TimeUtil.getEosUtcTime(Long.valueOf(headBlockTime)));
        info.setChain_id(chainId);

        SignedTransaction signedTransaction = EosUtil.createTransaction(accountContract, action, ""
                , EosUtil.getActivePermission(from), info);

        // EOS交易过期时间

        int EOS_ACTION_DEADLING = 1800000;
        signedTransaction.setReferenceBlock(info.getHeadBlockId());
        signedTransaction.setExpiration(info.getTimeAfterHeadBlockTime(EOS_ACTION_DEADLING));


        int blockNum = new BigInteger(1, HexUtils.toBytes(headBlockId.substring(0, 8))).intValue();
        long blockPrefix = new BigInteger(1, HexUtils.toBytesReversed(headBlockId.substring(16, 24))).longValue();
        long expiration = TimeUtil.eosDataToLong(signedTransaction.getExpiration());


        com.coldlar.coin.eos.transaction.Transaction transaction = SignEosTxPresenter.transfer( accountContract, from , to, permission, memo, amount, symbol, action, chainId, blockNum, blockPrefix, expiration);

        JSONObject jsonObject = new JSONObject();

        jsonObject.put("transaction", transaction.toJsonString());

        jsonObject.put("signHash", transaction.getSignHash());

        return jsonObject.toString();

    }




}
