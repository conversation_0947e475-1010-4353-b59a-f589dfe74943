package com.coldlar.coin.atom;


import com.coldlar.coin.CoinType;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import org.cosmosj.CosmosUtil;

import java.math.BigDecimal;
import java.util.Map;


public class BabylonTestNetMain extends CoinType {
    private BabylonTestNetMain() {
        addressHeader = 0;
        p2shHeader = 5;
        dumpedPrivateKeyHeader = 128;
        segwitAddressHrp = "bbn";
        coinID = ColdlarCoinID.ID_TBaby;
        cnName = "tBABY";
        trName = "tBABY";
        chain ="tbaby";
        symbol = "tBABY";
        showSymbol= "tBABY";
        name = "Babylon TestNet Chain";
        bip44Index = 118;
        unitExponent = 6;
        feeValue = new BigDecimal("10000");
        confirm = 6;
        coinOrder=14;
    }


    private static BabylonTestNetMain instance = new BabylonTestNetMain();
    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }



    @Override
    public boolean validateAddress(String address) {
        return CosmosUtil.validateAddress(address);
    }
    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof CosmosMain) {
            return true;
        } else {
            return false;
        }
    }

}
