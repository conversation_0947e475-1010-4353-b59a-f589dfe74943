package com.coldlar.coin.nem.core.utils;

/**
 * Created by 29343 on 2017/10/25.
 */

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class NemPBKDF2SHA512 {
    public NemPBKDF2SHA512() {
    }

    public static byte[] derive(String P, String S, int c, int dkLen) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            byte baDerived = 20;
            if((double)dkLen > (Math.pow(2.0D, 32.0D) - 1.0D) * (double)baDerived) {
                throw new IllegalArgumentException("derived key too long");
            }

            int l = (int) Math.ceil((double)dkLen / (double)baDerived);

            for(int i = 1; i <= l; ++i) {
                byte[] T = F(P, S, c, i);
                baos.write(T);
            }
        } catch (Exception var9) {
            throw new RuntimeException(var9);
        }

        byte[] var10 = new byte[dkLen];
        System.arraycopy(baos.toByteArray(), 0, var10, 0, var10.length);
        return var10;
    }

    private static byte[] F(String P, String S, int c, int i) throws Exception {
        byte[] U_LAST = null;
        byte[] U_XOR = null;
        SecretKeySpec key = new SecretKeySpec(P.getBytes("UTF-8"), "HmacSHA512");
        Mac mac = Mac.getInstance(key.getAlgorithm());
        mac.init(key);

        for(int j = 0; j < c; ++j) {
            byte[] baU;
            if(j == 0) {
                baU = S.getBytes("UTF-8");
                byte[] var12 = INT(i);
                byte[] baU1 = new byte[baU.length + var12.length];
                System.arraycopy(baU, 0, baU1, 0, baU.length);
                System.arraycopy(var12, 0, baU1, baU.length, var12.length);
                U_XOR = mac.doFinal(baU1);
                U_LAST = U_XOR;
                mac.reset();
            } else {
                baU = mac.doFinal(U_LAST);
                mac.reset();

                for(int k = 0; k < U_XOR.length; ++k) {
                    U_XOR[k] ^= baU[k];
                }

                U_LAST = baU;
            }
        }

        return U_XOR;
    }

    private static byte[] INT(int i) {
        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.order(ByteOrder.BIG_ENDIAN);
        bb.putInt(i);
        return bb.array();
    }
}
