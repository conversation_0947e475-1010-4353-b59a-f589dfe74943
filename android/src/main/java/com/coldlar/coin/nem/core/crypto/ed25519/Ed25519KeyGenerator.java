package com.coldlar.coin.nem.core.crypto.ed25519;

import com.coldlar.coin.nem.core.crypto.KeyGenerator;
import com.coldlar.coin.nem.core.crypto.KeyPair;
import com.coldlar.coin.nem.core.crypto.PrivateKey;
import com.coldlar.coin.nem.core.crypto.PublicKey;
import com.coldlar.coin.nem.core.crypto.ed25519.arithmetic.Ed25519EncodedFieldElement;
import com.coldlar.coin.nem.core.crypto.ed25519.arithmetic.Ed25519Group;
import com.coldlar.coin.nem.core.crypto.ed25519.arithmetic.Ed25519GroupElement;
import com.coldlar.coin.nem.core.utils.ArrayUtils;
import com.coldlar.coin.nem.core.utils.NemPBKDF2SHA512;

import java.security.SecureRandom;

/**
 * Implementation of the key generator for Ed25519.
 */
public class Ed25519KeyGenerator implements KeyGenerator {
	private final SecureRandom random;

	public Ed25519KeyGenerator() {
		this.random = new SecureRandom();
	}

	@Override
	public KeyPair generateKeyPair(String seedstr) {

		final byte[] seedByte = NemPBKDF2SHA512.derive(seedstr,"coldlar",2048,32);
//		final byte[] seedByte = new byte[32];
//		this.random.nextBytes(seedByte);
		// seed is the private key.
		final PrivateKey privateKey = new PrivateKey(ArrayUtils.toBigInteger(seedByte));

		return new KeyPair(privateKey);
	}

	@Override
	public PublicKey derivePublicKey(final PrivateKey privateKey) {
		final Ed25519EncodedFieldElement a = Ed25519Utils.prepareForScalarMultiply(privateKey);

		// a * base point is the public key.
		final Ed25519GroupElement pubKey = Ed25519Group.BASE_POINT.scalarMultiply(a);

		// verification of signatures will be about twice as fast when pre-calculating
		// a suitable table of group elements.
		return new PublicKey(pubKey.encode().getRaw());
	}
}
