package com.coldlar.coin.nem.core.crypto.ed25519;


import com.coldlar.coin.nem.core.crypto.BlockCipher;
import com.coldlar.coin.nem.core.crypto.CryptoEngine;
import com.coldlar.coin.nem.core.crypto.Curve;
import com.coldlar.coin.nem.core.crypto.DsaSigner;
import com.coldlar.coin.nem.core.crypto.KeyAnalyzer;
import com.coldlar.coin.nem.core.crypto.KeyGenerator;
import com.coldlar.coin.nem.core.crypto.KeyPair;

/**
 * Class that wraps the Ed25519 specific implementation.
 */
public class Ed25519CryptoEngine implements CryptoEngine {

	@Override
	public Curve getCurve() {
		return Ed25519Curve.ed25519();
	}

	@Override
	public DsaSigner createDsaSigner(final KeyPair keyPair) {
		return new Ed25519DsaSigner(keyPair);
	}

	@Override
	public KeyGenerator createKeyGenerator() {
		return new Ed25519KeyGenerator();
	}

	@Override
	public BlockCipher createBlockCipher(final KeyPair senderKeyPair, final KeyPair recipientKeyPair) {
		return new Ed25519BlockCipher(sender<PERSON><PERSON>P<PERSON>, recipientKeyPair);
	}

	@Override
	public KeyAnalyzer createKeyAnalyzer() {
		return new Ed25519KeyAnalyzer();
	}
}
