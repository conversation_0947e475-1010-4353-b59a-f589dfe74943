package com.coldlar.coin.nem.nac.models.transactions.drafts;

//public final class AggregateModificationTransactionDraft extends AbstractTransactionDraft {
//
//    private static final int VERSION = 2;
//
//    public final int minCosignatoriesRelativeChange;
//    private final Set<AggregateModification> _modifications = new HashSet<>();
//
//    public AggregateModificationTransactionDraft(final NacPublicKey signer,
//                                                 @NonNull final Collection<? extends NacPublicKey> cosignatoriesToAdd, final NacPublicKey cosignatoryToRemove,
//                                                 final int minCosignatoriesRelativeChange) {
//        super(VERSION, signer);
//        AssertUtils.notNull(cosignatoriesToAdd);
//        this.minCosignatoriesRelativeChange = minCosignatoriesRelativeChange;
//        final List<AggregateModification> addModifications = Stream.of(cosignatoriesToAdd)
//                .map(x -> new AggregateModification(MultisigCosignatoryModificationType.ADD_NEW_COSIGNATORY, x))
//                .collect(Collectors.toList());
//        _modifications.addAll(addModifications);
//        if (cosignatoryToRemove != null) {
//            _modifications.add(new AggregateModification(MultisigCosignatoryModificationType.DELETE_EXISTING_COSIGNATORY, cosignatoryToRemove));
//        }
//    }
//
//    @NonNull
//    @Override
//    public TransactionType getType() {
//        return TransactionType.MULTISIG_AGGREGATE_MODIFICATION_TRANSACTION;
//    }
//
//    @NonNull
//    @Override
//    public Xems calculateMinimumFee() {
//        final Xems fee = new Xems();
//        final int modificationsCount = _modifications.size();
//        final boolean minCosigsModificationPresent = this.minCosignatoriesRelativeChange != 0;
//        fee.addXems(10 + 6 * modificationsCount);
//        if (minCosigsModificationPresent) {
//            fee.addXems(6);
//        }
//        return fee;
//    }
//
//    @Override
//    protected void serializeAdditional(@NonNull final ByteArrayOutputStream os)
//            throws IOException {
//        // Number of cosignatory modifications
//        final int modificationsNumber = _modifications.size();
//        writeAsLeBytes(os, modificationsNumber);
//        final List<AggregateModification> sortedModifications = Stream.of(_modifications)
//                .sorted()
//                .collect(Collectors.toList());
//        // Modifications
//        for (AggregateModification mod : sortedModifications) {
//            mod.serialize(os);
//        }
//        writeAsLeBytes(os, minCosignatoriesRelativeChange != 0 ? SizeOf.INT : 0);
//        if (minCosignatoriesRelativeChange != 0) {
//            writeAsLeBytes(os, minCosignatoriesRelativeChange);
//        }
//        // Note: BloodyRookie:
//        // It should be possible to leave out the min cosignatories part in case there is no change for min cosignatories
//        // (leave out both, the 'Length" field and the 'relative change' field).
//        // Note 2(2015/08/17): Stopped working, gives "signature not verifiable", reverted to tested behaviour.
//    }
//}
