package com.coldlar.coin.nem.nac.helpers;

import androidx.annotation.NonNull;

import com.coldlar.coin.nem.core.crypto.KeyPair;
import com.coldlar.coin.nem.core.crypto.ed25519.Ed25519BlockCipher;
import com.coldlar.coin.nem.nac.models.BinaryData;
import com.coldlar.coin.nem.nac.models.NacPrivateKey;
import com.coldlar.coin.nem.nac.models.NacPublicKey;

public final class Ed25519Helper {

    public static BinaryData Ed25519BlockCipherEncrypt(
            @NonNull final BinaryData input,
            @NonNull final NacPrivateKey account1, @NonNull final NacPublicKey account2) {
        final KeyPair senderKeyPair = new KeyPair(account1.toPrivateKey());
        final KeyPair recipientKeyPair = new KeyPair(null, account2.toPublicKey());
        final byte[] inputBytes = input.getRaw();
        final byte[] encryptedBytes = new Ed25519BlockCipher(sender<PERSON><PERSON><PERSON><PERSON>, recipientKeyPair).encrypt(inputBytes);
        return new BinaryData(encryptedBytes);
    }
}
