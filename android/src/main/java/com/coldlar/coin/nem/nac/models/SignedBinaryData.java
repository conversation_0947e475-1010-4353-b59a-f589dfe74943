package com.coldlar.coin.nem.nac.models;

import androidx.annotation.NonNull;

import com.coldlar.coin.nem.core.crypto.DsaSigner;
import com.coldlar.coin.nem.core.utils.HexEncoder;

/**
 * Class represents immutable binary data that can be signed.
 */
public final class SignedBinaryData extends BinaryData {
	private final byte[] _signature;

	public SignedBinaryData(@NonNull final byte[] rawData, @NonNull final DsaSigner signer) {
		super(rawData);
		_signature = signer.sign(rawData).getBytes();
	}

	@NonNull
	public byte[] getSignature() {
		return _signature;
	}

	@NonNull
	public String getSignatureHex() {
		return HexEncoder.getString(_signature);
	}
}
