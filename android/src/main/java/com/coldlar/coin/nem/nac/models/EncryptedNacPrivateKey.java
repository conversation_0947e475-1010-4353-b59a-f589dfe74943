package com.coldlar.coin.nem.nac.models;

import android.os.Parcel;

import androidx.annotation.NonNull;

import com.coldlar.coin.nem.nac.crypto.NacCryptoException;
import com.fasterxml.jackson.annotation.JsonCreator;

public final class EncryptedNacPrivate<PERSON><PERSON> extends EncryptedBinaryData {
	public EncryptedNacPrivateKey(@NonNull final byte[] raw) {
		super(raw);
	}

	@JsonCreator
	public EncryptedNacPrivateKey(@NonNull final String rawDataHex) {
		super(rawDataHex);
	}

	public NacPrivateKey decryptKey(@NonNull final BinaryData encryptionKey)
			throws NacCryptoException {
		final BinaryData decrypted = super.decrypt(encryptionKey);
		return new NacPrivateKey(decrypted.rawData);
	}

	//region Parcelable
	protected EncryptedNacPrivateKey(Parcel in) {
		super(in);
	}

	public static final Creator<EncryptedNacPrivateKey> CREATOR = new Creator<EncryptedNacPrivateKey>() {
		@Override
		public EncryptedNacPrivateKey createFromParcel(Parcel source) {return new EncryptedNacPrivateKey(source);}

		@Override
		public EncryptedNacPrivateKey[] newArray(int size) {return new EncryptedNacPrivateKey[size];}
	};
	//endregion
}
