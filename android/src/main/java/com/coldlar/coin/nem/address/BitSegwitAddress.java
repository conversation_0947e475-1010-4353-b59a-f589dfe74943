package com.coldlar.coin.nem.address;

import com.coldlar.coin.CoinType;

import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.NetworkParameters;
import org.bitcoinj.core.SegwitAddress;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public class BitSegwitAddress extends SegwitAddress implements AbstractAddress {


    private BitSegwitAddress(NetworkParameters params, int witnessVersion, byte[] witnessProgram) throws AddressFormatException {
        super(params, witnessVersion, witnessProgram);
    }

    public static BitSegwitAddress from(CoinType params, ECKey key) {
        return new BitSegwitAddress(params, 0, key.getPubKeyHash());
    }

    @Override
    public String toString() {
        return super.toBech32();
    }

    @Override
    public CoinType getType() {
        return (CoinType) params;
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }
}
