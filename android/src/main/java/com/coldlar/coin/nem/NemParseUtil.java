package com.coldlar.coin.nem;


import com.coldlar.coin.nem.model.NemTransactionInfo;
import com.coldlar.coin.nem.nac.models.NacPublicKey;
import com.coldlar.coin.nem.nac.models.primitives.AddressValue;
import com.coldlar.core.util.CoinUtil;

import org.ripple.bouncycastle.util.encoders.Hex;

import java.net.ProtocolException;

/**
 * author wang<PERSON><PERSON>
 * date 2018/11/2 17:46
 * description Nem 解析工具类
 */
public class NemParseUtil {

    public static final int MAX_SIZE = 0x02000000; // 32MB

    /**
     * 解析交易
     *
     * @param rawTx  交易数据
     * @throws ProtocolException
     */
    public static NemTransactionInfo parseTx(String rawTx) throws ProtocolException {

        NemTransactionInfo info = new NemTransactionInfo();
        int cursor = 0;
        byte[] txData = Hex.decode(rawTx);
        long txType = readUint32(txData, cursor);
        info.setTxType(txType);
        cursor += 4;
        long txVersion = readUint32(txData, cursor);
        info.setTxVersion(txVersion);
        cursor += 4;
        long timestamp = readUint32(txData, cursor);
        info.setTimestamp(timestamp);
        cursor += 4;
        int pubkeyLen = (int) readUint32(txData, cursor);
        info.setPubkeyLen(pubkeyLen);
        cursor += 4;
        byte[] pubkey = readBytes(txData, cursor, pubkeyLen);
        info.setPubkeyByte(pubkey);
        NacPublicKey publicKey = new NacPublicKey(pubkey);
        info.setPubkey(pubkey.toString());
        String address = AddressValue.fromPublicKey(publicKey).toString();
        info.setFromAdress(address);
        cursor += pubkeyLen;
        long fee = readInt64(txData, cursor);
       String sFee  = CoinUtil.getCoinValueSatoshiToBtc(fee,NemMain.get().getUnitExponent()).toPlainString();
        info.setFee(sFee);
        cursor += 8;
        long deadline = readUint32(txData, cursor);
        info.setDeadline(deadline);
        cursor += 4;
        int receiptLen = (int) readUint32(txData, cursor);
        info.setReceiptLen(receiptLen);
        cursor += 4;
        byte[] receipt = readBytes(txData, cursor, receiptLen);
        info.setToAddressByte(receipt);
        String receive = new String(receipt);
        info.setToAddress(receive);
        cursor += receiptLen;
        long amount = readInt64(txData, cursor);
        String samount  = CoinUtil.getCoinValueSatoshiToBtc(amount,NemMain.get().getUnitExponent()).toPlainString();

        info.setAmount(samount);
        cursor += 8;
        //判断是否有message
        if (txData.length - cursor > 3) {//表示有message
//            long msgFiledLen = readUint32(txData, cursor);
//            cursor += 4;
//            long msgType = readUint32(txData, cursor);
//            cursor += 4;
            int msgLen = (int) readUint32(txData, cursor);
            cursor += 4;
            byte[] msg = readBytes(txData, cursor, msgLen);
            String msgStr = new String(msg);
            info.setRemark(msgStr);
            System.out.println(msgStr);
        }
        return info;
    }


    public static long readUint32(byte[] bytes, int offset) {
        return (bytes[offset] & 0xffL) |
                ((bytes[offset + 1] & 0xffL) << 8) |
                ((bytes[offset + 2] & 0xffL) << 16) |
                ((bytes[offset + 3] & 0xffL) << 24);
    }


    public static long readInt64(byte[] bytes, int offset) {
        return (bytes[offset] & 0xffL) |
                ((bytes[offset + 1] & 0xffL) << 8) |
                ((bytes[offset + 2] & 0xffL) << 16) |
                ((bytes[offset + 3] & 0xffL) << 24) |
                ((bytes[offset + 4] & 0xffL) << 32) |
                ((bytes[offset + 5] & 0xffL) << 40) |
                ((bytes[offset + 6] & 0xffL) << 48) |
                ((bytes[offset + 7] & 0xffL) << 56);
    }


    public static byte[] readBytes(byte[] payload, int cursor, int length) throws ProtocolException {
        if (length > MAX_SIZE) {
            throw new ProtocolException("Claimed byte array length too large: " + length);
        }
        byte[] b = new byte[length];
        try {
            System.arraycopy(payload, cursor, b, 0, length);
        } catch (ArrayIndexOutOfBoundsException e) {
            throw new ProtocolException("info length error ");
        }
        return b;
    }
}
