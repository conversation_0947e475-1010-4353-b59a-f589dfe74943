package com.coldlar.coin.nem.core.crypto.ed25519;

import com.coldlar.coin.nem.core.crypto.KeyAnalyzer;
import com.coldlar.coin.nem.core.crypto.PublicKey;

/**
 * Implementation of the key analyzer for Ed25519.
 */
public class Ed25519KeyAnalyzer implements KeyAnalyzer {
	private final static int COMPRESSED_KEY_SIZE = 32;

	@Override
	public boolean isKeyCompressed(final PublicKey publicKey) {
		return COMPRESSED_KEY_SIZE == publicKey.getRaw().length;
	}
}
