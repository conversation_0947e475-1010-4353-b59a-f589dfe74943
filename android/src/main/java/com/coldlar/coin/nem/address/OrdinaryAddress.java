package com.coldlar.coin.nem.address;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.ethereum.transaction.HashUtil;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */
public final class OrdinaryAddress implements AbstractAddress {
    private final String address;
    private final CoinType type;

    public OrdinaryAddress(CoinType paramCoinType, String paramString) {
        this.type = paramCoinType;
        this.address = paramString;
    }

    private byte[] getHash160() {
        return HashUtil.ripemd160(this.address.getBytes(Charset.forName("utf-8")));
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    @Override
    public CoinType getType() {
        return this.type;
    }

    @Override
    public String toString() {
        return address;

    }
}