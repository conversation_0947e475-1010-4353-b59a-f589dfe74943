package com.coldlar.coin.nem.nac.models.transactions.drafts;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.coldlar.coin.nem.nac.application.NemConstants;
import com.coldlar.coin.nem.nac.common.SizeOf;
import com.coldlar.coin.nem.nac.common.enums.MessageType;
import com.coldlar.coin.nem.nac.common.enums.TransactionType;
import com.coldlar.coin.nem.nac.common.models.MessageDraft;
import com.coldlar.coin.nem.nac.models.NacPublicKey;
import com.coldlar.coin.nem.nac.models.Xems;
import com.coldlar.coin.nem.nac.models.primitives.AddressValue;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org51.spongycastle.util.encoders.Hex;

public final class TransferTransactionDraft extends AbstractTransactionDraft {

    private static final int VERSION = 1;
    public static final double NEM_MAX_FEE = 1.25;
    /**
     * NEM 最低手续费
     */
    public static final double NEM_MIN_FEE = 0.05;

    /**
     * @param payloadLength message payload length (byte count)
     */
    public static Xems calculateMinimumFee(final Xems amount, final int payloadLength) {
        final Xems fee = new Xems();

        Xems maxFee = Xems.fromXems(NEM_MAX_FEE);
        Xems minFee = Xems.fromXems(NEM_MIN_FEE);

        double amountXems = amount.getAsFractional();
        if (payloadLength > 0) {//计算msg的手续费
            fee.addXems(minFee.getAsFractional() * (Math.floor(payloadLength / 32) + 1));//最低手续费计算
        }
        //计算每笔交易的手续费，最高1.25
        if (amountXems > 10000) {
            double needFee = minFee.getAsFractional() * (Math.floor(amount.getAsFractional() / 10000));//采用最低低手续费计算
            fee.addXems(Math.min(maxFee.getAsFractional(), needFee));//和最大手续费对比
        } else {
            fee.addXems(minFee.getAsFractional());
        }

        return fee;
    }

    public final AddressValue recipientAddress;
    @NonNull
    public final Xems amount;
    @Nullable
    public final MessageDraft message;

    public TransferTransactionDraft(
            @NonNull final NacPublicKey signer,
            @NonNull final AddressValue recipient, @Nullable final Xems amount, @Nullable final MessageDraft message) {
        super(VERSION, signer);
        this.recipientAddress = recipient;
        this.amount = amount != null ? amount : Xems.ZERO;
        this.message = message;
    }

    @NonNull
    @Override
    public TransactionType getType() {
        return TransactionType.TRANSFER_TRANSACTION;
    }

    @NonNull
    @Override
    public Xems calculateMinimumFee() {
        final int length = message != null && message.hasPayload() ? message.getPayload().length() : 0;
        return calculateMinimumFee(amount, length);
    }

    @Override
    protected void serializeAdditional(@NonNull final ByteArrayOutputStream os)
            throws IOException {
        final byte[] recipientBytes = recipientAddress.getRaw().getBytes(NemConstants.ENCODING_UTF8);
        writeAsLeBytes(os, recipientBytes.length);
        os.write(recipientBytes);
        String s1 = Hex.toHexString(recipientBytes);
        writeAsLeBytes(os, amount.getAsMicro());
        if (message != null && message.hasPayload()) {
            //noinspection ConstantConditions // already checked if it has payload.
            final int messageFieldLength = // 4 bytes message type + 4 byte length of payload + payload bytes
                    SizeOf.INT + SizeOf.INT + message.getPayload().length();
            writeAsLeBytes(os, messageFieldLength);
            writeAsLeBytes(os, (message.isEncrypted() ? MessageType.ENCRYPTED : MessageType.NOT_ENCRYPTED).getValue());
            writeAsLeBytes(os, message.getPayload().length());
            os.write(message.getPayload().getRaw());
        } else {
            writeAsLeBytes(os, 0);
        }
    }
}
