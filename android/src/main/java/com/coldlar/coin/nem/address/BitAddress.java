package com.coldlar.coin.nem.address;


import com.coldlar.coin.CoinType;

import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.LegacyAddress;
import org.bitcoinj.core.NetworkParameters;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public class BitAddress extends LegacyAddress implements AbstractAddress {

    private BitAddress(NetworkParameters params, boolean p2sh, byte[] hash160) throws AddressFormatException {
        super(params, p2sh, hash160);
    }

    @Override
    public CoinType getType() {
        return (CoinType) params;
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }


    public static BitAddress from(CoinType params,boolean isP2sh, ECKey key) {
        return new BitAddress(params, isP2sh, key.getPubKeyHash());
    }

    public static BitAddress fromScriptHash(CoinType params, boolean isP2sh,byte[] pubkeyHash) {
        return new BitAddress(params, isP2sh, pubkeyHash);
    }

    public static BitAddress from(CoinType params, String address) {
        LegacyAddress address1 = LegacyAddress.fromBase58(params, address);
        return new BitAddress(params, false, address1.getHash160());
    }

    @Override
    public String toString() {
        return toBase58();
    }

}
