package com.coldlar.coin.nem.nac.models;

import androidx.annotation.NonNull;

import com.coldlar.coin.nem.core.crypto.PrivateKey;
import com.coldlar.coin.nem.nac.crypto.NacCryptoException;

public final class NacPrivate<PERSON>ey extends BinaryData {

	public NacPrivateKey(@NonNull final byte[] raw) {
		super(raw);
	}

	public NacPrivateKey(@NonNull final String rawDataHex) {
		super(rawDataHex);
	}

	@NonNull
	public EncryptedNacPrivateKey encryptKey(@NonNull final BinaryData encryptionKey)
			throws NacCryptoException {
		final EncryptedBinaryData encryptedData = super.encrypt(encryptionKey);
		return new EncryptedNacPrivateKey(encryptedData.rawData);
	}

	@NonNull
	public PrivateKey toPrivateKey() {
		return PrivateKey.fromBytes(this.rawData);
	}
}
