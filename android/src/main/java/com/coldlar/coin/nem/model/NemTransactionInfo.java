package com.coldlar.coin.nem.model;

/**
 * author wang<PERSON><PERSON>
 * date 2018/11/2 17:46
 * description Nem 交易实体
 */
public class NemTransactionInfo {

    /**
     * 交易类型
     * NEM_TRANSACTION_TYPE_TRANSFER                0x0101
     * NEM_TRANSACTION_TYPE_IMPORTANCE_TRANSFER     0x0801
     * NEM_TRANSACTION_TYPE_AGGREGATE_MODIFICATION  0x1001
     * NEM_TRANSACTION_TYPE_MULTISIG_SIGNATURE      0x1002
     * NEM_TRANSACTION_TYPE_MULTISIG                0x1004
     * NEM_TRANSACTION_TYPE_PROVISION_NAMESPACE     0x2001
     * NEM_TRANSACTION_TYPE_MOSAIC_CREATION         0x4001
     * NEM_TRANSACTION_TYPE_MOSAIC_SUPPLY_CHANGE    0x4002
     * NEM_NETWORK_MAINNET 0x68
     * NEM_NETWORK_TESTNET 0x98
     * NEM_NETWORK_MIJIN   0x60
     */
    private long txType;
    /**
     * 交易版本
     */
    private  long txVersion;
    /**
     * 交易时间
     */
    private long timestamp;
    /**
     * 公钥长度
     */
    private int pubkeyLen;
    /**
     * 公钥byte
     */
    private byte[] pubkeyByte;
    /**
     * 公钥
     */
    private String pubkey;
    /**
     * 发送地址
     */
    private String fromAdress;
    /**
     * 手续费
     */
    private String fee;
    /**
     * Deadline过期时间戳
     */
    private long deadline;
    /**
     * 接收地址长度
     */
    private int receiptLen;
    /**
     * 接收地址Byte
     */
    private byte[] toAddressByte;
    /**
     * 接收地址
     */
    private String toAddress;
    /**
     * 金额
     */
    private String amount;
    /**
     * 备注
     */
    private String remark;

    public long getTxType() {
        return txType;
    }

    public void setTxType(long txType) {
        this.txType = txType;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getPubkeyLen() {
        return pubkeyLen;
    }

    public void setPubkeyLen(int pubkeyLen) {
        this.pubkeyLen = pubkeyLen;
    }

    public byte[] getPubkeyByte() {
        return pubkeyByte;
    }

    public void setPubkeyByte(byte[] pubkeyByte) {
        this.pubkeyByte = pubkeyByte;
    }

    public String getPubkey() {
        return pubkey;
    }

    public void setPubkey(String pubkey) {
        this.pubkey = pubkey;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public long getDeadline() {
        return deadline;
    }

    public void setDeadline(long deadline) {
        this.deadline = deadline;
    }

    public int getReceiptLen() {
        return receiptLen;
    }

    public void setReceiptLen(int receiptLen) {
        this.receiptLen = receiptLen;
    }

    public String getToAddress() {
        return toAddress;
    }

    public void setToAddress(String toAddress) {
        this.toAddress = toAddress;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public long getTxVersion() {
        return txVersion;
    }

    public void setTxVersion(long txVersion) {
        this.txVersion = txVersion;
    }

    public String getFromAdress() {
        return fromAdress;
    }

    public void setFromAdress(String fromAdress) {
        this.fromAdress = fromAdress;
    }

    public byte[] getToAddressByte() {
        return toAddressByte;
    }

    public void setToAddressByte(byte[] toAddressByte) {
        this.toAddressByte = toAddressByte;
    }
}
