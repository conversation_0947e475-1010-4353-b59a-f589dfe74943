package com.coldlar.coin.nem;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.ethereum.transaction.HashUtil;
import com.coldlar.coin.nem.core.crypto.CryptoException;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;
import com.coldlar.encrypt.Base32Encoder;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;


public class NemMain extends CoinType {
    private NemMain() {
        id = "nem.main";

        addressHeader = 122;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID=ColdlarCoinID.ID_NEM;
        name = "NEM";
        cnName = "新经币";
        trName="新經幣";
        chain ="xem";
        symbol = "XEM";
        showSymbol = "XEM";
        uriScheme = "nem";
        bip44Index = 43;
        unitExponent = 6;
        feeValue = new BigDecimal("1500000");
        minNonDust =new BigDecimal("10000");
        minGlobalNetwokFee =new BigDecimal("1000");
        softDustLimit =new BigDecimal("1000");  // 0.001 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        confirm=10;
        coinOrder=13;
        maxSend=new BigDecimal("9000000000");
    }

    private static NemMain instance = new NemMain();
    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {
        if (address == null) {
            return false;
        }
        if (address.contains("-")) {
            address = address.replace("-", "");
        }
        if (40 != address.length()) {
            return false;
        }
        final byte[] encodedBytes;
        try {
            encodedBytes = Base32Encoder.getBytes(address);
        } catch (final IllegalArgumentException e) {
            return false;
        }
        if (25 != encodedBytes.length) {
            return false;
        }
        final int checksumStartIndex = 21;
        final byte[] versionPrefixedHash = Arrays.copyOfRange(encodedBytes, 0, checksumStartIndex);
        final byte[] addressChecksum = Arrays.copyOfRange(encodedBytes, checksumStartIndex, checksumStartIndex + 4);
        final byte[] calculatedChecksum = generateChecksum(versionPrefixedHash);
        return Arrays.equals(addressChecksum, calculatedChecksum);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof NemMain){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    private static byte[] generateChecksum(final byte[] input) {
        // step 1: sha3 hash of (input
        byte[] sha3StepThreeHash = new byte[0];
        try {
            sha3StepThreeHash = HashUtil.sha3(input);
        } catch (CryptoException e) {
            e.printStackTrace();
        }
        // step 2: get the first X bytes of (1)
        return Arrays.copyOfRange(sha3StepThreeHash, 0, 4);
    }

}
