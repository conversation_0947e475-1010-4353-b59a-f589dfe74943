package com.coldlar.coin.nem.core.crypto.ed25519.arithmetic;

import android.util.Log;

import com.coldlar.coin.nem.core.utils.HexEncoder;

import java.math.BigInteger;

/**
 * Represents the underlying group for Ed25519.
 */
public class Ed25519Group {

	/**
	 * 2^252 - 27742317777372353535851937790883648493
	 */
	public static final BigInteger GROUP_ORDER = BigInteger.ONE.shiftLeft(252).add(new BigInteger("27742317777372353535851937790883648493"));

	/**
	 * <pre>{@code
	 * (x, 4/5); x > 0
	 * }</pre>
	 */
	public static Ed25519GroupElement BASE_POINT;

	static {
		try {
			BASE_POINT = getBasePoint();
			ZERO_P3 = Ed25519GroupElement.p3(Ed25519Field.ZERO, Ed25519Field.ONE, Ed25519Field.ONE, Ed25519Field.ZERO);
			ZERO_P2 = Ed25519GroupElement.p2(Ed<PERSON>51<PERSON><PERSON><PERSON>.ZERO, Ed25519Field.ONE, Ed25519Field.ONE);
			ZERO_PRECOMPUTED = Ed25519GroupElement.precomputed(Ed25519Field.ONE, Ed25519Field.ONE, Ed25519Field.ZERO);
		} catch (Throwable t) {
			Log.e("E!", "Exception!", t);
			throw t;
		}
	}

	// different representations of zero
	public static final Ed25519GroupElement ZERO_P3;
	public static final Ed25519GroupElement ZERO_P2;
	public static final Ed25519GroupElement ZERO_PRECOMPUTED;

	private static Ed25519GroupElement getBasePoint() { // ok
		final byte[] encodedGroupElementBytes = HexEncoder.getBytes("5866666666666666666666666666666666666666666666666666666666666666");
		final Ed25519EncodedGroupElement encodedGroupElement = new Ed25519EncodedGroupElement(encodedGroupElementBytes);
		BASE_POINT = encodedGroupElement.decode();
		BASE_POINT.precomputeForScalarMultiplication();
		BASE_POINT.precomputeForDoubleScalarMultiplication();
		return BASE_POINT;
	}
}
