package com.coldlar.coin.ethereum.testnet;




import com.coldlar.coin.CoinType;

import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;
import java.math.BigDecimal;
import java.util.Map;

public class EthereumHoleskyTest extends CoinType {
    private static EthereumHoleskyTest instance = new EthereumHoleskyTest();

    private EthereumHoleskyTest() {
        this.id = "ethereum.test";
        this.name = "Ethereum holesky";
        this.symbol = "ETH_Holesky";
        this.chain ="eth_holesky";
        this.showSymbol= "hETH";
        this.uriScheme = "ethereum_holesky";
        this.bip44Index =17000;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "以太坊 Holesky";
        trName="以太坊 Holesky";
        this.coinID = ColdlarCoinID.ID_ETH_HOLESKY;
        confirm=12;
        this.isTestNetWork = true;
        coinOrder=3;
        this.isEVM = true;
        this.isEthereumFamily= true;
        this.chainId = 17000;
        this.support1559Transactions =true;
        this.rpcUrl="https://cloudflare-eth.com";
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        estimateGasPrice = "10000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 1;
    }

    public static CoinType get() {
        return instance;
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof EthereumHoleskyTest){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


}