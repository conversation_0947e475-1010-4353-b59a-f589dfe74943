package com.coldlar.coin.ethereum.transaction.crypto;


public abstract class DigestEngine
        implements Digest
{
    private long blockCount;
    private int blockLen;
    private int digestLen;
    private byte[] inputBuf;
    private int inputLen;
    private byte[] outputBuf;

    public DigestEngine()
    {
        doInit();
        this.digestLen = getDigestLength();
        this.blockLen = getInternalBlockLength();
        this.inputBuf = new byte[this.blockLen];
        this.outputBuf = new byte[this.digestLen];
        this.inputLen = 0;
        this.blockCount = 0L;
    }

    private void adjustDigestLen()
    {
        if (this.digestLen == 0)
        {
            this.digestLen = getDigestLength();
            this.outputBuf = new byte[this.digestLen];
        }
    }

    public int digest(byte[] paramArrayOfByte, int paramInt1, int paramInt2)
    {
        adjustDigestLen();
        if (paramInt2 >= this.digestLen)
        {
            doPadding(paramArrayOfByte, paramInt1);
            reset();
            return this.digestLen;
        }
        doPadding(this.outputBuf, 0);
        System.arraycopy(this.outputBuf, 0, paramArrayOfByte, paramInt1, paramInt2);
        reset();
        return paramInt2;
    }

    public byte[] digest()
    {
        adjustDigestLen();
        byte[] arrayOfByte = new byte[this.digestLen];
        digest(arrayOfByte, 0, this.digestLen);
        return arrayOfByte;
    }

    protected abstract void doInit();

    protected abstract void doPadding(byte[] paramArrayOfByte, int paramInt);

    protected abstract void engineReset();

    protected final int flush()
    {
        return this.inputLen;
    }

    protected final byte[] getBlockBuffer()
    {
        return this.inputBuf;
    }

    protected int getInternalBlockLength()
    {
        return getBlockLength();
    }

    protected abstract void processBlock(byte[] paramArrayOfByte);

    public void reset()
    {
        engineReset();
        this.inputLen = 0;
        this.blockCount = 0L;
    }

    public void update(byte[] paramArrayOfByte)
    {
        update(paramArrayOfByte, 0, paramArrayOfByte.length);
    }

    public void update(byte[] paramArrayOfByte, int paramInt1, int paramInt2)
    {
        while (paramInt2 > 0)
        {
            int i = this.blockLen - this.inputLen;
            if (i > paramInt2)
                i = paramInt2;
            System.arraycopy(paramArrayOfByte, paramInt1, this.inputBuf, this.inputLen, i);
            paramInt1 += i;
            this.inputLen = (i + this.inputLen);
            paramInt2 -= i;
            if (this.inputLen == this.blockLen)
            {
                processBlock(this.inputBuf);
                this.blockCount = (1L + this.blockCount);
                this.inputLen = 0;
            }
        }
    }
}