/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-11 11:17:34
 */
package com.coldlar.coin.ethereum.evm;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class PolygonMain extends CoinType {
    private static PolygonMain instance = new PolygonMain();

    private PolygonMain() {
        this.id = "polygon.main";
        this.name = "Polygon";
        this.symbol = "Polygon";
        this.chain = "polygon";
        this.showSymbol = "POL";
        this.uriScheme = "polygon";
        this.bip44Index = 966;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "Polygon";
        trName = "Polygon";
        this.coinID = ColdlarCoinID.ID_MATIC;
        this.support1559Transactions = true;

        this.isEVM = true;
        this.isEthereumFamily = true;
        this.chainId = 137;
        this.rpcUrl = "https://polygon-rpc.com";
        confirm = 12;
        coinOrder = 6;
        minFeeSat = new BigDecimal("1");
        maxFeeSat = new BigDecimal("2000");
        estimateGasPrice = "103000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 8;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof PolygonMain) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public int getEthereumChainId() {
        return 137;
    }

}
