package com.coldlar.coin.ethereum;

import org.web3j.utils.Numeric;

public class TransactionType {

    public static final String LEGACY = "LegacyTransaction";
    public static final String EIP2930 = "EIP-2930";
    public static final String EIP1559 = "EIP-1559";


    public static org.web3j.crypto.transaction.type.TransactionType getTransactionType(String hexTransaction) {
        byte[] transaction = Numeric.hexStringToByteArray(hexTransaction);
        byte firstByte = transaction[0];
        if (firstByte == org.web3j.crypto.transaction.type.TransactionType.EIP1559.getRlpType()) {
            return org.web3j.crypto.transaction.type.TransactionType.EIP1559;
        } else {
            return firstByte == org.web3j.crypto.transaction.type.TransactionType.EIP2930.getRlpType() ? org.web3j.crypto.transaction.type.TransactionType.EIP2930 : org.web3j.crypto.transaction.type.TransactionType.LEGACY;
        }
    }

}
