package com.coldlar.coin.ethereum.transaction;

import static java.lang.String.format;

import android.text.TextUtils;

import com.coldlar.coin.ethereum.transaction.crypto.Keccak256;
import com.coldlar.core.util.NumberUtil;
import com.coldlar.utils.BigDecimalUtils;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class FunctionUtil {
    private final static ObjectMapper DEFAULT_MAPPER = new ObjectMapper()
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);


    public static byte[] getCallData(Function callFunc, Object... funcArgs) {
        return callFunc.encode(funcArgs);
    }

    public static class Function {
        public boolean constant;
        public String name = "";
        public Param[] inputs = new Param[0];
        public Param[] outputs = new Param[0];
        public FunctionType type;

        public Function() {
        }

        public byte[] encode(Object... args) {
            return ByteUtil.merge(encodeSignature(), encodeArguments(args));
        }

        byte[] encodeArguments(Object... args) {
            if (args.length > inputs.length)
                throw new RuntimeException("Too many arguments: " + args.length + " > " + inputs.length);

            int staticSize = 0;
            int dynamicCnt = 0;
            // calculating static size and number of dynamic params
            for (int i = 0; i < args.length; i++) {
                Param param = inputs[i];
                if (param.type.isDynamicType()) {
                    dynamicCnt++;
                }
                staticSize += param.type.getFixedSize();
            }

            byte[][] bb = new byte[args.length + dynamicCnt][];

            int curDynamicPtr = staticSize;
            int curDynamicCnt = 0;
            for (int i = 0; i < args.length; i++) {
                if (inputs[i].type.isDynamicType()) {
                    byte[] dynBB = inputs[i].type.encode(args[i]);
                    bb[i] = SolidityType.IntType.encodeInt(curDynamicPtr);
                    bb[args.length + curDynamicCnt] = dynBB;
                    curDynamicCnt++;
                    curDynamicPtr += dynBB.length;
                } else {
                    bb[i] = inputs[i].type.encode(args[i]);
                }
            }
            return ByteUtil.merge(bb);
        }

        private Object[] decode(byte[] encoded, Param[] params) {
            Object[] ret = new Object[params.length];

            int off = 0;
            for (int i = 0; i < params.length; i++) {
                if (params[i].type.isDynamicType()) {
                    ret[i] = params[i].type.decode(encoded, SolidityType.IntType.decodeInt(encoded, off).intValue());
                } else {
                    ret[i] = params[i].type.decode(encoded, off);
                }
                off += params[i].type.getFixedSize();
            }
            return ret;
        }

        public Object[] decode(byte[] encoded) {
            return decode(subarray(encoded, 4, encoded.length), inputs);
        }

        static byte[] subarray(byte[] array, int startIndexInclusive, int endIndexExclusive) {
            if (array == null) {
                return null;
            } else {
                if (startIndexInclusive < 0) {
                    startIndexInclusive = 0;
                }

                if (endIndexExclusive > array.length) {
                    endIndexExclusive = array.length;
                }

                int newSize = endIndexExclusive - startIndexInclusive;
                if (newSize <= 0) {
                    return new byte[0];
                } else {
                    byte[] subarray = new byte[newSize];
                    System.arraycopy(array, startIndexInclusive, subarray, 0, newSize);
                    return subarray;
                }
            }
        }

        String formatSignature() {
            StringBuilder paramsTypes = new StringBuilder();
            for (Param param : inputs) {
                paramsTypes.append(param.type.getCanonicalName()).append(",");
            }
            return format("%s(%s)", name, stripEnd(paramsTypes.toString(), ","));
        }

        static String stripEnd(String str, String stripChars) {
            int end;
            if (str != null && (end = str.length()) != 0) {
                if (stripChars == null) {
                    while (end != 0 && Character.isWhitespace(str.charAt(end - 1))) {
                        --end;
                    }
                } else {
                    if (stripChars.isEmpty()) {
                        return str;
                    }

                    while (end != 0 && stripChars.indexOf(str.charAt(end - 1)) != -1) {
                        --end;
                    }
                }

                return str.substring(0, end);
            } else {
                return str;
            }
        }


        byte[] encodeSignatureLong() {
            String signature = formatSignature();
            return sha3(signature.getBytes());
        }

        byte[] encodeSignature() {
            return Arrays.copyOfRange(encodeSignatureLong(), 0, 4);
        }

        @Override
        public String toString() {
            return formatSignature();
        }

        public static Function fromJsonInterface(String json) {
            try {
                return DEFAULT_MAPPER.readValue(json, Function.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static byte[] sha3(byte[] paramArrayOfByte) {
        Keccak256 localKeccak256 = new Keccak256();
        localKeccak256.update(paramArrayOfByte);
        return localKeccak256.digest();
    }

    enum FunctionType {
        constructor,
        function,
        event,
        fallback
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Param {
        public String name;
        public SolidityType type;

        @JsonGetter("type")
        public String getType() {
            return type.getName();
        }
    }

    public static abstract class Type {
        protected String name;

        public Type(String paramString) {
            this.name = paramString;
        }

        public static SolidityType getType(String paramString) {
            if (paramString.contains("["))
                return SolidityType.ArrayType.getType(paramString);
            if ("bool".equals(paramString))
                return new SolidityType.BoolType(paramString);
            if ((paramString.startsWith("int")) || (paramString.startsWith("uint")))
                return new SolidityType.IntType(paramString);
            if ("address".equals(paramString))
                return new SolidityType.AddressType();
            if ("string".equals(paramString))
                return new SolidityType.StringType();
            if ("bytes".equals(paramString))
                return new SolidityType.BytesType(paramString);
            if (paramString.startsWith("bytes"))
                return new SolidityType.Bytes32Type(paramString);
            throw new RuntimeException("Unknown type: " + paramString);
        }

        public abstract Object decode(byte[] paramArrayOfByte, int paramInt);

        public abstract byte[] encode(Object paramObject);

        public String getCanonicalName() {
            return getName();
        }

        public int getFixedSize() {
            return 32;
        }

        public String getName() {
            return this.name;
        }

        @Override
        public String toString() {
            return getName();
        }
    }


    public static byte[] getERC721TransferBytes(String from, String to, String contract, BigInteger tokenIdBI) throws NumberFormatException {

        org.web3j.abi.datatypes.Function txFunc = getTransferFunction(from, to, contract, tokenIdBI);
        String encodedFunction = FunctionEncoder.encode(txFunc);
        return Numeric.hexStringToByteArray(Numeric.cleanHexPrefix(encodedFunction));
    }


    public static org.web3j.abi.datatypes.Function getTransferFunction(String from, String to, String contract, BigInteger tokenIdBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function function = null;
        List<org.web3j.abi.datatypes.Type> params;
        List<TypeReference<?>> returnTypes = Collections.emptyList();
        if (tokenUsesLegacyTransfer(contract)) {
            params = Arrays.asList(new Address(to), new Uint256(tokenIdBI));
            function = new org.web3j.abi.datatypes.Function("transfer", params, returnTypes);
        } else {
            //function safeTransferFrom(address _from, address _to, uint256 _tokenId) external payable;
            params = Arrays.asList(new Address(from), new Address(to), new Uint256(tokenIdBI));
            function = new org.web3j.abi.datatypes.Function("safeTransferFrom", params, returnTypes);
        }
        return function;
    }

    private static boolean tokenUsesLegacyTransfer(String contract) throws NumberFormatException {
        switch (contract.toLowerCase()) {
            case "0x06012c8cf97bead5deae237070f9587f8e7a266d":
            case "0xabc7e6c01237e8eef355bba2bf925a730b714d5f":
            case "0x71c118b00759b0851785642541ceb0f4ceea0bd5":
            case "0x16baf0de678e52367adc69fd067e5edd1d33e3bf":
            case "0x7fdcd2a1e52f10c28cb7732f46393e297ecadda1":
                return true;

            default:
                return false;
        }
    }


    public static byte[] getERC1155TransferBytes(String from, String to, BigInteger tokenIds, BigInteger amountBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function txFunc = getERC1155TransferFunction(from, to, tokenIds, amountBI);
        String encodedFunction = FunctionEncoder.encode(txFunc);
        return Numeric.hexStringToByteArray(Numeric.cleanHexPrefix(encodedFunction));
    }


    public static  org.web3j.abi.datatypes.Function getERC1155TransferFunction(String from, String to, BigInteger tokenIdBI, BigInteger amountBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function function;
        List<org.web3j.abi.datatypes.Type> params;
        List<TypeReference<?>> returnTypes = Collections.emptyList();

        params = Arrays.asList(new Address(from), new Address(to), new Uint256(tokenIdBI),
                new Uint256(amountBI), new DynamicBytes(new byte[0]));
        function = new org.web3j.abi.datatypes.Function("safeTransferFrom", params, returnTypes);
        return function;
    }



    /**
     * 以太系列余额转换，10进制带小数最大单位转换为16进制
     * 1.23》123000000000000000》0xce2f1
     *
     * @param Balance 最大单位值
     * @param decimal 精度
     * @return
     */
    public static String balanceToHex(String Balance, int decimal) {
        BigDecimal balance = new BigDecimal(Balance);
        String decimalStr = "1";
        for (int i = 0; i < decimal; i++) {
            decimalStr = decimalStr + "0";
        }
        BigDecimal balanceWei = new BigDecimal(decimalStr);
        BigInteger hexBalanceTmp = balance.multiply(balanceWei).toBigInteger();
        String resultStr = "0x" + hexBalanceTmp.toString(16);
        return resultStr;
    }


    public static String gweiTohex(String hexBalance) {
        try {
            String gwei = BigDecimalUtils.mul5(hexBalance, "1000000000");
            return toHexStr(gwei);
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 10进制转16进制
     * 传入时需要转换为大整数String
     *
     * @param
     * @return
     */
    public static String toHexStr(String Balance) {
        String result = null;
        try {
            if (!TextUtils.isEmpty(Balance)) {
                BigInteger bigInteger = new BigInteger(NumberUtil.setFormate(Balance));
                result = "0x" + bigInteger.toString(16);
            }
        } catch (Exception e) {

        } finally {
            return result;
        }
    }


    /**
     * 金额转换
     *
     * @param bVal   转换得金额
     * @param digits 小数位数
     * @return 显示的金额
     */
    public static String toAmountString(BigInteger bVal, int digits) {
        try {
            BigDecimal unit = BigDecimal.valueOf(Math.pow(10, digits));
            BigDecimal value = new BigDecimal(bVal);
            BigDecimal result = value.divide(unit, digits, RoundingMode.DOWN);
            return result.stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            return "0";
        }
    }


}
