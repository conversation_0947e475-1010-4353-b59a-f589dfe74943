package com.coldlar.coin.ethereum;



import com.coldlar.coin.ethereum.entity.ContractInfo;
import com.coldlar.coin.ethereum.entity.EthTransactionInfo;
import com.coldlar.coin.ethereum.util.EthUtil;
import com.coldlar.coin.ethereum.util.RLP;
import com.coldlar.coin.ethereum.util.RLPItem;
import com.coldlar.coin.ethereum.util.RLPList;
import org.spongycastle.util.BigIntegers;
import org.spongycastle.util.encoders.Hex;
import org.web3j.rlp.RlpDecoder;
import org.web3j.rlp.RlpList;
import org.web3j.rlp.RlpString;

import java.math.BigInteger;

/**
 * Created by Administrator on 2017/5/8 0008.
 */

public class EthParseUtil {


    private static byte[] nonce;
    private static byte[] value;
    private static byte[] receiveAddress;
    private static byte[] gasPrice;
    private static byte[] gasLimit;
    private static byte[] input;
    private static String inputStr;



    public static EthTransactionInfo parse1599Transaction(String data) {
        EthTransactionInfo info = new EthTransactionInfo();

        final byte[] transaction = Hex.decode(data);
        final RlpList rlpList = RlpDecoder.decode(transaction);
        final RlpList values = (RlpList) rlpList.getValues().get(1);
        final long chainId = ((RlpString) values.getValues().get(0)).asPositiveBigInteger().longValue();
        final BigInteger nonce = ((RlpString) values.getValues().get(1)).asPositiveBigInteger();
        final BigInteger maxPriorityFeePerGas = ((RlpString) values.getValues().get(2)).asPositiveBigInteger();
        final BigInteger maxFeePerGas = ((RlpString) values.getValues().get(3)).asPositiveBigInteger();
        final BigInteger gasLimit = ((RlpString) values.getValues().get(4)).asPositiveBigInteger();
        final String to = ((RlpString) values.getValues().get(5)).asString();
        final BigInteger value = ((RlpString) values.getValues().get(6)).asPositiveBigInteger();
        final String inputStr = ((RlpString) values.getValues().get(7)).asString();

        info.setNonce(nonce);
        info.setMaxFee(maxFeePerGas);
        info.setMaxPriorityFee(maxPriorityFeePerGas);
        info.setGasLimit(gasLimit);
        info.setReceiveAddress(to);
        info.setValue(value);
        info.setTransactionType(TransactionType.EIP1559);
        info.setChainId(chainId);
        ContractInfo contractInfo = new ContractInfo();
        if (inputStr != null && inputStr.length() >= 138) {
            contractInfo.setAddress("0x" + inputStr.substring(34, 74));
            contractInfo.setAmount("0x" + inputStr.substring(74, 138));
            info.setContractInfo(contractInfo);
        }

        return info;

    }

    public static EthTransactionInfo parseEIP155(String data) {
        EthTransactionInfo info = new EthTransactionInfo();
        RLPList decodedTxList = RLP.decode2(Hex.decode(data));
        RLPList transaction = (RLPList) decodedTxList.get(0);
        nonce = ((RLPItem) transaction.get(0)).getRLPData();
        gasPrice = ((RLPItem) transaction.get(1)).getRLPData();
        gasLimit = ((RLPItem) transaction.get(2)).getRLPData();
        receiveAddress = ((RLPItem) transaction.get(3)).getRLPData();
        value = ((RLPItem) transaction.get(4)).getRLPData();
        input = ((RLPItem) transaction.get(5)).getRLPData();


        BigInteger bNonce;
        try {
            bNonce = BigIntegers.fromUnsignedByteArray(nonce);
        } catch (Exception e) {
            bNonce = BigInteger.ZERO;
        }
        try {
            if (input != null) {
                inputStr = BigIntegers.fromUnsignedByteArray(input).toString();
                inputStr = EthUtil.toHexStr(inputStr);
            }
        } catch (Exception e) {

        }



        BigInteger bGasPrice;
        if (gasPrice != null) {
            bGasPrice = BigIntegers.fromUnsignedByteArray(gasPrice);
        } else {
            bGasPrice = BigInteger.ZERO;
        }

        BigInteger bGasLimit;

        if (gasLimit != null) {
            bGasLimit = BigIntegers.fromUnsignedByteArray(gasLimit);
        } else {
            bGasLimit = BigInteger.ZERO;
        }
        String address;
        if (receiveAddress != null) {
            address = BigIntegers.fromUnsignedByteArray(receiveAddress).toString();
            address = EthUtil.toHexStr(address);
        } else {
            address = "";
        }


        //转为16进制以后少0补0
        if (address.length() < 42) {
            int zeronum = 42 - address.length();
            String str = "";
            for (int i = 0; i < zeronum; i++) {
                str = str + "0";
            }
            StringBuilder sb = new StringBuilder(address);
            sb.insert(2, str);
            address = sb.toString();
        }
        BigInteger bValue;
        if (value != null) {
            bValue = BigIntegers.fromUnsignedByteArray(value);
        } else {
            bValue = BigInteger.ZERO;
        }


        info.setNonce(bNonce);
        info.setGasPrice(bGasPrice);
        info.setGasLimit(bGasLimit);
        info.setReceiveAddress(address);
        info.setValue(bValue);
        ContractInfo contractInfo = new ContractInfo();
        if (inputStr != null && inputStr.length() >= 138) {
            contractInfo.setAddress("0x" + inputStr.substring(34, 74));
            contractInfo.setAmount("0x" + inputStr.substring(74, 138));
            info.setContractInfo(contractInfo);
        }
        return info;
    }
}
