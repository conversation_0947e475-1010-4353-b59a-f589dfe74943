package com.coldlar.coin.ethereum.evm;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class HecoMain extends CoinType {
    private static HecoMain instance = new HecoMain();

    private HecoMain() {
        this.id = "heco.main";
        this.name = "Huobi ECO Chain";
        this.chain ="ht";
        this.symbol = "HT";
        this.showSymbol= "HT";
        this.uriScheme = "huobi eco chain";
        this.bip44Index = 1010;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "火币链";
        trName="火币链";
        this.coinID = ColdlarCoinID.ID_HT;
        this.isEVM = true;
        this.isEthereumFamily= true;
        this.chainId = 128;
        this.support1559Transactions =true;

        confirm=12;
        coinOrder=12;
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        estimateGasPrice = "10000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 11;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof HecoMain){
            return true;
        }else {
            return false;
        }
    }
    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public int getEthereumChainId() {
        return 128;
    }

}
