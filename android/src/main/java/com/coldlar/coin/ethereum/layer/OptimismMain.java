package com.coldlar.coin.ethereum.layer;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class OptimismMain extends CoinType {
    private static OptimismMain instance = new OptimismMain();

    private OptimismMain() {
        this.id = "optimism.main";
        this.name = "Optimism";
        this.symbol = "OPT";
        this.chain = "opt";
        this.showSymbol = "ETH";
        this.uriScheme = "optimism";
        this.bip44Index = 614;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "Optimism";
        trName="Optimism";
        this.coinID = ColdlarCoinID.ID_OPT;
        this.isEVM = false;
        this.isLayer2 =true;
        this.isEthereumFamily= true;
        this.support1559Transactions =true;

        this.chainId = 10;
        confirm=12;
        coinOrder=8;
        minFeeSat=new BigDecimal("0.01");
        maxFeeSat=new BigDecimal("1");
        estimateGasPrice = "1500000000";
        gasLimitHex = "0x5208";
        this.coinSort = 10;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof OptimismMain){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }




    public int getEthereumChainId() {
        return 10;
    }

}
