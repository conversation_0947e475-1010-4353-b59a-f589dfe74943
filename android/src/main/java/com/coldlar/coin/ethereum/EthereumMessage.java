package com.coldlar.coin.ethereum;


import static com.coldlar.coin.ethereum.entity.Numeric.cleanHexPrefix;

import com.coldlar.coin.ethereum.entity.Numeric;
import com.coldlar.coin.ethereum.entity.SignMessageType;

public class EthereumMessage {

    private final SignMessageType messageType;
    private final CharSequence userMessage;
    public final String displayOrigin = null;
    public final long leafPosition =  0;
    public final byte[] prehash; //this could be supplied on-demand
    public static final String MESSAGE_PREFIX = "\u0019Ethereum Signed Message:\n";

    public EthereumMessage(String message, SignMessageType type) {

        this.messageType = type;

        message = message == null ? "" : message;
        this.prehash = getEthereumMessage(message);
        this.userMessage = message;
    }



    private byte[] getEthereumMessage(String message) {
        byte[] encodedMessage;
        if (isHex(message))
        {
            encodedMessage = Numeric.hexStringToByteArray(message);
        }
        else
        {
            encodedMessage = message.getBytes();
        }

        byte[] result;
        if (messageType == SignMessageType.SIGN_PERSONAL_MESSAGE
                || messageType == SignMessageType.SIGN_MESSAGE)
        {
            byte[] prefix = getEthereumMessagePrefix(encodedMessage.length);

            result = new byte[prefix.length + encodedMessage.length];
            System.arraycopy(prefix, 0, result, 0, prefix.length);
            System.arraycopy(encodedMessage, 0, result, prefix.length, encodedMessage.length);
        }
        else
        {
            result = encodedMessage;
        }
        return result;
    }



    public byte[] getPrehash() {
        return this.prehash;
    }


    public long getCallbackId() {
        return this.leafPosition;
    }





    private boolean isHex(String testMsg)
    {
        if (testMsg == null || testMsg.length() == 0) return false;
        testMsg = cleanHexPrefix(testMsg);

        for (int i = 0; i < testMsg.length(); i++)
        {
            if (Character.digit(testMsg.charAt(i), 16) == -1) { return false; }
        }

        return true;
    }

    private byte[] getEthereumMessagePrefix(int messageLength) {
        return MESSAGE_PREFIX.concat(String.valueOf(messageLength)).getBytes();
    }
}
