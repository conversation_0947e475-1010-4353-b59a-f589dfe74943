package com.coldlar.coin.ethereum.util;

import android.text.TextUtils;

import com.coldlar.core.util.NumberUtil;
import com.coldlar.core.util.StringUtil;
import com.coldlar.utils.BigDecimalUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by mark on 17/5/5.
 * 2020年7月8日17:53:38 调整：删除多余方法，整合重复方法，做好容错异常判断
 * 调用时用现有方法，仔细看备注
 */
@SuppressWarnings("unchecked")
public class EthUtil {
    /**
     * P1 P2 bug ，传到端金额过大时，会引起闪退，APP判断金额超过500亿就传送500亿
     */
    public static final String UNIT_500E = "50000000000";
    /**
     * 100亿，精确到最大单位
     */
    public static final String UNIT_1000E = "100000000000";
    /**
     * 以太最小单位
     */
    public static final String ETHWEI = "1000000000000000000";

    public static final BigInteger COIN_UNIT_GWEI = new BigInteger("1000000000");

    private static final BigInteger COIN_UNIT_SHANON = new BigInteger("1000000000");

    public static final String HEX_HEADER = "0x";

    /**
     * 16进制转10进制，转完以后去掉末尾0和小数点
     * 适用于区块高度，nonce gasused 这类不需要处理小数位的，转完直接使用
     *
     * @param hex
     * @return
     */
    public static String hexToString(String hex) {
        if (StringUtil.isEmpty(hex)) {
            return "0";
        } else {
            hex = hex.replace(HEX_HEADER, "");
            BigInteger bigInteger = new BigInteger(hex, 16);
            String result = bigInteger.toString();
            result = NumberUtil.setFormate(result);
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
            return result;
        }
    }

    /**
     * ETH余额
     * 16进制 换算为10进制最大单位，保留18位小数，去掉末尾0和小数点
     * 适用于ETH的金额转换和手续费计算
     *
     * @param hexBalance
     * @return
     */
    public static String ethBalanceToStr(String hexBalance) {
        String str = "0";
        try {
            hexBalance = hexBalance.replace(HEX_HEADER, "");
            BigInteger hexBalanceTmp = new BigInteger(hexBalance, 16);
            BigDecimal balance = new BigDecimal(hexBalanceTmp.toString());
            BigDecimal balanceWei = new BigDecimal(ETHWEI);
            BigDecimal result = balance.divide(balanceWei, 18, RoundingMode.DOWN);
            str = NumberUtil.setFormate(result.toString());
            if (str.indexOf(".") > 0) {
                str = str.replaceAll("0+?$", "");
                str = str.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            return str;
        }
        return str;
    }

    /**
     * Token余额；根据小数位转换
     * 16进制 换算为10进制,保留精度小数位，去掉末尾0和小数点
     *
     * @param hexBalance 16进制余额
     * @param decimal    token小数位
     * @return
     */
    public static String tokenBalanceToStr(String hexBalance, String decimal) {
        String str = "0";
        try {
            if (StringUtil.isEmpty(hexBalance) || hexBalance.equals("0") || hexBalance.equals("0x")
                    || StringUtil.isEmpty(decimal)) {
                return str;
            }
            int decimalIntValue = Integer.parseInt(decimal);
            // 判断是16进制还是10进制
            if (!hexBalance.toUpperCase().equals(hexBalance)) {
                hexBalance = hexToString(hexBalance);
            }
            hexBalance = hexBalance.replace(HEX_HEADER, "");
            BigDecimal balance = new BigDecimal(hexBalance);
            BigDecimal balanceWei = new BigDecimal(getTokenUnit(decimalIntValue));
            BigDecimal result = balance.divide(balanceWei, decimalIntValue, RoundingMode.DOWN);
            str = NumberUtil.setFormate(result.toString());
            if (str.indexOf(".") > 0) {
                str = str.replaceAll("0+?$", "");
                str = str.replaceAll("[.]$", "");
            }
            return str;
        } catch (Exception e) {
            return str;
        }

    }

    /**
     * 10进制转16进制
     * 传入时需要转换为大整数String
     *
     * @param
     * @return
     */
    public static String toHexStr(String Balance) {
        String result = null;
        try {
            if (!StringUtil.isEmpty(Balance)) {
                BigInteger bigInteger = new BigInteger(NumberUtil.setFormate(Balance));
                result = "0x" + bigInteger.toString(16);
            }
        } catch (Exception e) {

        } finally {
            return result;
        }
    }

    /**
     * 以太系列余额转换，10进制带小数最大单位转换为16进制
     * 1.23》123000000000000000》0xce2f1
     *
     * @param Balance 最大单位值
     * @param decimal 精度
     * @return
     */
    public static String balanceToHex(String Balance, int decimal) {
        BigDecimal balance = new BigDecimal(Balance);
        String decimalStr = "1";
        for (int i = 0; i < decimal; i++) {
            decimalStr = decimalStr + "0";
        }
        BigDecimal balanceWei = new BigDecimal(decimalStr);
        BigInteger hexBalanceTmp = balance.multiply(balanceWei).toBigInteger();
        String resultStr = "0x" + hexBalanceTmp.toString(16);
        return resultStr;
    }

    /**
     * ETH 16进制转换单位为GWEI，常用于手续费
     *
     * @param hexBalance
     * @return
     */
    public static String toGWei(String hexBalance) {
        return hexToUnitStr(hexBalance, COIN_UNIT_GWEI.toString());
    }

    /**
     * ETH 16进制转换单位为GWEI，常用于手续费
     *
     * @param hexBalance
     * @return
     */
    public static String toGWeiLayer2(String hexBalance) {
        return hexToUnitStrLayer2(hexBalance, COIN_UNIT_GWEI.toString());
    }

    /**
     * ETH 16进制转换单位为SHANON常用于手续费
     *
     * @param hexBalance
     * @return
     */
    public static String toShannon(String hexBalance) {
        return hexToUnitStr(hexBalance, COIN_UNIT_SHANON.toString());
    }

    /**
     * GWEI单位的gasprice转换为16进制
     *
     * @param hexBalance
     * @return
     */
    public static String gweiTohex(String hexBalance) {
        try {
            String gwei = BigDecimalUtils.mul5(hexBalance, "1000000000");
            return toHexStr(gwei);
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 16进制转换成对应的单位数
     * 保留精度小数位，去掉末尾0和小数点
     *
     * @param hexBalance
     * @param unit
     * @return
     */
    public static String hexToUnitStr(String hexBalance, String unit) {
        String result = "0";
        if (StringUtil.isEmpty(hexBalance) || hexBalance.equals("0") || hexBalance.equals("0x")
                || StringUtil.isEmpty(unit)) {
            return result;
        }
        hexBalance = hexBalance.replace(HEX_HEADER, "");
        BigInteger hexBalanceTmp = new BigInteger(hexBalance, 16);
        BigDecimal balance = new BigDecimal(hexBalanceTmp.toString());
        BigDecimal balanceWei = new BigDecimal(unit);
        result = balance.divide(balanceWei, 4, RoundingMode.DOWN).toString();
        if (result.indexOf(".") > 0) {
            result = result.replaceAll("0+?$", "");
            result = result.replaceAll("[.]$", "");
        }
        return result;
    }

    /**
     * 16进制转换成对应的单位数
     * 保留精度小数位，去掉末尾0和小数点
     *
     * @param hexBalance
     * @param unit
     * @return
     */
    public static String hexToUnitStrLayer2(String hexBalance, String unit) {
        String result = "0";
        if (StringUtil.isEmpty(hexBalance) || hexBalance.equals("0") || hexBalance.equals("0x")
                || StringUtil.isEmpty(unit)) {
            return result;
        }
        hexBalance = hexBalance.replace(HEX_HEADER, "");
        BigInteger hexBalanceTmp = new BigInteger(hexBalance, 16);
        BigDecimal balance = new BigDecimal(hexBalanceTmp.toString());
        BigDecimal balanceWei = new BigDecimal(unit);
        result = balance.divide(balanceWei, 2, RoundingMode.UP).stripTrailingZeros().toPlainString();
        return result;
    }

    /**
     * 传送余额界面，判断余额是否大于100Y
     *
     * @param balance
     * @param decimal
     * @return
     */
    public static Map<Integer, Object> value1000eTo500e(String balance, int decimal) {
        balance = balance.replace("0x", "");
        Map result = new HashMap();
        BigInteger e500 = new BigInteger(UNIT_500E);
        BigInteger e1000 = new BigInteger(UNIT_1000E);
        BigInteger balanceValue = new BigInteger(balance, 16);
        BigInteger value1000e = e1000.multiply(new BigInteger(getTokenUnit(decimal)));
        if (balanceValue.compareTo(value1000e) >= 0) {
            balanceValue = e500.multiply(new BigInteger(getTokenUnit(decimal)));
            result.put(0, true);
        } else {
            result.put(0, false);
        }
        String string = toHexStr(balanceValue.toString());
        result.put(1, string);
        return result;
    }

    /**
     * 以太系列 精确的余额运算
     *
     * @param b1
     * @param b2
     * @return
     */
    public static String ethBalancAdd(String b1, String b2) {
        if (TextUtils.isEmpty(b1)) {
            b1 = "0";
        }
        if (TextUtils.isEmpty(b2)) {
            b2 = "0";
        }
        BigDecimal bd1 = new BigDecimal(b1);
        BigDecimal bd2 = new BigDecimal(b2);
        return bd1.add(bd2).toString();
    }

    /**
     * 小数位转换 小数位 8 转换完： 100000000
     *
     * @param decimal
     * @return
     */
    public static String getTokenUnit(int decimal) {
        String value = "1";
        for (int i = 0; i < decimal; i++) {
            value += "0";
        }
        return value;
    }

    /**
     * 判断是否为以太地址
     *
     * @param address
     * @return
     */
    public static boolean isEthAddress(String address) {
        return address.matches("^(0x)?[0-9a-fA-F]{40}$");
    }

    /**
     * 尝试从String中解析出ETH地址，解析失败返回null,适用于扫一扫扫各种地方的ETH收币地址
     *
     * @param addressStr
     * @return
     */
    public static String getEthAddress(String addressStr) {
        String ethAddress = null;
        addressStr = addressStr.substring(addressStr.lastIndexOf(":") + 1,
                addressStr.lastIndexOf("?") < 0 ? addressStr.length() : addressStr.lastIndexOf("?"));
        if (isEthAddress(addressStr)) {
            ethAddress = addressStr;
        } else {
            if (addressStr != null && addressStr.startsWith("XE")) {
                try {
                    addressStr = addressStr.substring(4);
                    BigInteger addBig = new BigInteger(addressStr, 36);
                    String add = addBig.toString(16);
                    while (add.length() < 40) {
                        add = '0' + add;
                    }
                    addressStr = "0x" + add;
                    ethAddress = addressStr;
                } catch (Exception e) {
                }
            }
        }
        return ethAddress;
    }

}
