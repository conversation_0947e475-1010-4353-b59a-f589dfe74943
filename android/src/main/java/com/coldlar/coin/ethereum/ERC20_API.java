package com.coldlar.coin.ethereum;

/**
 * Created by Administrator on 2017/7/29 0029.
 */

public class ERC20_API {
    public static String TRANSFER = "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'_to', 'type':'address'},{'name':'_value', 'type':'uint256'}], \n" +
            "  'name': 'transfer', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";
    public static String EOS_MAPPING= "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'key', 'type':'string'}], \n" +
            "  'name': 'register', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";
    public static String PG_MINT= "   {\n" +
            "        \"anonymous\":false,\n" +
            "        \"inputs\":[\n" +
            "            {\n" +
            "                \"indexed\":true,\n" +
            "                \"name\":\"to\",\n" +
            "                \"type\":\"address\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"indexed\":false,\n" +
            "                \"name\":\"amount\",\n" +
            "                \"type\":\"uint256\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"name\":\"Mint\",\n" +
            "        \"type\":\"event\"\n" +
            "    }";
    static {
        TRANSFER = TRANSFER.replaceAll("'", "\"");
        EOS_MAPPING = EOS_MAPPING.replaceAll("'", "\"");
        PG_MINT = PG_MINT.replaceAll("'", "\"");
    }
}
