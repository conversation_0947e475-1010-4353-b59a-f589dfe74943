package com.coldlar.coin.ethereum.util;

/**
 * Created by Administrator on 2017/7/29 0029.
 */

public class EthContractUtil {
    public static String funcJson1 = "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'_to', 'type':'address'},{'name':'_value', 'type':'uint256'}], \n" +
            "  'name': 'transfer', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";

    static {
        funcJson1 = funcJson1.replaceAll("'", "\"");
    }
    public static String transferABI = "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'_to', 'type':'address'},{'name':'_value', 'type':'uint256'}], \n" +
            "  'name': 'transfer', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";

    public static String registerABI= "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'key', 'type':'string'}], \n" +
            "  'name': 'register', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";

    static {
        transferABI = transferABI.replaceAll("'", "\"");
        registerABI = registerABI.replaceAll("'", "\"");
    }
}
