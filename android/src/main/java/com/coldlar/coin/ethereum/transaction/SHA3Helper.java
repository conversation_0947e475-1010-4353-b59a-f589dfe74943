package com.coldlar.coin.ethereum.transaction;


import org.bouncycastle.crypto.digests.SHA3Digest;

/**
 * Created by Administrator on 2017/5/2 0002.
 */
public class SHA3Helper
{
    private static byte[] doSha3(byte[] paramArrayOfByte, SHA3Digest paramSHA3Digest, boolean paramBoolean)
    {
        byte[] arrayOfByte = new byte[paramSHA3Digest.getDigestSize()];
        if (paramArrayOfByte.length != 0)
            paramSHA3Digest.update(paramArrayOfByte, 0, paramArrayOfByte.length);
        paramSHA3Digest.doFinal(arrayOfByte, 0);
        return arrayOfByte;
    }

    public static byte[] sha3(byte[] paramArrayOfByte)
    {
        return sha3(paramArrayOfByte, new SHA3Digest(256), true);
    }

    private static byte[] sha3(byte[] paramArrayOfByte, SHA3Digest paramSHA3Digest, boolean paramBoolean)
    {
        return doSha3(paramArrayOfByte, paramSHA3Digest, paramBoolean);
    }
}