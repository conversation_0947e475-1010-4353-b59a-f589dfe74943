package com.coldlar.coin.ethereum.transaction;

/**
 * Created by huyx on 2017/7/29 0029.
 * 常用ABI
 */

public class ERC20_ABI {
    public static String TRANSFER = "{ \n" +
            "  'constant': false, \n" +
            "  'inputs': [{'name':'_to', 'type':'address'},{'name':'_value', 'type':'uint256'}], \n" +
            "  'name': 'transfer', \n" +
            "  'outputs': [], \n" +
            "  'type': 'function' \n" +
            "} \n";



    static {
        TRANSFER = TRANSFER.replaceAll("'", "\"");
    }
}
