package com.coldlar.coin.ethereum;




import static com.coldlar.coin.ethereum.transaction.FunctionUtil.sha3;
import static org.apache.commons.lang3.ArrayUtils.subarray;
import static org.apache.commons.lang3.StringUtils.stripEnd;
import static java.lang.String.format;

import com.coldlar.coin.ethereum.util.ByteUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.spongycastle.util.encoders.Hex;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.lang.reflect.Array;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@SuppressWarnings("unchecked")
public class CallTransaction {

    private final static ObjectMapper DEFAULT_MAPPER = new ObjectMapper()
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);

    public static Transaction createCallTransaction(long nonce, long gasPrice, long gasLimit, String toAddress,
                                                    long value, Function callFunc, Object... funcArgs) {

        byte[] callData = callFunc.encode(funcArgs);
        return createRawTransaction(nonce, gasPrice, gasLimit, toAddress, value, callData);
    }

    public static byte[] getCallData(Function callFunc, Object... funcArgs) {
        return callFunc.encode(funcArgs);
    }

    public static Transaction createRawTransaction(long nonce, long gasPrice, long gasLimit, String toAddress, long value, byte[] paramArrayOfByte) {
        byte[] arrayOfByte1 = ByteUtil.longToBytesNoLeadZeroes(nonce);
        byte[] arrayOfByte2 = ByteUtil.longToBytesNoLeadZeroes(gasPrice);
        byte[] arrayOfByte3 = ByteUtil.longToBytesNoLeadZeroes(gasLimit);
        if (toAddress == null) ;
        for (byte[] arrayOfByte4 = null; ; arrayOfByte4 = Hex.decode(toAddress))
            return new Transaction(arrayOfByte1, arrayOfByte2, arrayOfByte3, arrayOfByte4, ByteUtil.longToBytesNoLeadZeroes(value), paramArrayOfByte, null);
    }

    public static class AddressType extends CallTransaction.IntType {

        public AddressType(String paramString) {
            super(paramString);
        }

        public byte[] encode(Object paramObject) {
            if (((paramObject instanceof String)) && (!((String) paramObject).startsWith("0x")))
                paramObject = "0x" + paramObject;
            byte[] arrayOfByte = super.encode(paramObject);
            for (int i = 0; i < 12; i++)
                if (arrayOfByte[i] != 0)
                    throw new RuntimeException("Invalid address (should be 20 bytes length): " + Hex.toHexString(arrayOfByte));
            return arrayOfByte;
        }
    }

    public static abstract class ArrayType extends CallTransaction.Type {
        CallTransaction.Type elementType;

        public ArrayType(String paramString) {
            super(paramString);
            int i = paramString.indexOf("[");
            String str1 = paramString.substring(0, i);
            int j = paramString.indexOf("]", i);
            if (j + 1 == paramString.length()) ;
            for (String str2 = ""; ; str2 = paramString.substring(j + 1)) {
                this.elementType = CallTransaction.Type.getType(str1 + str2);
                return;
            }
        }

        public static ArrayType getType(String paramString) {
            int i = paramString.indexOf("[");
            int j = paramString.indexOf("]", i);
            if (i + 1 == j)
                return new CallTransaction.DynamicArrayType(paramString);
            return new CallTransaction.StaticArrayType(paramString);
        }

        public byte[] encode(Object paramObject) {
            if (paramObject.getClass().isArray()) {
                ArrayList localArrayList = new ArrayList();
                for (int i = 0; i < Array.getLength(paramObject); i++)
                    localArrayList.add(Array.get(paramObject, i));
                return encodeList(localArrayList);
            }
            if ((paramObject instanceof List))
                return encodeList((List) paramObject);
            throw new RuntimeException("List value expected for type " + getName());
        }

        public abstract byte[] encodeList(List paramList);
    }

    public static class BoolType extends CallTransaction.IntType {

        public BoolType(String paramString) {
            super(paramString);
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            if (((Number) super.decode(paramArrayOfByte, paramInt)).intValue() != 0) ;
            for (boolean bool = true; ; bool = false)
                return Boolean.valueOf(bool);
        }

        public byte[] encode(Object paramObject) {
            if ((paramObject instanceof String)) {
                if ((((String) paramObject).equalsIgnoreCase("true")) || (((String) paramObject).equalsIgnoreCase("1")))
                    return super.encode(Integer.valueOf(1));
                if ((((String) paramObject).equalsIgnoreCase("false")) || (((String) paramObject).equalsIgnoreCase("0")))
                    return super.encode(Integer.valueOf(0));
            }
            if (!(paramObject instanceof Boolean))
                throw new RuntimeException("Wrong value for bool type: " + paramObject);
            if (paramObject == Boolean.TRUE) ;
            for (int i = 1; ; i = 0)
                return super.encode(Integer.valueOf(i));
        }
    }

    public static class Bytes32Type extends CallTransaction.Type {
        public Bytes32Type(String paramString) {
            super(paramString);
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            return Arrays.copyOfRange(paramArrayOfByte, paramInt, paramInt + getFixedSize());
        }

        public byte[] encode(Object paramObject) {
            if ((paramObject instanceof Number))
                return CallTransaction.IntType.encodeInt(new BigInteger(paramObject.toString()));
            if ((paramObject instanceof String)) {
                byte[] arrayOfByte1 = new byte[32];
                byte[] arrayOfByte2 = ((String) paramObject).getBytes(Charset.forName("utf-8"));
                System.arraycopy(arrayOfByte2, 0, arrayOfByte1, 0, arrayOfByte2.length);
                return arrayOfByte1;
            }
            return new byte[0];
        }
    }

    public static class BytesType extends CallTransaction.Type {

        protected BytesType(String paramString) {
            super(paramString);
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            int i = CallTransaction.IntType.decodeInt(paramArrayOfByte, paramInt).intValue();
            if (i == 0)
                return new byte[0];
            int j = paramInt + 32;
            return Arrays.copyOfRange(paramArrayOfByte, j, j + i);
        }

        public byte[] encode(Object paramObject) {
            if (!(paramObject instanceof byte[]))
                throw new RuntimeException("byte[] value expected for type 'bytes'");
            byte[] arrayOfByte1 = (byte[]) paramObject;
            byte[] arrayOfByte2 = new byte[32 * (1 + (-1 + arrayOfByte1.length) / 32)];
            System.arraycopy(arrayOfByte1, 0, arrayOfByte2, 0, arrayOfByte1.length);
            byte[][] arrayOfByte = new byte[2][];
            arrayOfByte[0] = CallTransaction.IntType.encodeInt(arrayOfByte1.length);
            arrayOfByte[1] = arrayOfByte2;
            return ByteUtil.merge(arrayOfByte);
        }

        public boolean isDynamicType() {
            return true;
        }
    }

    public static class Contract {
        public Function[] functions;

        public Contract(String jsonInterface) {
            try {
                functions = new ObjectMapper().readValue(jsonInterface, Function[].class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        public Function getByName(String name) {
            for (Function function : functions) {
                if (name.equals(function.name)) {
                    return function;
                }
            }
            return null;
        }

        public Function getConstructor() {
            for (Function function : functions) {
                if (function.type == FunctionType.constructor) {
                    return function;
                }
            }
            return null;
        }

        private Function getBySignatureHash(byte[] hash) {
            if (hash.length == 4) {
                for (Function function : functions) {
                    if (FastByteComparisons.equal(function.encodeSignature(), hash)) {
                        return function;
                    }
                }
            } else if (hash.length == 32) {
                for (Function function : functions) {
                    if (FastByteComparisons.equal(function.encodeSignatureLong(), hash)) {
                        return function;
                    }
                }
            } else {
                throw new RuntimeException("Function signature hash should be 4 or 32 bytes length");
            }
            return null;
        }

    }

    public static class DynamicArrayType extends CallTransaction.ArrayType {


        public DynamicArrayType(String paramString) {
            super(paramString);
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            int i = CallTransaction.IntType.decodeInt(paramArrayOfByte, paramInt).intValue();
            int j = paramInt + 32;
            int k = j;
            Object[] arrayOfObject = new Object[i];
            int m = 0;
//            if (m < i) {
//                if (this.elementType.isDynamicType())
//                    arrayOfObject[m] = this.elementType.decode(paramArrayOfByte, j + CallTransaction.IntType.decodeInt(paramArrayOfByte, k).intValue());
//                while (true) {
//                    k += this.elementType.getFixedSize();
//                    m++;
//                    break;
//                    arrayOfObject[m] = this.elementType.decode(paramArrayOfByte, k);
//                }
//            }
            return arrayOfObject;
        }

        public byte[] encodeList(List paramList) {
            if (this.elementType.isDynamicType()) {
//                arrayOfByte = new byte[1 + 2 * paramList.size()][];
//                arrayOfByte[0] = CallTransaction.IntType.encodeInt(paramList.size());
//                int j = 32 * paramList.size();
//                for (int k = 0; k < paramList.size(); k++) {
//                    arrayOfByte[(k + 1)] = CallTransaction.IntType.encodeInt(j);
//                    byte[] arrayOfByte1 = this.elementType.encode(paramList.get(k));
//                    arrayOfByte[(1 + (k + paramList.size()))] = arrayOfByte1;
//                    j += 32 * (1 + (-1 + arrayOfByte1.length) / 32);
//                }
            }
            byte[][] arrayOfByte = new byte[1 + paramList.size()][];
            arrayOfByte[0] = CallTransaction.IntType.encodeInt(paramList.size());
            for (int i = 0; i < paramList.size(); i++)
                arrayOfByte[(i + 1)] = this.elementType.encode(paramList.get(i));
            return ByteUtil.merge(arrayOfByte);
        }

        public String getCanonicalName() {
            return this.elementType.getCanonicalName() + "[]";
        }

        public boolean isDynamicType() {
            return true;
        }
    }

    public static class Function {
        public boolean anonymous;
        public boolean constant;
        public String name = "";
        public Param[] inputs = new Param[0];
        public Param[] outputs = new Param[0];
        public FunctionType type;

        private Function() {
        }

        public byte[] encode(Object... args) {
            return ByteUtil.merge(encodeSignature(), encodeArguments(args));
        }

        public byte[] encodeArguments(Object... args) {
            if (args.length > inputs.length)
                throw new RuntimeException("Too many arguments: " + args.length + " > " + inputs.length);

            int staticSize = 0;
            int dynamicCnt = 0;
            // calculating static size and number of dynamic params
            for (int i = 0; i < args.length; i++) {
                Param param = inputs[i];
                if (param.type.isDynamicType()) {
                    dynamicCnt++;
                }
                staticSize += param.type.getFixedSize();
            }

            byte[][] bb = new byte[args.length + dynamicCnt][];

            int curDynamicPtr = staticSize;
            int curDynamicCnt = 0;
            for (int i = 0; i < args.length; i++) {
                if (inputs[i].type.isDynamicType()) {
                    byte[] dynBB = inputs[i].type.encode(args[i]);
                    bb[i] = SolidityType.IntType.encodeInt(curDynamicPtr);
                    bb[args.length + curDynamicCnt] = dynBB;
                    curDynamicCnt++;
                    curDynamicPtr += dynBB.length;
                } else {
                    bb[i] = inputs[i].type.encode(args[i]);
                }
            }
            return ByteUtil.merge(bb);
        }

        private Object[] decode(byte[] encoded, Param[] params) {
            Object[] ret = new Object[params.length];

            int off = 0;
            for (int i = 0; i < params.length; i++) {
                if (params[i].type.isDynamicType()) {
                    ret[i] = params[i].type.decode(encoded, IntType.decodeInt(encoded, off).intValue());
                } else {
                    ret[i] = params[i].type.decode(encoded, off);
                }
                off += params[i].type.getFixedSize();
            }
            return ret;
        }

        public Object[] decode(byte[] encoded) {
            return decode(subarray(encoded, 4, encoded.length), inputs);
        }

        public Object[] decodeResult(byte[] encodedRet) {
            return decode(encodedRet, outputs);
        }

        public String formatSignature() {
            StringBuilder paramsTypes = new StringBuilder();
            for (Param param : inputs) {
                paramsTypes.append(param.type.getCanonicalName()).append(",");
            }

            return format("%s(%s)", name, stripEnd(paramsTypes.toString(), ","));
        }

        public byte[] encodeSignatureLong() {
            String signature = formatSignature();
            byte[] sha3Fingerprint = sha3(signature.getBytes());
            return sha3Fingerprint;
        }

        public byte[] encodeSignature() {
            return Arrays.copyOfRange(encodeSignatureLong(), 0, 4);
        }

        @Override
        public String toString() {
            return formatSignature();
        }

        public static Function fromJsonInterface(String json) {
            try {
                return DEFAULT_MAPPER.readValue(json, Function.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        public static Function fromSignature(String funcName, String... paramTypes) {
            return fromSignature(funcName, paramTypes, new String[0]);
        }

        public static Function fromSignature(String funcName, String[] paramTypes, String[] resultTypes) {
            Function ret = new Function();
            ret.name = funcName;
            ret.constant = false;
            ret.type = FunctionType.function;
            ret.inputs = new Param[paramTypes.length];
            for (int i = 0; i < paramTypes.length; i++) {
                ret.inputs[i] = new Param();
                ret.inputs[i].name = "param" + i;
                ret.inputs[i].type = SolidityType.getType(paramTypes[i]);
            }
            ret.outputs = new Param[resultTypes.length];
            for (int i = 0; i < resultTypes.length; i++) {
                ret.outputs[i] = new Param();
                ret.outputs[i].name = "res" + i;
                ret.outputs[i].type = SolidityType.getType(resultTypes[i]);
            }
            return ret;
        }

    }

    enum FunctionType {
        constructor,
        function,
        event,
        fallback

    }

    public static class IntType extends CallTransaction.Type {
        public IntType(String paramString) {
            super(paramString);
        }

        public static BigInteger decodeInt(byte[] paramArrayOfByte, int paramInt) {
            return new BigInteger(Arrays.copyOfRange(paramArrayOfByte, paramInt, paramInt + 32));
        }

        public static byte[] encodeInt(int paramInt) {
            return encodeInt(new BigInteger("" + paramInt));
        }

        public static byte[] encodeInt(BigInteger paramBigInteger) {
            byte[] arrayOfByte1 = new byte[32];
            if (paramBigInteger.signum() < 0) ;
            for (byte b = -1; ; b = 0) {
                Arrays.fill(arrayOfByte1, b);
                byte[] arrayOfByte2 = paramBigInteger.toByteArray();
                System.arraycopy(arrayOfByte2, 0, arrayOfByte1, 32 - arrayOfByte2.length, arrayOfByte2.length);
                return arrayOfByte1;
            }
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            return decodeInt(paramArrayOfByte, paramInt);
        }

        public byte[] encode(Object paramObject) {
            String str;
            int i;
            BigInteger localBigInteger;
            if ((paramObject instanceof String)) {
                str = ((String) paramObject).toLowerCase().trim();
                i = 10;
                if (str.startsWith("0x")) {
                    str = str.replace("0x", "");
                    i = 16;
                    localBigInteger = new BigInteger(str, i);
                }
            }
//            while (true) {
//                return encodeInt(localBigInteger);
//                if ((!str.contains("a")) && (!str.contains("b")) && (!str.contains("c")) && (!str.contains("d")) && (!str.contains("e")) && (!str.contains("f")))
//                    break;
//                i = 16;
//                break;
//                if ((paramObject instanceof BigInteger)) {
//                    localBigInteger = (BigInteger) paramObject;
//                } else {
//                    if (!(paramObject instanceof Number))
//                        break label158;
//                    localBigInteger = new BigInteger(paramObject.toString());
//                }
//            }
            label158:
            throw new RuntimeException("Invalid value for type '" + this + "': " + paramObject + " (" + paramObject.getClass() + ")");
        }

        public String getCanonicalName() {
            if (getName().equals("int"))
                return "int256";
            if (getName().equals("uint"))
                return "uint256";
            return super.getCanonicalName();
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Param {
        public Boolean indexed;
        public String name;
        public SolidityType type;

        @JsonGetter("type")
        public String getType() {
            return type.getName();
        }
    }

    public static class StaticArrayType extends CallTransaction.ArrayType {
        int size;

        public StaticArrayType(String paramString) {
            super(paramString);
            int i = paramString.indexOf("[");
            int j = paramString.indexOf("]", i);
            this.size = Integer.parseInt(paramString.substring(i + 1, j));
        }

        public Object[] decode(byte[] paramArrayOfByte, int paramInt) {
            Object[] arrayOfObject = new Object[this.size];
            for (int i = 0; i < this.size; i++)
                arrayOfObject[i] = this.elementType.decode(paramArrayOfByte, paramInt + i * this.elementType.getFixedSize());
            return arrayOfObject;
        }

        public byte[] encodeList(List paramList) {
            if (paramList.size() != this.size)
                throw new RuntimeException("List size (" + paramList.size() + ") != " + this.size + " for type " + getName());
            byte[][] arrayOfByte = new byte[this.size][];
            for (int i = 0; i < paramList.size(); i++)
                arrayOfByte[i] = this.elementType.encode(paramList.get(i));
            return ByteUtil.merge(arrayOfByte);
        }

        public String getCanonicalName() {
            return this.elementType.getCanonicalName() + "[" + this.size + "]";
        }

        public int getFixedSize() {
            return this.elementType.getFixedSize() * this.size;
        }
    }

    public static class StringType extends CallTransaction.BytesType {

        protected StringType(String paramString) {
            super(paramString);
        }

        public Object decode(byte[] paramArrayOfByte, int paramInt) {
            return new String((byte[]) super.decode(paramArrayOfByte, paramInt), Charset.forName("UTF-8"));
        }

        public byte[] encode(Object paramObject) {
            if (!(paramObject instanceof String))
                throw new RuntimeException("String value expected for type 'string'");
            return super.encode(((String) paramObject).getBytes(Charset.forName("UTF-8")));
        }
    }

    public static abstract class Type {
        protected String name;

        public Type(String paramString) {
            this.name = paramString;
        }

        public static Type getType(String paramString) {
            if (paramString.contains("["))
                return CallTransaction.ArrayType.getType(paramString);
            if ("bool".equals(paramString))
                return new CallTransaction.BoolType(paramString);
            if ((paramString.startsWith("int")) || (paramString.startsWith("uint")))
                return new CallTransaction.IntType(paramString);
            if ("address".equals(paramString))
                return new CallTransaction.AddressType(paramString);
            if ("string".equals(paramString))
                return new CallTransaction.StringType(paramString);
            if ("bytes".equals(paramString))
                return new CallTransaction.BytesType(paramString);
            if (paramString.startsWith("bytes"))
                return new CallTransaction.Bytes32Type(paramString);
            throw new RuntimeException("Unknown type: " + paramString);
        }

        public abstract Object decode(byte[] paramArrayOfByte, int paramInt);

        public abstract byte[] encode(Object paramObject);

        public String getCanonicalName() {
            return getName();
        }

        public int getFixedSize() {
            return 32;
        }

        public String getName() {
            return this.name;
        }

        public boolean isDynamicType() {
            return false;
        }

        public String toString() {
            return getName();
        }
    }


    public static String getERC721TransferBytes(String from, String to, String contract, BigInteger tokenIdBI) throws NumberFormatException {

        org.web3j.abi.datatypes.Function txFunc = getTransferFunction(from, to, contract, tokenIdBI);
        String encodedFunction = FunctionEncoder.encode(txFunc);
        return Numeric.cleanHexPrefix(encodedFunction);
    }


    public static org.web3j.abi.datatypes.Function getTransferFunction(String from, String to, String contract, BigInteger tokenIdBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function function = null;
        List<org.web3j.abi.datatypes.Type> params;
        List<TypeReference<?>> returnTypes = Collections.emptyList();
        if (tokenUsesLegacyTransfer(contract)) {
            params = Arrays.asList(new Address(to), new Uint256(tokenIdBI));
            function = new org.web3j.abi.datatypes.Function("transfer", params, returnTypes);
        } else {
            //function safeTransferFrom(address _from, address _to, uint256 _tokenId) external payable;
            params = Arrays.asList(new Address(from), new Address(to), new Uint256(tokenIdBI));
            function = new org.web3j.abi.datatypes.Function("safeTransferFrom", params, returnTypes);
        }
        return function;
    }

    private static boolean tokenUsesLegacyTransfer(String contract) throws NumberFormatException {
        switch (contract.toLowerCase()) {
            case "0x06012c8cf97bead5deae237070f9587f8e7a266d":
            case "0xabc7e6c01237e8eef355bba2bf925a730b714d5f":
            case "0x71c118b00759b0851785642541ceb0f4ceea0bd5":
            case "0x16baf0de678e52367adc69fd067e5edd1d33e3bf":
            case "0x7fdcd2a1e52f10c28cb7732f46393e297ecadda1":
                return true;

            default:
                return false;
        }
    }


    public static String getERC1155TransferBytes(String from, String to, BigInteger tokenIds, BigInteger amountBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function txFunc = getERC1155TransferFunction(from, to, tokenIds, amountBI);
        String encodedFunction = FunctionEncoder.encode(txFunc);
        return Numeric.cleanHexPrefix(encodedFunction);
    }


    public static  org.web3j.abi.datatypes.Function getERC1155TransferFunction(String from, String to, BigInteger tokenIdBI, BigInteger amountBI) throws NumberFormatException {
        org.web3j.abi.datatypes.Function function;
        List<org.web3j.abi.datatypes.Type> params;
        List<TypeReference<?>> returnTypes = Collections.emptyList();

        params = Arrays.asList(new Address(from), new Address(to), new Uint256(tokenIdBI),
                new Uint256(amountBI), new DynamicBytes(new byte[0]));
        function = new org.web3j.abi.datatypes.Function("safeTransferFrom", params, returnTypes);
        return function;
    }
}