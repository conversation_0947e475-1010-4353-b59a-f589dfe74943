package com.coldlar.coin.ethereum.layer;



import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class BlastMain extends CoinType {
    private static BlastMain instance = new BlastMain();

    private BlastMain() {
        this.id = "blast.main";
        this.name = "Blast";
        this.symbol = "BLAST";
        this. chain ="blast";
        this.showSymbol= "ETH";
        this.uriScheme = "blast";
        this.bip44Index = 60342;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "Blast";
        trName="Blast";
        this.coinID = ColdlarCoinID.ID_BLAST;
        this.isEVM = false;
        this.isLayer2 =true;
        this.chainId = 81457;
        this.isEthereumFamily =true;
        this.support1559Transactions =true;
        confirm=12;
        coinOrder=12;
        minFeeSat=new BigDecimal("0.01");
        maxFeeSat=new BigDecimal("1");
        estimateGasPrice = "100000000";
        gasLimitHex = "0x3F3FE";
        this.coinSort = 9;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BlastMain){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public int getEthereumChainId() {
        return 81457;
    }

}
