package com.coldlar.coin.ethereum;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.math.BigDecimal;
import java.util.Map;

public class EthereumMain extends CoinType {
    private static EthereumMain instance = new EthereumMain();

    private EthereumMain() {
        this.id = "ethereum.main";
        this.name = "Ethereum";
        this.symbol = "ETH";
        this.chain ="eth";
        this.showSymbol= "ETH";
        this.uriScheme = "ethereum";
        this.bip44Index = Integer.valueOf(60);
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "以太坊";
        trName="以太坊";
        this.coinID = ColdlarCoinID.ID_ETH;
        confirm=12;
        coinOrder=2;
        this.isEthereumFamily= true;
        this.chainId = 1;
        this.support1559Transactions =true;
        this.rpcUrl="https://cloudflare-eth.com";
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        estimateGasPrice = "10000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 1;
    }

    public static CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof EthereumMain){
            return true;
        }else {
            return false;
        }
    }


    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }
    public String addressFormat(String address) {
        String lowercaseAddress = Numeric.cleanHexPrefix(address).toLowerCase();
        String addressHash = Numeric.cleanHexPrefix(Hash.sha3String(lowercaseAddress));
        StringBuilder result = new StringBuilder(lowercaseAddress.length() + 2);
        result.append("0x");
        for (int i = 0; i < lowercaseAddress.length(); ++i) {
            if (Integer.parseInt(String.valueOf(addressHash.charAt(i)), 16) >= 8) {
                result.append(String.valueOf(lowercaseAddress.charAt(i)).toUpperCase());
            } else {
                result.append(lowercaseAddress.charAt(i));
            }
        }
        return result.toString();
    }



}