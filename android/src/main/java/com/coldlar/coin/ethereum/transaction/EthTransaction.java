package com.coldlar.coin.ethereum.transaction;

import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.Sha256Hash;
import org.bouncycastle.util.BigIntegers;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;

/**
 * Created by Administrator on 2017/5/2 0002.
 */
public class EthTransaction {
    private static final Logger logger = LoggerFactory.getLogger(EthTransaction.class);

    private byte[] chainID;

    /**
     * SHA3 hash of the RLP encoded transaction
     */
    private byte[] hash;

    /**
     * a counter used to make sure each transaction can only be processed once
     */
    private byte[] nonce;

    /**
     * the amount of ether to transfer (calculated as wei)
     */
    private byte[] value;

    /**
     * the address of the destination account
     * In creation transaction the receive address is - 0
     */
    private byte[] receiveAddress;

    /**
     * the amount of ether to pay as a transaction fee
     * to the miner for each unit of gas
     */
    private byte[] gasPrice;

    /**
     * the amount of "gas" to allow for the computation.
     * Gas is the fuel of the computational engine;
     * every computational step taken and every byte added
     * to the state or transaction list consumes some gas.
     */
    private byte[] gasLimit;

    /**
     * An unlimited size byte array specifying
     * input [data] of the message call or
     * Initialization code for a new contract
     */
    private byte[] data;

    /**
     * the elliptic curve signature
     * (including public key recovery bits)
     */
    private ECKey.ECDSASignature signature;

    /**
     * Tx in encoded form
     */
    private byte[] rlpEncoded;
    private byte[] rlpRaw;
    /**
     * Indicates if this transaction has been parsed
     * from the RLP-encoded data
     */
    private boolean parsed = false;

    public EthTransaction(byte[] rawData) {
        this.rlpEncoded = rawData;
        parsed = false;
    }


    /**
     * creation contract tx
     * [ nonce, gasPrice, gasLimit, "", endowment, init, signature(v, r, s) ]
     * or simple send tx
     * [ nonce, gasPrice, gasLimit, receiveAddress, value, data, signature(v, r, s) ]
     */
    private EthTransaction(byte[] nonce, byte[] gasPrice, byte[] gasLimit, byte[] receiveAddress, byte[] value, byte[] data, byte[] chainID) {
        this.nonce = nonce;
        this.gasPrice = gasPrice;
        this.gasLimit = gasLimit;
        this.receiveAddress = receiveAddress;
        this.value = value;
        this.data = data;
        this.chainID = chainID;
        if (receiveAddress == null) {
            this.receiveAddress = ByteUtil.EMPTY_BYTE_ARRAY;
        }
        parsed = true;
    }

    public static EthTransaction create(String receiveAddress, BigInteger value, BigInteger nonce, BigInteger gasPrice, BigInteger gasLimit, byte[] data, BigInteger chainID) {
        if (receiveAddress.startsWith("0x")) receiveAddress = receiveAddress.substring(2);
        return new EthTransaction(BigIntegers.asUnsignedByteArray(nonce),
                BigIntegers.asUnsignedByteArray(gasPrice),
                BigIntegers.asUnsignedByteArray(gasLimit),
                Hex.decode(receiveAddress),
                BigIntegers.asUnsignedByteArray(value),
                data,
                chainID.intValue() > 0 ?   BigIntegers.asUnsignedByteArray(chainID) : null);
    }

    private void rlpParse() {
        RLPList decodedTxList = RLP.decode2(rlpEncoded);
        RLPList transaction = (RLPList) decodedTxList.get(0);

        this.nonce = (transaction.get(0)).getRLPData();
        this.gasPrice = (transaction.get(1)).getRLPData();
        this.gasLimit = (transaction.get(2)).getRLPData();
        this.receiveAddress = (transaction.get(3)).getRLPData();
        this.value = (transaction.get(4)).getRLPData();

        this.data = (transaction.get(5)).getRLPData();
        // only parse signature in case tx is signed
        if ((transaction.get(6)).getRLPData() != null) {
            byte v = (transaction.get(6)).getRLPData()[0];
            byte[] r = (transaction.get(7)).getRLPData();
            byte[] s = (transaction.get(8)).getRLPData();
            this.signature = ECKey.ECDSASignature.fromComponents(r, s, v);
        } else {
            logger.debug("RLP encoded tx is not signed!");
        }
        this.parsed = true;
        this.hash = this.getHash();
    }

    public byte[] getHash() {
        if (!parsed) rlpParse();
        byte[] plainMsg = this.getEncodedRaw();
        return FunctionUtil.sha3(plainMsg);
    }

    public byte[] getNonce() {
        if (!parsed) rlpParse();

        if (nonce == null) return new byte[]{0};
        return nonce;
    }

    public byte[] getValue() {
        if (!parsed) rlpParse();
        return value;
    }

    public byte[] getReceiveAddress() {
        if (!parsed) rlpParse();
        return receiveAddress;
    }

    public byte[] getGasPrice() {
        if (!parsed) rlpParse();
        return gasPrice;
    }

    public byte[] getGasLimit() {
        if (!parsed) rlpParse();
        return gasLimit;
    }

    public byte[] getData() {
        if (!parsed) rlpParse();
        return data;
    }

    public ECKey.ECDSASignature getSignature() {
        if (!parsed) rlpParse();
        return signature;
    }

    public void setSignData(ECKey.ECDSASignature signData) {
        signature = signData;
    }

    public void sign(byte[] privKeyBytes) throws ECKey.MissingPrivateKeyException {
        byte[] hash = this.getHash();
        ECKey key = ECKey.fromPrivate(privKeyBytes).decompress();
        this.signature = key.sign(Sha256Hash.wrap(hash));
        this.rlpEncoded = null;
    }

    @Override
    public String toString() {
        if (!parsed) rlpParse();
        return "TransactionData [" + "hash=" + ByteUtil.toHexString(hash) +
                "  nonce=" + ByteUtil.toHexString(nonce) +
                ", gasPrice=" + ByteUtil.toHexString(gasPrice) +
                ", gas=" + ByteUtil.toHexString(gasLimit) +
                ", receiveAddress=" + ByteUtil.toHexString(receiveAddress) +
                ", value=" + ByteUtil.toHexString(value) +
                ", data=" + ByteUtil.toHexString(data) +
                ", signatureV=" + signature.v +
                ", signatureR=" + ByteUtil.toHexString(BigIntegers.asUnsignedByteArray(signature.r)) +
                ", signatureS=" + ByteUtil.toHexString(BigIntegers.asUnsignedByteArray(signature.s)) +
                "]";
    }


    /**
     * For signatures you have to keep also
     * RLP of the transaction without any signature data
     */
    public byte[] getEncodedRaw() {

        if (!parsed) rlpParse();
        if (rlpRaw != null) return rlpRaw;

        // parse null as 0 for nonce
        byte[] nonce = null;
        if (this.nonce == null || this.nonce.length == 1 && this.nonce[0] == 0) {
            nonce = RLP.encodeElement(null);
        } else {
            nonce = RLP.encodeElement(this.nonce);
        }
        byte[] gasPrice = RLP.encodeElement(this.gasPrice);
        byte[] gasLimit = RLP.encodeElement(this.gasLimit);
        byte[] receiveAddress = RLP.encodeElement(this.receiveAddress);
        byte[] value = RLP.encodeElement(this.value);
        byte[] data = RLP.encodeElement(this.data);

        if (this.chainID != null) { // EIP155
            byte[] chainID = RLP.encodeElement(this.chainID);
            this.rlpRaw = RLP.encodeList(nonce, gasPrice, gasLimit, receiveAddress,
                    value, data, chainID, RLP.encodeElement(new byte[0]), RLP.encodeElement(new byte[0]));
        } else {
            this.rlpRaw = RLP.encodeList(nonce, gasPrice, gasLimit, receiveAddress,
                    value, data);
        }

        return rlpRaw;
    }

    public byte[] getEncoded(byte[] vData) {

        if (rlpEncoded != null) return rlpEncoded;

        // parse null as 0 for nonce
        byte[] nonce = null;
        if (this.nonce == null || this.nonce.length == 1 && this.nonce[0] == 0) {
            nonce = RLP.encodeElement(null);
        } else {
            nonce = RLP.encodeElement(this.nonce);
        }
        byte[] gasPrice = RLP.encodeElement(this.gasPrice);
        byte[] gasLimit = RLP.encodeElement(this.gasLimit);
        byte[] receiveAddress = RLP.encodeElement(this.receiveAddress);
        byte[] value = RLP.encodeElement(this.value);
        byte[] data = RLP.encodeElement(this.data);
        byte[] v, r, s;

        if (signature != null) {
            v = RLP.encodeElement(vData);
            r = RLP.encodeElement(BigIntegers.asUnsignedByteArray(signature.r));
            s = RLP.encodeElement(BigIntegers.asUnsignedByteArray(signature.s));
        } else {
            v = RLP.encodeElement(new byte[0]);
            r = RLP.encodeElement(new byte[0]);
            s = RLP.encodeElement(new byte[0]);
        }

        this.rlpEncoded = RLP.encodeList(nonce, gasPrice, gasLimit,
                receiveAddress, value, data, v, r, s);
        return rlpEncoded;
    }

    @Override
    public int hashCode() {

        byte[] hash = this.getHash();
        int hashCode = 0;

        for (int i = 0; i < hash.length; ++i) {
            hashCode += hash[i] * i;
        }

        return hashCode;
    }

    @Override
    public boolean equals(Object obj) {

        if (!(obj instanceof EthTransaction)) return false;
        EthTransaction tx = (EthTransaction) obj;
        return tx.hashCode() == this.hashCode();
    }
}