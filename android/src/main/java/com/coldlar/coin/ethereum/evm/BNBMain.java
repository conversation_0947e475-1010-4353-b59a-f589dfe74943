package com.coldlar.coin.ethereum.evm;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class BNBMain extends CoinType {
    private static BNBMain instance = new BNBMain();

    private BNBMain() {
        this.id = "bnb.main";
        this.name = "BNB Smart Chain";
        this.symbol = "BSC";
        this. chain ="bsc";
        this.showSymbol= "BNB";
        this.uriScheme = "bnb smart chain";
        this.bip44Index = 9006;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "币安智能链";
        this.isEVM = true;
        this.isEthereumFamily= true;
        this.chainId = 56;
        this.rpcUrl="https://bsc-dataseed2.binance.org";
        trName="币安智能链";
        this.support1559Transactions =true;
        this.coinID = ColdlarCoinID.ID_BSC;
        confirm=12;
        coinOrder=3;
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        estimateGasPrice = "3000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 7;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof BNBMain){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public int getEthereumChainId() {
        return 56;
    }


}
