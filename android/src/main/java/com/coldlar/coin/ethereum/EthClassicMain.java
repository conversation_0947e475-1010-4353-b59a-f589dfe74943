package com.coldlar.coin.ethereum;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class EthClassicMain extends CoinType {
    private static EthClassicMain instance = new EthClassicMain();

    private EthClassicMain() {
        this.id = "ethclassic.main";
        this.name = "Ethereum Classic";
        this.symbol = "ETC";
        this.chain ="etc";
        this.showSymbol= "ETC";
        this.uriScheme = "ethclassic";
        this.bip44Index = Integer.valueOf(61);
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("20000000000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "以太经典";
        trName = "以太經典";
        this.coinID = ColdlarCoinID.ID_ETC;
        confirm=100;
        coinOrder=17;
        this.isEthereumFamily= true;
        this.chainId = 61;
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        maxSend=new BigDecimal("210000000");
        estimateGasPrice = "10000000000";
        gasLimitHex = "0x5208";
    }



    public static CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof EthClassicMain){
            return true;
        }else {
            return false;
        }
    }

}
