package com.coldlar.coin.ethereum.evm;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.FeePolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class OKTMain extends CoinType {
    private static OKTMain instance = new OKTMain();

    private OKTMain() {
        this.id = "okx.chain";
        this.name = "OKX Chain";
        this.symbol = "OKT";
        this.chain ="okt";
        this.showSymbol = "OKT";
        this.uriScheme = "bnb smart chain";
        this.bip44Index = 996;
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "OKX Chain";
        this.isEVM = true;
        this.isEthereumFamily= true;
        this.support1559Transactions =true;
        this.chainId = 66;
        trName="OKX Chain";
        this.coinID = ColdlarCoinID.ID_OKT;
        confirm=12;
        coinOrder=11;
        minFeeSat=new BigDecimal("1");
        maxFeeSat=new BigDecimal("2000");
        estimateGasPrice = "10000000000";
        gasLimitHex = "0x5208";
        this.coinSort = 12;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateEthereumFamilyAddress(address);
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof OKTMain){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public int getEthereumChainId() {
        return 66;
    }


}

