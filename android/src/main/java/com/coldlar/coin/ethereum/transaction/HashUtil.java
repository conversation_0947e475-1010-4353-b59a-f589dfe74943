package com.coldlar.coin.ethereum.transaction;


import static java.util.Arrays.copyOfRange;

import com.coldlar.coin.ethereum.transaction.crypto.Keccak256;
import com.coldlar.coin.ethereum.util.RLP;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.spongycastle.crypto.digests.RIPEMD160Digest;
import org.spongycastle.crypto.digests.SHA3Digest;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;

public class HashUtil {

    private static final MessageDigest sha256digest;

    static {
        Security.addProvider(new BouncyCastleProvider());
    }


    static {
        try {

            sha256digest = MessageDigest.getInstance("SHA-256");


        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);  // Can't happen.
        }
    }

    public static byte[] calcNewAddr(byte[] addr, byte[] nonce) {

        byte[] encSender = RLP.encodeElement(addr);
        byte[] encNonce = RLP.encodeBigInteger(new BigInteger(1, nonce));
        byte[] newAddress = sha3omit12(RLP.encodeList(encSender, encNonce));

        return newAddress;
    }

    public static byte[] ripemd160(byte[] paramArrayOfByte) {
        RIPEMD160Digest localRIPEMD160Digest = new RIPEMD160Digest();
        if (paramArrayOfByte != null) {
            byte[] arrayOfByte = new byte[localRIPEMD160Digest.getDigestSize()];
            localRIPEMD160Digest.update(paramArrayOfByte, 0, paramArrayOfByte.length);
            localRIPEMD160Digest.doFinal(arrayOfByte, 0);
            return arrayOfByte;
        }
        throw new NullPointerException("Can't hash a NULL value");
    }

    public static byte[] sha3omit12(byte[] input) {
        byte[] hash = sha3(input);
        return copyOfRange(hash, 12, hash.length);
    }

    public static byte[] sha256(byte[] input) {
        return sha256digest.digest(input);
    }

    public static byte[] sha3(byte[] paramArrayOfByte) {
        Keccak256 localKeccak256 = new Keccak256();
        localKeccak256.update(paramArrayOfByte);
        return localKeccak256.digest();
    }


    //TODO----------------------------------------

    /**
     * Performs a SHA3-256 hash of the concatenated inputs.
     *
     * @param inputs The byte arrays to concatenate and hash.
     * @return The hash of the concatenated inputs.
     * @throws CryptoException if the hash operation failed.
     */
    public static byte[] sha3_256(final byte[]... inputs) {
        return hash("SHA3-256", inputs);
    }

    /**
     * Performs a SHA3-512 hash of the concatenated inputs.
     *
     * @param inputs The byte arrays to concatenate and hash.
     * @return The hash of the concatenated inputs.
     * @throws CryptoException if the hash operation failed.
     */
    public static byte[] sha3_512(final byte[]... inputs) {
        SHA3Digest digest = new SHA3Digest(512);
        for (final byte[] input : inputs) {
            digest.update(input, 0, input.length);
        }
        byte[] signature = new byte[512 / 8];
        digest.doFinal(signature, 0);
        return signature;
    }


    private static byte[] hash(final String algorithm, final byte[]... inputs) throws CryptoException {
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance(algorithm, "BC");
            for (final byte[] input : inputs) {
                digest.update(input);
            }
            return digest.digest();
        } catch (Exception e) {
            throw new CryptoException("Hashing error: " + e.getMessage(), e);
        }
    }

    public static String calculateSHA256(String input) {
        try {
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256", "BC");
            byte[] hash = sha256.digest(input.getBytes("UTF-8"));

            // 将字节转换为十六进制表示
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("Could not hash input string", e);
        }
    }


}