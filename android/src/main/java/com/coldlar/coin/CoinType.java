package com.coldlar.coin;


import static com.google.common.base.Preconditions.checkNotNull;

import com.coldlar.coin.other.FeePolicy;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.exceptions.AddressMalformedException;
import com.coldlar.core.util.MonetaryFormat;
import com.coldlar.core.wallet.AbstractAddress;
import com.google.common.base.Charsets;

import org.bitcoinj.params.AbstractBitcoinNetParams;

import java.math.BigDecimal;
import java.util.Map;


abstract public class CoinType extends AbstractBitcoinNetParams {

    /**
     * 全称
     */
    protected String name;

    /**
     * 链名小写
     */
    protected String chain;
    /**
     * 链名
     */
    protected String symbol;

    /**
     * 币种
     */
    protected String showSymbol;
    /**
     * 币种全称小写，用着uri之类的地方
     */
    protected String uriScheme;
    /**
     * 小数位
     */
    protected Integer unitExponent;

    /**
     * 默认GasPrice 9次方
     */
    protected String estimateGasPrice;


    /**
     * 默认GasLimithex
     */
    protected String gasLimitHex;

    protected BigDecimal feeValue;
    protected BigDecimal minFeeValue;
    protected BigDecimal minNonDust;
    protected BigDecimal minGlobalNetwokFee;
    protected BigDecimal softDustLimit;
    protected String cnName;
    protected String trName;
    protected long chainId;
    public boolean isEVM = false;

    public boolean isLayer2 = false;

    public boolean isEthereumFamily = false;
    public boolean isBitCoinFamily = false;
    public boolean isBitcoinNetWork = false; //是BTC主网或者测试网

    public boolean  support1559Transactions = false;
    public boolean isTestNetWork = false; //是测试网

    protected String coinID;
    protected SoftDustPolicy softDustPolicy;
    protected FeePolicy feePolicy = FeePolicy.FEE_PER_KB;
    protected byte[] signedMessageHeader;
    private transient MonetaryFormat plainFormat;
    protected int coinSort;

    /**
     * 是否支持消息签名
     */
    protected boolean isSupportMsgSign = false;
    /**
     * 是否支持多签名
     */
    protected boolean isSupportMulSign = false;

    /**
     * 资产ID
     */
    protected String assetID;

    /**
     * 币种确认中状态的确认数 打包中、确认中、成功、失败
     */
    protected int confirm;
    protected int[] acceptableAddressCodes;
    protected float coinOrder = 0;

    /**
     * 最小手续费  sat/b
     */
    protected BigDecimal minFeeSat = new BigDecimal("1");


    /**
     * 最大手续费  sat/b   0.00010000 BTC/kb = 10 sat/b
     */
    protected BigDecimal maxFeeSat;
    /**
     * 收款金额，最大输入金额
     */
    protected BigDecimal maxSend;


    /**
     * 比特莱特 隔离验证P2SH 路径
     */
    protected String bip49Path;

    /**
     * 比特莱特 隔离验证bech32 路径
     */
    protected String bip84Path;

    /**
     * EOS类型，交易合约账户
     */
    protected String contract;

    abstract public boolean validateAddress(String address);

    abstract public String createAddress(Map<String, Object> map);

    abstract public String getPublicKey(Map<String, Object> map);


    public boolean isEthereumFamily() {
        return isEthereumFamily;
    }

    public void setEthereumFamily(boolean ethereumFamily) {
        isEthereumFamily = ethereumFamily;
    }

    public boolean isBitCoinFamily() {
        return isBitCoinFamily;
    }

    public void setBitCoinFamily(boolean bitCoinFamily) {
        isBitCoinFamily = bitCoinFamily;
    }

    abstract public String buildTransactionData(String jsonData);

    abstract public String splitTransactionSignature(String waitSignature,String signature);


    /**
     * 节点地址
     */
    protected String rpcUrl;

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public BigDecimal getMaxSend() {
        return maxSend;
    }

    public void setMaxSend(BigDecimal maxSend) {
        this.maxSend = maxSend;
    }

    public String getBip49Path() {
        return bip49Path;
    }

    public String getBip84Path() {
        return bip84Path;
    }

    public void setBip84Path(String bip84Path) {
        this.bip84Path = bip84Path;
    }

    public void setBip49Path(String bip49Path) {
        this.bip49Path = bip49Path;
    }

    public BigDecimal getMinFeeSat() {
        return minFeeSat;
    }

    public void setMinFeeSat(BigDecimal minFeeSat) {
        this.minFeeSat = minFeeSat;
    }

    public BigDecimal getMaxFeeSat() {
        return maxFeeSat;
    }

    public void setMaxFeeSat(BigDecimal maxFeeSat) {
        this.maxFeeSat = maxFeeSat;
    }

    public int[] getAcceptableAddressCodes() {
        return acceptableAddressCodes;
    }

    public void setAcceptableAddressCodes(int[] acceptableAddressCodes) {
        this.acceptableAddressCodes = acceptableAddressCodes;
    }

    public String getAssetID() {
        return assetID;
    }

    public void setAssetID(String assetID) {
        this.assetID = assetID;
    }

    public boolean isSupportMulSign() {
        return isSupportMulSign;
    }

    public void setSupportMulSign(boolean supportMulSign) {
        isSupportMulSign = supportMulSign;
    }

    public boolean isSupportMsgSign() {
        return isSupportMsgSign;
    }

    public void setSupportMsgSign(boolean supportMsgSign) {
        isSupportMsgSign = supportMsgSign;
    }


    public String getCoinID() {
        return coinID;
    }

    public long getChainId() {
        return chainId;
    }

    public String getChain() {
        return chain;
    }

    public void setCoinID(String coinID) {
        this.coinID = coinID;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getName() {
        return checkNotNull(name, "A coin failed to set a name");
    }

    public String getCnName() {
        return checkNotNull(cnName, "A coin failed to set a name");
    }

    public boolean isTestnet() {
        return id.endsWith("test");
    }

    public String getTrName() {
        return trName;
    }

    public void setTrName(String trName) {
        this.trName = trName;
    }

    public String getSymbol() {
        return checkNotNull(symbol, "A coin failed to set a symbol");
    }

    public String getShowSymbol() {
        return checkNotNull(showSymbol, "A coin failed to set a symbol");
    }

    @Override
    public String getUriScheme() {
        return checkNotNull(uriScheme, "A coin failed to set a URI scheme");
    }

    @Override
    public int getBip44Index() {
        return checkNotNull(bip44Index, "A coin failed to set a BIP 44 index");
    }

    public int getUnitExponent() {
        return checkNotNull(unitExponent, "A coin failed to set a unit exponent");
    }


    public SoftDustPolicy getSoftDustPolicy() {
        return checkNotNull(softDustPolicy, "A coin failed to set a soft dust policy");
    }

    public FeePolicy getFeePolicy() {
        return checkNotNull(feePolicy, "A coin failed to set a fee policy");
    }

    public byte[] getSignedMessageHeader() {
        return checkNotNull(signedMessageHeader, "A coin failed to set signed message header bytes");
    }

    public String getSignedMessageHeaderString() {
        return new String(signedMessageHeader);
    }

    public boolean canSignVerifyMessages() {
        return signedMessageHeader != null;
    }




    @Override
    public String getPaymentProtocolId() {
        throw new RuntimeException("Method not implemented");
    }

    @Override
    public String toString() {
        return "Coin{" +
                "name='" + name + '\'' +
                ", symbol='" + symbol + '\'' +
                ", bip44Index=" + bip44Index +
                '}';
    }


    public MonetaryFormat getPlainFormat() {
        if (plainFormat == null) {
            plainFormat = new MonetaryFormat().shift(0)
                    .minDecimals(0).repeatOptionalDecimals(1, unitExponent).noCode();
        }
        return plainFormat;
    }


    public void setName(String name) {
        this.name = name;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public void setBip44Index(Integer bip44Index) {
        this.bip44Index = bip44Index;
    }

    public BigDecimal getFeeValue() {
        return feeValue;
    }

    public void setFeeValue(BigDecimal feeValue) {
        this.feeValue = feeValue;
    }

    public BigDecimal getMinFeeValue() {
        return minFeeValue;
    }

    public void setMinFeeValue(BigDecimal minFeeValue) {
        this.minFeeValue = minFeeValue;
    }

    public BigDecimal getMinNonDust() {
        return minNonDust;
    }

    public void setMinNonDust(BigDecimal minNonDust) {
        this.minNonDust = minNonDust;
    }

    public BigDecimal getSoftDustLimit() {
        return softDustLimit;
    }

    public void setSoftDustLimit(BigDecimal softDustLimit) {
        this.softDustLimit = softDustLimit;
    }

    public void setFeePolicy(FeePolicy feePolicy) {
        this.feePolicy = feePolicy;
    }

    protected static byte[] toBytes(String str) {
        return str.getBytes(Charsets.UTF_8);
    }


    /**
     * 获取Bip44路径
     *
     * @return Path
     */
    public String getBip44KeyPath() {
        return String.format("m/44'/%s'/0'/0", this.bip44Index);
    }

    /**
     * 获取隔离验证P2SH路径
     *
     * @return Path
     */
    public String getP2shKeyPath() {
        return String.format("m/44'/%s'/0'/0", this.bip44Index);
    }

    public float getCoinOrder() {
        return coinOrder;
    }

    public void setCoinOrder(float coinOrder) {
        this.coinOrder = coinOrder;
    }


    public int getConfirm() {
        return confirm;
    }

    public void setConfirm(int confirm) {
        this.confirm = confirm;
    }

    public abstract AbstractAddress newAddress(String addressStr) throws AddressMalformedException;

    public abstract boolean coinTypeEquals(CoinType coinType);

    public String getRpcUrl() {
        return rpcUrl;
    }

    public void setRpcUrl(String rpcUrl) {
        this.rpcUrl = rpcUrl;
    }


    public String getEstimateGasPrice() {
        return estimateGasPrice;
    }

    public void setEstimateGasPrice(String estimateGasPrice) {
        this.estimateGasPrice = estimateGasPrice;
    }


    public String getGasLimitHex() {
        return gasLimitHex;
    }

    public void setGasLimitHex(String gasLimitHex) {
        this.gasLimitHex = gasLimitHex;
    }


    public int getCoinSort() {
        return coinSort;
    }

    public void setCoinSort(int coinSort) {
        this.coinSort = coinSort;
    }


    public boolean isTestNetWork() {
        return isTestNetWork;
    }

    public void setTestNetWork(boolean testNetWork) {
        isTestNetWork = testNetWork;
    }

    public boolean isBitcoinNetWork() {
        return isBitcoinNetWork;
    }

    public void setBitcoinNetWork(boolean bitcoinNetWork) {
        isBitcoinNetWork = bitcoinNetWork;
    }


    public boolean isSupport1559Transactions() {
        return support1559Transactions;
    }

    public void setSupport1559Transactions(boolean support1559Transactions) {
        this.support1559Transactions = support1559Transactions;
    }

}

