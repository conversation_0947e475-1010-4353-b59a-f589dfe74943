package com.coldlar.coin.xlm;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import org.stellar.sdk.KeyPair;

import java.math.BigDecimal;
import java.util.Map;


public class XlmMain extends CoinType {
    private XlmMain() {
        id = "xlm.main";

        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_XLM;
        name = "Stellar";
        cnName = "恒星币";
        trName = "XLM";
        symbol = "XLM";
        chain = "xlm";
        showSymbol = "XLM";
        uriScheme = "xlm";
        bip44Index = 148;
        unitExponent = 7;
        feeValue = new BigDecimal("100");

        minNonDust =new BigDecimal("100"); 
        minGlobalNetwokFee = new BigDecimal("100");
        softDustLimit = new BigDecimal("100"); // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm=12;
        coinOrder=11;
        maxSend=new BigDecimal("***********");
    }

    private static XlmMain instance = new XlmMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        try {
            KeyPair keyPair = KeyPair.fromAccountId(address);
            if (null != keyPair.getAccountId()) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }

        return false;
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof XlmMain){
            return true;
        }else {
            return false;
        }
    }

}
