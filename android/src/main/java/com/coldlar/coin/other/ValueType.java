package com.coldlar.coin.other;

import com.coldlar.core.util.MonetaryFormat;

import org.bitcoinj.core.Coin;

import java.io.Serializable;


public interface ValueType extends Serializable {
    String getId();
    String getName();
    String getCnName();
    String getCoinID();
    String getSymbol();
    int getUnitExponent();

    /**
     * Typical 1 coin value, like 1 Bitcoin, 1 Peercoin or 1 Dollar
     */
    Value oneCoin();

    /**
     * Get the minimum valid amount that can be sent a.k.a. dust amount or minimum input
     */
    Value getMinNonDust();

    /**
     * 全网最低每KB手续费
     */
    Value getGlobalNetworkMinFee();

    Value value(Coin coin);

    Value value(long units);

    MonetaryFormat getMonetaryFormat();
    MonetaryFormat getPlainFormat();

    boolean equals(ValueType obj);

    Value value(String string);
}
