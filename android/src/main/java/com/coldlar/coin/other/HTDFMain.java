package com.coldlar.coin.other;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

/**
 * +
 */
public class HTDFMain extends CoinType {
    private HTDFMain() {
        id = "hdtf.main";

        addressHeader = 30;
        p2shHeader = 22;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 240; // COINBASE_MATURITY_NEW
        dumpedPrivateKeyHeader = 158;


        cnName = "华特东方";
        trName = "华特东方";
        coinID = ColdlarCoinID.ID_HTDF;
        name = "Orient Walt";
        chain ="htdf";
        symbol = "HTDF";
        showSymbol= "HTDF";
        uriScheme = "htdf";
        bip44Index = 346;
        unitExponent = 8;
        feeValue =new BigDecimal("20");
        minNonDust =new BigDecimal("100000");
        softDustPolicy = SoftDustPolicy.BASE_FEE_FOR_EACH_SOFT_DUST_TXO;
        signedMessageHeader = toBytes("Dogecoin Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm=100;
        coinOrder=28;
    }

    private static HTDFMain instance = new HTDFMain();

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof HTDFMain){
            return true;
        }else {
            return false;
        }
    }

}
