package com.coldlar.coin.other;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class WiccMain extends CoinType {
    private WiccMain() {
        id = "waykichain.main";

        addressHeader = 73;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 153;

        coinID =ColdlarCoinID.ID_WICC;
        name = "Wayki<PERSON>hain";
        cnName = "维基链";
        trName = "维基链";
        symbol = "WICC";
        chain = "wicc";
        showSymbol = "WICC";
        uriScheme = "WaykiChain";
        bip44Index = 99999;
        unitExponent = 8;
        feeValue =new BigDecimal("10000000");

        minNonDust =new BigDecimal("10000"); 
        minGlobalNetwokFee =new BigDecimal("1000"); 
        softDustLimit =new BigDecimal("1000");  // 0.01 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("WaykiChain Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm=100;
        coinOrder=25;
    }

    private static WiccMain instance = new WiccMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof WiccMain){
            return true;
        }else {
            return false;
        }
    }

}
