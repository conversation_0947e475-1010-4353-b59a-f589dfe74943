package com.coldlar.coin.other;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

public class PocMain extends CoinType implements Serializable {
    private PocMain() {
        id = "poc.main";

        addressHeader = 0;
        p2shHeader = 5;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_POC;
        name = "POC Chain";
        cnName = "POC Chain";
        trName = "POC Chain";
        symbol = "POC";
        chain = "poc";
        showSymbol = "POC";
        uriScheme = "poc";
        bip44Index = 3897465;;
        unitExponent = 18;
        feeValue = new BigDecimal("10000000000000");
        minNonDust = new BigDecimal("5460");
        minGlobalNetwokFee = new BigDecimal("1000");
        softDustLimit = new BigDecimal("1000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm=20;
        coinOrder=27;
    }

    private static PocMain instance = new PocMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof PocMain){
            return true;
        }else {
            return false;
        }
    }

}
