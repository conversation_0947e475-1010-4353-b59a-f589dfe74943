package com.coldlar.coin.other;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class WwbMain extends CoinType {
    protected WwbMain() {
        id = "wwbcoin.main";
        addressHeader = 23;//单地址头
        p2shHeader = 23;//多签名地址头
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;//私钥头

        coinID = ColdlarCoinID.ID_WWB;//自定义的ID
        name = "Wowbit";
        cnName = "Wowbit";
        symbol = "WWB";//单位
        chain = "wwb";
        showSymbol = "WWB";
        trName = "Wowbit";
        uriScheme = "wwbcoin";//
        bip44Index = -1;//分层确定性
        unitExponent = 8; //小数位为8为小数
        feeValue = new BigDecimal("20000");//交易的默认手续费
        minFeeValue = new BigDecimal("1000");
        minNonDust = new BigDecimal("5460"); //比特币的粉尘大小
        softDustLimit = new BigDecimal("10000"); // 软件设置的粉尘大小
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");//签名头信息
        isSupportMsgSign = false;
        confirm=12;
        coinOrder=105;
    }

    private static WwbMain instance = new WwbMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof WwbMain){
            return true;
        }else {
            return false;
        }
    }


}
