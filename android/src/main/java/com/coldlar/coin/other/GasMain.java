package com.coldlar.coin.other;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class GasMain extends CoinType {
    protected GasMain() {
        id = "gascoin.main";
        //单地址头
        addressHeader = 23;
        //多签名地址头
        p2shHeader = 23;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        //私钥头
        dumpedPrivateKeyHeader = 128;
        //自定义的ID
        coinID = ColdlarCoinID.ID_GAS;
        name = "Gas";
        cnName = "Gas";
        //单位
        symbol = "GAS";
        chain ="gas";
        showSymbol= "GAS";
        trName="Gas";
        uriScheme = "gascoin";
        //分层确定性
        bip44Index = -1;
        //小数位为8为小数
        unitExponent = 8;
        //交易的默认手续费
        feeValue = new BigDecimal("20000");
        minFeeValue = new BigDecimal("1000");
        minNonDust = new BigDecimal("5460");
        softDustLimit =new BigDecimal("10000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        confirm=12;
        coinOrder=105;
    }

    private static GasMain instance = new GasMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }


    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof GasMain){
            return true;
        }else {
            return false;
        }
    }

}
