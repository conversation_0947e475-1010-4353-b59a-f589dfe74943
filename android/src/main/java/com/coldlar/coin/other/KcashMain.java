package com.coldlar.coin.other;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class KcashMain extends CoinType {
    private KcashMain() {
        id = "kcash.main";

        addressHeader = 53;
        p2shHeader = 54;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_KCASH;
        name = "KCash";
        cnName = "KCash";
        chain ="kcash";
        symbol = "KCASH";
        showSymbol = "KCASH";
        trName="KCash";
        uriScheme = "KCash";
        bip44Index = 0;
        bip44Index = -1;
        unitExponent = 5;
        feeValue =new BigDecimal("1000");
        minNonDust = new BigDecimal("10000");
        softDustLimit = new BigDecimal("1000"); // 0.001 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Achain Signed Message:\n");
        confirm=20;
        coinOrder=105;
    }

    private static KcashMain instance = new KcashMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof KcashMain){
            return true;
        }else {
            return false;
        }
    }

}
