package com.coldlar.coin.other;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class TomoMain extends CoinType {
    private static TomoMain instance = new TomoMain();

    private TomoMain() {
        this.id = "tomochain.main";
        this.name = "TomoChain";
        this.symbol = "TOMO";
        this .chain = "tomo";
        this.showSymbol = "TOMO";
        this.uriScheme = "TomoChain";
        this.bip44Index = Integer.valueOf(889);
        this.unitExponent = Integer.valueOf(18);
        this.feeValue = new BigDecimal("200000");
        this.minNonDust = new BigDecimal("1");
        this.feePolicy = FeePolicy.FEE_GAS_PRICE;
        this.cnName = "TOMO";
        trName = "TOMO";
        this.coinID = ColdlarCoinID.ID_TOMO;
        isSupportMsgSign = false;
        isSupportMulSign = false;
        confirm = 100;
        coinOrder = 26;
    }

    public static CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().  validateEthereumFamilyAddress(address);
    }
    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof TomoMain) {
            return true;
        } else {
            return false;
        }
    }

}