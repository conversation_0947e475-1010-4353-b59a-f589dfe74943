package com.coldlar.coin.other;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class FscMain extends CoinType {
    private FscMain() {
        id = "fas.main";

        addressHeader = 53;
        p2shHeader = 54;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID = ColdlarCoinID.ID_FSC;
        name = "FSharesCoin";
        cnName = "FSharesCoin";
        trName="FSharesCoin";
        symbol = "FSC";
        chain ="fsc";
        showSymbol= "FSC";
        uriScheme = "fscio";
        contract = "fscio.token";
        bip44Index = 9527;
        unitExponent = 8;
        feeValue =new BigDecimal("200000");
        minNonDust = new BigDecimal("10000");
        softDustLimit = new BigDecimal("1000");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        confirm=12;
        coinOrder=30;
    }

    private static FscMain instance = new FscMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }


    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof FscMain){
            return true;
        }else {
            return false;
        }
    }

}
