package com.coldlar.coin.solana;

import android.text.TextUtils;
import android.util.Log;

import com.coldlar.v8.ScriptLoader;
import com.eclipsesource.v8.V8;
import com.eclipsesource.v8.V8Object;
import com.example.wallet_core.WalletCorePlugin;

@SuppressWarnings("deprecation")
public class SolanaHandler {

    public static boolean verifyAddress(String address) {

        V8Object signProvider = null;
        try {
            V8 runtime = ScriptLoader.sInstance.loadByCoinCode("SOL");

            signProvider = new V8Object(runtime);

            Object state = runtime.executeScript("SolanaLib.IsOnCurve(\"" + address + "\")");

            if (TextUtils.isEmpty(state.toString())) {
                return false;
            }
            // 返回值
            // "onCurve" 在曲线上（是可以被转账的地址），
            // "offCurve" 不在曲线上（可能是派生账户地址，不能直接转账），
            // "invalid" 非法地址

            return state.toString().equalsIgnoreCase("onCurve");
        } catch (Exception e) {
            return false;
        } finally {
            if (signProvider != null && !signProvider.isReleased()) {
                signProvider.release();
            }
        }
    }

    public static String buildTransactionData(String rawData) {
        V8Object signProvider = null;
        try {
            V8 runtime = ScriptLoader.sInstance.loadByCoinCode("SOL");

            signProvider = new V8Object(runtime);
            String escapedRawData = rawData.replace("\"", "\\\"");
            Object object = runtime.executeScript("SolanaLib.BuildTransactionData(\"" + escapedRawData + "\")");
            if (TextUtils.isEmpty(object.toString())) {
                return null;
            }
            return object.toString();
        } catch (Exception e) {
            Log.d(WalletCorePlugin.TAG, e.getMessage());
            return null;
        } finally {
            if (signProvider != null && !signProvider.isReleased()) {
                signProvider.release();
            }
        }
    }

    public static String splitTransactionSignature(String waitSignature, String signature) {
        V8Object signProvider = null;
        try {
            V8 runtime = ScriptLoader.sInstance.loadByCoinCode("SOL");
            signProvider = new V8Object(runtime);

            String script = String.format("SolanaLib.SplitTransactionSignature(\"%s\",\"%s\")", waitSignature, signature);

            Object object = runtime.executeScript(script);
            if (TextUtils.isEmpty(object.toString())) {
                return null;
            }
            return object.toString();
        } catch (Exception e) {
            Log.d(WalletCorePlugin.TAG, e.getMessage());
            return null;
        } finally {
            if (signProvider != null && !signProvider.isReleased()) {
                signProvider.release();
            }
        }
    }

}
