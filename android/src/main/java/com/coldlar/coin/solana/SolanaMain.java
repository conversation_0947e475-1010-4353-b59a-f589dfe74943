package com.coldlar.coin.tron;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.coin.solana.SolanaHandler;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;

public class SolanaMain extends CoinType {

    public SolanaMain() {
        addressHeader = 0;
        p2shHeader = 0;
        dumpedPrivateKeyHeader = 0;
        coinID = ColdlarCoinID.ID_SOLANA;
        symbol = "SOL";
        chain = "sol";
        showSymbol = "SOL";
        name = "Solana";
        cnName = "Solana";
        trName = "Solana";
        uriScheme = "Solana";
        bip44Index = 501;
        unitExponent = 9;
        feeValue = new BigDecimal("100000");
        minNonDust = new BigDecimal("5460");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        isSupportMsgSign = true;
        isSupportMulSign = true;
        confirm = 12;
        coinOrder = 15;
        minFeeSat = new BigDecimal("1");
        maxFeeSat = new BigDecimal("10000000");
        maxSend = new BigDecimal("2000000000");
    }

    private static SolanaMain instance = new SolanaMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public String buildTransactionData(String jsonData) {
        String rawData = "";
        try {
            rawData = SolanaHandler.buildTransactionData(jsonData);

        } catch (Exception e) {

        }
        return rawData;
    }

    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        String rawData = "";
        try {
            rawData = SolanaHandler.splitTransactionSignature(waitSignature, signature);

        } catch (Exception e) {

        }
        return rawData;
    }


    @Override
    public boolean validateAddress(String address) {
        boolean isAddress = false;
        try {
            isAddress = SolanaHandler.verifyAddress(address);

        } catch (Exception e) {

        }
        return isAddress;

    }

    @Override
    public AbstractAddress newAddress(String addressStr) {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if (coinType instanceof SolanaMain) {
            return true;
        } else {
            return false;
        }
    }

}
