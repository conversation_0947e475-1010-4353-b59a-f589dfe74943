package com.coldlar.coin.neo;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import java.math.BigDecimal;
import java.util.Map;


public class NeoMain extends CoinType {
    protected NeoMain() {
        id = "neocoin.main";

        addressHeader = 23;//单地址头
        p2shHeader = 5;//多签名地址头
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;//私钥头

        coinID = ColdlarCoinID.ID_NEO;//自定义的ID
        name = "NEO";//
        cnName = "小蚁股";
        chain ="neo";
        symbol = "NEO";//单位
        showSymbol = "NEO";
        uriScheme = "neocoin";//
        bip44Index = 888;//分层确定性
        unitExponent = 0; //小数位为8为小数
        feeValue = new BigDecimal("0.0002");
        minFeeValue =new BigDecimal("0");
        minNonDust = new BigDecimal("0");
        softDustLimit =new BigDecimal("0");
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");//签名头信息
        confirm=12;
        coinOrder=13;
    }

    private static NeoMain instance = new NeoMain();

    public static synchronized CoinType get() {
        return instance;
    }

    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }


    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }


    @Override
    public boolean validateAddress(String address) {
        return CoreCoinBase.getInstance().validateBitcoinFamilyAddress(address,this);
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }
    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof NeoMain){
            return true;
        }else {
            return false;
        }
    }

}
