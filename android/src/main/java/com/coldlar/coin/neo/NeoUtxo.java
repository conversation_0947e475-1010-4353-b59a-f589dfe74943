package com.coldlar.coin.neo;

import java.io.Serializable;

public class NeoUtxo implements Serializable {
    private String txid;
    private String value;
    private int index;
    private String asset;
    private long createdAtBlock;

    public String getPreTxid() {
        return txid;
    }

    public void setPreTxid(String txid) {
        this.txid = txid;
    }

    public String getAmount() {
        return value;
    }

    public void setAmount(String value) {
        this.value = value;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getAsset() {
        return asset;
    }

    public void setAsset(String asset) {
        this.asset = asset;
    }

    public long getCreatedAtBlock() {
        return createdAtBlock;
    }

    public void setCreatedAtBlock(long createdAtBlock) {
        this.createdAtBlock = createdAtBlock;
    }
}
