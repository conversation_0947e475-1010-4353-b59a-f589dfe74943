package com.coldlar.coin.neo;

import com.coldlar.coin.CoinType;
import com.coldlar.core.wallet.AbstractAddress;

import java.nio.ByteBuffer;

public class NeoAddress implements AbstractAddress
{
    private final String address;
    private final CoinType type;

    public NeoAddress(CoinType paramCoinType, String paramString)
    {
        this.type = paramCoinType;
        this.address = paramString;
    }

    public byte[] getHash160()
    {
        return MyNeoUtils.getAddressHash160(address);
    }

    public long getId()
    {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    public CoinType getType()
    {
        return this.type;
    }

    @Override
    public String toString()
    {
        return this.address;
    }
}
