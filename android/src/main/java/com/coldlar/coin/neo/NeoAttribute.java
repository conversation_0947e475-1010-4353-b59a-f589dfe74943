package com.coldlar.coin.neo;

import com.google.common.base.Charsets;

import org.bitcoinj.core.VarInt;
import org.ripple.bouncycastle.util.encoders.Hex;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class NeoAttribute {

    public static void hexDescriptionAttribute(ByteArrayOutputStream stream, String descriptionHex) throws IOException {
        stream.write(0x90);
        byte[] descriptionBytes = Hex.decode(descriptionHex);
        long lengthBytes = descriptionBytes.length;
        stream.write(new VarInt(lengthBytes).encode());
        stream.write(descriptionBytes);
    }

    public static void remarkAttribute(ByteArrayOutputStream stream, String remark) throws IOException {
        stream.write(0xf0);
        byte[] remarkBytes = remark.getBytes(Charsets.UTF_8);
        long lengthBytes = remarkBytes.length;
        stream.write(new VarInt(lengthBytes).encode());
        stream.write(remarkBytes);
    }

    public static void scriptAttribute(ByteArrayOutputStream stream, String fromAddress) throws IOException {
        stream.write(0x20);
        byte[] attribute = MyNeoUtils.getAddressHash160(fromAddress);
        stream.write(attribute);
    }
}
