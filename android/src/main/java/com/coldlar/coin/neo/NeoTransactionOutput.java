package com.coldlar.coin.neo;


import com.coldlar.core.wallet.AbstractAddress;

import java.io.Serializable;
import java.math.BigDecimal;

public class NeoTransactionOutput implements Serializable {
    private String assetId;
    private BigDecimal amount;
    private byte[] toAddress;
    private String address;

    public NeoTransactionOutput(NeoAsset asset, BigDecimal amount, AbstractAddress address) {
        this.assetId = asset.assetID();
        this.amount = amount;
        this.address = address.toString();
        this.toAddress = MyNeoUtils.getAddressHash160(address.toString());
    }

    public String getAssetId() {
        return assetId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public byte[] getToAddress() {
        return toAddress;
    }

    public String getReceiveAddress(){
        return address;
    }
}
