package com.coldlar.coin.iost;

/**
 * <AUTHOR>
 * @date 2019/8/16 11:07
 * @description
 */

/**
 * "actions":Array[1],
 * "amount_limit":Array[1],
 * "delay":0,
 * "expiration":1655875665539000000,
 * "gas_limit":1000000,
 * "gas_ratio":1,
 * "hash":"",
 * "publisher":"zkhtest4",
 * "publisher_sigs":Array[2],
 * "referred_tx":"",
 * "reserved":Array[0],
 * "signatures":Array[0],
 * "signers":Array[0],
 * "time":1565875665539000000
 */
public class Txmodel {


    private int chain_id;
    public long gas_ratio;
    public long gas_limit;
    public long time = 0L;
    public long expiration = 0L;
    public long delay = 0L;


}
