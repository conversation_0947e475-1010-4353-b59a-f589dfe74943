package com.coldlar.coin.ripple;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.other.SoftDustPolicy;
import com.coldlar.core.base.ColdlarCoinID;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;
import com.ripple.core.coretypes.AccountID;

import java.math.BigDecimal;
import java.util.Map;


public class RippleMain extends CoinType {
    private RippleMain() {
        id = "ripple.main";

        addressHeader = 122;
        acceptableAddressCodes = new int[]{addressHeader, p2shHeader};
        spendableCoinbaseDepth = 100;
        dumpedPrivateKeyHeader = 128;

        coinID=ColdlarCoinID.ID_RIPPLE;
        name = "Ripple";
        cnName = "瑞波币";
        trName="瑞波幣";
        symbol = "XRP";
        chain = "xrp";
        showSymbol = "XRP";
        uriScheme = "ripple";
        bip44Index = 144;
        unitExponent = 6;
        feeValue = new BigDecimal("10000");
        minNonDust =new BigDecimal("10000"); 
        minGlobalNetwokFee =new BigDecimal("1000"); 
        softDustLimit = new BigDecimal("1000"); // 0.001 BTC
        softDustPolicy = SoftDustPolicy.AT_LEAST_BASE_FEE_IF_SOFT_DUST_TXO_PRESENT;
        signedMessageHeader = toBytes("Bitcoin Signed Message:\n");
        confirm=3;
        coinOrder=11;
        maxSend=new BigDecimal("************");
    }

    private static RippleMain instance = new RippleMain();
    public static synchronized CoinType get() {
        return instance;
    }


    @Override
    public String createAddress(Map<String, Object> map) {
        return "";
    }

    @Override
    public String getPublicKey(Map<String, Object> map) {
        return "";
    }

    @Override
    public boolean validateAddress(String address) {
        try {
            AccountID.fromAddress(address);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    @Override
    public AbstractAddress newAddress(String addressStr)   {
        return BitAddress.fromAddress(this, addressStr);
    }

    @Override
    public String buildTransactionData(String jsonData) {
        return "";
    }


    @Override
    public String splitTransactionSignature(String waitSignature, String signature) {
        return "";
    }

    @Override
    public boolean coinTypeEquals(CoinType coinType) {
        if(coinType instanceof RippleMain){
            return true;
        }else {
            return false;
        }
    }

}
