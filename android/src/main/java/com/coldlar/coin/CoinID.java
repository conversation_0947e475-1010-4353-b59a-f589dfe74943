package com.coldlar.coin;

import com.coldlar.coin.act.AchainMain;
import com.coldlar.coin.bch.BitcoinCashMain;
import com.coldlar.coin.bitcoin.BitcoinDiamondMain;
import com.coldlar.coin.bitcoin.BitcoinGodMain;
import com.coldlar.coin.bitcoin.BitcoinGoldMain;
import com.coldlar.coin.bitcoin.BitcoinMain;
import com.coldlar.coin.bitcoin.BitcoinPayMain;
import com.coldlar.coin.bitcoin.BitcoinSvMain;
import com.coldlar.coin.bitcoin.BitcoinXMain;
import com.coldlar.coin.bitcoin.DashMain;
import com.coldlar.coin.bitcoin.DogecoinMain;
import com.coldlar.coin.bitcoin.IpchainMain;
import com.coldlar.coin.bitcoin.LitecoinMain;
import com.coldlar.coin.bitcoin.QtumMain;
import com.coldlar.coin.bitcoin.SuperBitcoinMain;
import com.coldlar.coin.bitcoin.ZcashMain;
import com.coldlar.coin.eos.EosMain;
import com.coldlar.coin.ethereum.EthClassicMain;
import com.coldlar.coin.ethereum.EthereumMain;
import com.coldlar.coin.neo.NeoMain;
import com.coldlar.coin.other.KcashMain;
import com.coldlar.coin.other.PocMain;
import com.coldlar.coin.other.WiccMain;
import com.coldlar.coin.ripple.RippleMain;
import com.coldlar.coin.nem.NemMain;
import com.coldlar.core.exceptions.AddressMalformedException;
import com.coldlar.core.util.GenericUtils;
import com.google.common.collect.ImmutableList;

import org.bitcoinj.params.Networks;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * When adding new coin types the order affects which types will be chosen by default if they share
 * a URI scheme. For example BITCOIN_MAIN and BITCOIN_TEST share the bitcoin: scheme so BITCOIN_MAIN
 * will be chosen by default when we don't have any other information. The same applies to the other
 * testnets and NUBITS_MAIN and NUSHARES_MAIN that share the nu: URI scheme. For anything else the
 * order doesn't matter.
 */
public enum CoinID {
    BITCOINGOLD_MAIN(BitcoinGoldMain.get()),
    NEM_MAIN(NemMain.get()),
    RIPPLE_MAIN(RippleMain.get()),
    ZCASH_MAIN(ZcashMain.get()),
    ETHEREUM_MAIN(EthereumMain.get()),
    BITCOINCASH_MAIN(BitcoinCashMain.get()),
    ETHCLASSIC_MAIN(EthClassicMain.get()),
    BITCOIN_MAIN(BitcoinMain.get()),
    LITECOIN_MAIN(LitecoinMain.get()),
    DOGECOIN_MAIN(DogecoinMain.get()),
    DASH_MAIN(DashMain.get()),
    BCD_MAIN(BitcoinDiamondMain.get()),
    BCX_MAIN(BitcoinXMain.get()),
    SBTC_MAIN(SuperBitcoinMain.get()),
    QTUM_MAIN(QtumMain.get()),
    ACHAIN_MAIN(AchainMain.get()),
    KCASH_MAIN(KcashMain.get()),
    EOS_MAIN(EosMain.get()),
    IPCHAIN_MAIN(IpchainMain.get()),
    NE0_MAIN(NeoMain.get()),
    WICC_MAIN(WiccMain.get()),
    BSV_MAIN(BitcoinSvMain.get()),
    BTP_MAIN(BitcoinPayMain.get()),
    POC_MAIN(PocMain.get()),
    BITCOINGOD_MAIN(BitcoinGodMain.get()),;

    private static List<CoinType> types;
    private static HashMap<String, CoinType> idLookup = new HashMap<>();
    private static HashMap<String, CoinType> symbolLookup = new HashMap<>();
    private static HashMap<String, ArrayList<CoinType>> uriLookup = new HashMap<>();

    static {
//        Set<NetworkParameters> bitcoinjNetworks = Networks.get();
//        for (NetworkParameters network : bitcoinjNetworks) {
//            Networks.unregister(network);
//        }//coin-review-todotag

        ImmutableList.Builder<CoinType> coinTypeBuilder = ImmutableList.builder();
        for (CoinID id : values()) {
            Networks.register(id.type);

            if (symbolLookup.containsKey(id.type.symbol)) {
                throw new IllegalStateException(
                        "Coin currency codes must be unique, double found: " + id.type.symbol);
            }
            symbolLookup.put(id.type.symbol, id.type);

            if (idLookup.containsKey(id.type.getId())) {
                throw new IllegalStateException(
                        "Coin IDs must be unique, double found: " + id.type.getId());
            }
            // Coin ids must end with main or test
            if (!id.type.getId().endsWith("main") && !id.type.getId().endsWith("test")) {
                throw new IllegalStateException(
                        "Coin IDs must end with 'main' or 'test': " + id.type.getId());
            }
            idLookup.put(id.type.getId(), id.type);

            if (!uriLookup.containsKey(id.type.uriScheme)) {
                uriLookup.put(id.type.uriScheme, new ArrayList<CoinType>());
            }
            uriLookup.get(id.type.uriScheme).add(id.type);

            coinTypeBuilder.add(id.type);
        }
        types = coinTypeBuilder.build();
    }

    private final CoinType type;

    CoinID(final CoinType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return type.getId();
    }

    public CoinType getCoinType() {
        return type;
    }

    public static List<CoinType> getSupportedCoins() {
        return types;
    }

    public static CoinType typeFromId(String stringId) {
        if (idLookup.containsKey(stringId)) {
            return idLookup.get(stringId);
        } else {
            throw new IllegalArgumentException("Unsupported ID: " + stringId);
        }
    }

    public static List<CoinType> fromUri(String input) {
        String inputLowercase = input.toLowerCase();
        for (String uri : uriLookup.keySet()) {
            if (inputLowercase.startsWith(uri + "://") || inputLowercase.startsWith(uri + ":")) {
                return uriLookup.get(uri);
            }
        }
        throw new IllegalArgumentException("Unsupported URI: " + input);
    }

    public static List<CoinType> fromUriScheme(String scheme) {
        String schemeLowercase = scheme.toLowerCase();
        if (uriLookup.containsKey(schemeLowercase)) {
            return uriLookup.get(schemeLowercase);
        } else {
            throw new IllegalArgumentException("Unsupported URI scheme: " + scheme);
        }
    }

    public static List<CoinType> typesFromAddress(String address) throws AddressMalformedException {
        return GenericUtils.getPossibleTypes(address);
    }

    public static boolean isSymbolSupported(String symbol) {
        return symbolLookup.containsKey(symbol);
    }

    public static CoinType typeFromSymbol(String symbol) {
        if (symbolLookup.containsKey(symbol.toUpperCase())) {
            return symbolLookup.get(symbol.toUpperCase());
        } else {
            throw new IllegalArgumentException("Unsupported coin symbol: " + symbol);
        }
    }
}
