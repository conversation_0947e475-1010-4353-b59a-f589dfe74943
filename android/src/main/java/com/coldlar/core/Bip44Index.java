package com.coldlar.core;

public class Bip44Index {
    public static final int BTC = 0;
    public static final int LTC = 2;
    public static final int DOGE = 3;
    public static final int RDD = 4;
    public static final int DASH = 5;
    public static final int GRS = 17;
    public static final int DGB = 20;
    public static final int DCR = 42;
    public static final int XEM = 43;
    public static final int ETH = 60;
    public static final int ETC = 61;
    public static final int ICX = 74;
    public static final int XVG = 77;
    public static final int STRAT = 105;
    public static final int ARK = 111;
    public static final int ATOM = 118;
    public static final int PIVX = 119;
    public static final int XMR = 128;
    public static final int FCT = 131;
    public static final int ZEC = 133;
    public static final int LSK = 134;
    public static final int STEEM = 135;
    public static final int XZC = 136;
    public static final int KMD = 141;
    public static final int XRP = 144;
    public static final int BCH = 145;
    public static final int XLM = 148;
    public static final int BTM = 153;
    public static final int BTG = 156;
    public static final int RVN = 175;
    public static final int EOS = 194;
    public static final int TRX = 195;
    public static final int BCN = 204;
    public static final int BSV = 236;
    public static final int NANO = 256;
    public static final int ALGO = 283;
    public static final int IOST = 291;
    public static final int ZIL = 313;
    public static final int MOAC = 314;
    public static final int AION = 425;
    public static final int AE = 457;
    public static final int FIL = 461;
    public static final int THETA = 500;
    public static final int OPT = 614;
    public static final int ACT = 666;
    public static final int BNB = 714;
    public static final int VET = 818;
    public static final int NEO = 888;
    public static final int TOMO = 889;
    public static final int AVAX = 9000;
    public static final int ARB = 9001;
    public static final int MATIC = 966;
    public static final int LBTC = 998;
    public static final int OKT = 996;
    public static final int BCD = 999;
    public static final int HT = 1010;
    public static final int ONT = 1024;
    public static final int BCX = 1688;
    public static final int XTZ = 1729;
    public static final int ADA = 1815;
    public static final int QTUM = 2301;
    public static final int GXC = 2303;
    public static final int ELA = 2305;
    public static final int NAS = 2718;
    public static final int IOTA = 4218;
    public static final int SBTC = 8888;
    public static final int NULS = 8964;
    public static final int BSC = 9006;
    public static final int GOD = 9999;
    public static final int IPC = 272;
    public static final int WICC = 99999;
    public static final int WAN = 5718350;
    public static final int WAVES = 5741564;
    public static final int BABYLONTESTNET = 10119;
    public static final int BABYLON = 10118;
}
