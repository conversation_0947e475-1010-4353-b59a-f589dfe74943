package com.coldlar.core;


import com.coldlar.coin.ethereum.transaction.crypto.Keccak256;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020/7/18 16:03
 * @description
 */
public  class CommonUtil {


    public static byte[] sha3omit12(byte[] paramArrayOfByte) {
        byte[] arrayOfByte = sha3(paramArrayOfByte);
        return Arrays.copyOfRange(arrayOfByte, 12, arrayOfByte.length);
    }

    public static byte[] sha3(byte[] paramArrayOfByte) {
        Keccak256 localKeccak256 = new Keccak256();
        localKeccak256.update(paramArrayOfByte);
        return localKeccak256.digest();
    }

}
