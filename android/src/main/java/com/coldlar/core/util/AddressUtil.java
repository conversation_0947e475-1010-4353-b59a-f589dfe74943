package com.coldlar.core.util;

import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.Base58;

/**
 * <AUTHOR>
 * @date 2020/7/20 19:00
 * @description
 */
public class AddressUtil {


    /**
     * 解析地址头版本号
     * @param n
     * @param s
     * @return
     */
    public static int getExpectedVersion(int n, String s) {
        byte[] decodeChecked = new byte[0];

        try {
            decodeChecked = Base58.decodeChecked(s);
            n = decodeChecked.length - n;
            if (n < 0 || n > 3) {
                throw new AddressFormatException("No acceptable version length found");
            }
        } catch (AddressFormatException var4) {
            var4.printStackTrace();
        }

        return getVersionFromData(n, decodeChecked);
    }


    private static int getVersionFromData(int n, byte[] array) {
        int n2 = 0;

        for(int i = 0; i < n; ++i) {
            n2 = n2 << 8 | array[i] & 255;
        }

        return n2;
    }

}
