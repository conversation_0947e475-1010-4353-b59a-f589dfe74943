package com.coldlar.core.util;


import static org.web3j.crypto.Keys.ADDRESS_LENGTH_IN_HEX;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.act.ACTAddress;
import com.coldlar.coin.act.AchainMain;
import com.coldlar.coin.atom.CosmosMain;
import com.coldlar.coin.bch.BitcoinCashAddressFormatter;
import com.coldlar.coin.bch.MoneyNetwork;
import com.coldlar.coin.bitcoin.BitcoinMain;
import com.coldlar.coin.bitcoin.UsdtMain;
import com.coldlar.coin.bnb.BinanceMain;
import com.coldlar.coin.bnb.ColdLarLibBinanceCoin;
import com.coldlar.coin.eos.EosMain;
import com.coldlar.coin.eos.IostMain;
import com.coldlar.coin.ethereum.EthClassicMain;
import com.coldlar.coin.ethereum.EthereumMain;
import com.coldlar.coin.ethereum.transaction.CryptoException;
import com.coldlar.coin.ethereum.transaction.HashUtil;
import com.coldlar.coin.filecoin.ColdLarLibFilecoin;
import com.coldlar.coin.filecoin.FilecoinMain;
import com.coldlar.coin.neo.NeoMain;
import com.coldlar.coin.other.GasMain;
import com.coldlar.coin.other.HTDFMain;
import com.coldlar.coin.other.KcashMain;
import com.coldlar.coin.other.PocMain;
import com.coldlar.coin.other.TomoMain;
import com.coldlar.coin.other.WwbMain;
import com.coldlar.coin.ripple.RippleMain;
import com.coldlar.coin.tron.TronMain;
import com.coldlar.coin.tron.utils.TrxHexUtils;
import com.coldlar.coin.nem.NemMain;
import com.coldlar.coin.xlm.XlmMain;
import com.coldlar.core.exceptions.AddressMalformedException;
import com.coldlar.encrypt.Base32Encoder;
import com.ripple.core.coretypes.AccountID;

import org.apache.commons.lang3.StringUtils;
import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.Bech32;
import org.bitcoinj.core.LegacyAddress;
import org.bitcoinj.core.SegwitAddress;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.HDPath;
import org.cosmosj.CosmosUtil;
import org.json.JSONException;
import org.json.JSONObject;
import org.stellar.sdk.KeyPair;
import org.web3j.utils.Numeric;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Nonnull;


public class CoinUtil {

    /**
     * 币种最小单位转最大单位
     * ********** Satoshi =1 BTC
     * 去掉后面无用的零，如小数点后面全是零则去掉小数点
     *
     * @param str     金额
     * @param decimal 小数位
     * @return
     */
    public static BigDecimal getCoinValueSatoshiToBtc(String str, int decimal) {
        if (str != null && !str.isEmpty() && str.length() != 0) {
            return getCoinValueSatoshiToBtc(new BigDecimal(str), decimal);
        } else {
            return getCoinValueSatoshiToBtc(new BigDecimal("0"), decimal);
        }
    }




    public static BigDecimal getCoinValueSatoshiToBtc(long value, int decimal) {
        return getCoinValueSatoshiToBtc(new BigDecimal(value + ""), decimal);
    }

    public static BigDecimal getCoinValueSatoshiToBtc(BigDecimal value, int decimal) {
        try {
            String decimalStr = "1";
            for (int i = 0; i < decimal; i++) {
                decimalStr = decimalStr + "0";
            }
            BigDecimal balanceWei = new BigDecimal(decimalStr);
            String result = value.divide(balanceWei, decimal, RoundingMode.DOWN).toString();
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
            return new BigDecimal(result);
        } catch (Exception e) {
            return new BigDecimal("0");
        }


    }


    /**
     * 币种最大单位转最小单位
     * 0.1 BTC=10000000 Satoshi
     *
     * @param str     金额
     * @param decimal 小数位
     * @return
     */
    public static BigDecimal getCoinValueBtcToSatoshi(String str, int decimal) {
        String decimalStr = "1";
        for (int i = 0; i < decimal; i++) {
            decimalStr = decimalStr + "0";
        }
        BigDecimal balance = new BigDecimal(str);
        if ("0".equals(balance.toString())) {
            return new BigDecimal("0");
        }
        BigDecimal balanceWei = new BigDecimal(decimalStr);
        String result = balance.multiply(balanceWei).toString();
        if (result.indexOf(".") > 0) {
            result = result.replaceAll("0+?$", "");
            result = result.replaceAll("[.]$", "");
        }
        return new BigDecimal(result);
    }



    /**
     * 获取EOS金额
     *
     * @param str
     * @return
     */
    public static String getEosValue(CoinType coinType, long str) {
        String result = getCoinValueSatoshiToBtc(str, coinType.getUnitExponent()) + "";
        return NumberUtil.setFormate(result);
    }




    /**
     * 根据cointype判断是否是以太系列
     *
     * @param cointype
     * @return
     */
    public static boolean isEther(CoinType cointype) {
        if (EthClassicMain.get().coinTypeEquals(cointype)
                || EthereumMain.get().coinTypeEquals(cointype) || cointype.isEVM || cointype.isLayer2
        ) {
            return true;
        }
        return false;
    }

    /**
     * 根据cointype判断是否是POC
     *
     * @param cointype
     * @return
     */
    public static boolean isPoc(CoinType cointype) {
        if (PocMain.get().coinTypeEquals(cointype)) {
            return true;
        }
        return false;
    }

    /**
     * 根据cointype判断是否是瑞波
     *
     * @param coinType
     * @return
     */
    public static boolean isRipple(CoinType coinType) {
        if (RippleMain.get().coinTypeEquals(coinType)) {
            return true;
        }
        return false;
    }

    /**
     * 校验是否为XRP地址
     *
     * @param address
     * @return
     */
    public static boolean isRippleAddress(String address) {
        try {
            AccountID.fromAddress(address);
            return true;
        } catch (Exception e) {
            return false;
        }
    }





    /**
     * 判断是否为act
     *
     * @param coinType
     * @return
     */
    public static boolean isAct(CoinType coinType) {
        if (AchainMain.get().coinTypeEquals(coinType) || KcashMain.get().coinTypeEquals(coinType)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为NEO
     *
     * @param coinType
     * @return
     */
    public static boolean isNeo(CoinType coinType) {
        if (NeoMain.get().coinTypeEquals(coinType) || GasMain.get().coinTypeEquals(coinType) || WwbMain.get().coinTypeEquals(coinType)) {
            return true;
        }
        return false;
    }



    private static boolean  validateBitcoinFamilyAddress( String address,CoinType type) {
     boolean isRightAddress = false;

        int version=9999;
        try {
            version = AddressUtil.getExpectedVersion(20, address);
        }catch (Exception e){

        }
        for (int addressCode : type.getAcceptableAddressCodes()) {
            if (addressCode == version) {
                isRightAddress = true;
                break;
            }
        }
        return isRightAddress;
    }

    private static boolean  validateEthereumFamilyAddress( String address) {

        String cleanInput = Numeric.cleanHexPrefix(address);

        try {
            Numeric.toBigIntNoPrefix(cleanInput);
        } catch (NumberFormatException e) {
            return false;
        }

        return cleanInput.length() == ADDRESS_LENGTH_IN_HEX;
    }

    /**
     * 判断是否是NEM地址
     *
     * @param value
     * @return
     */
    public static boolean isNemAddress(String value) {
        if (value == null) {
            return false;
        }
        if (value.contains("-")) {
            value = value.replace("-", "");
        }
        if (40 != value.length()) {
            return false;
        }
        final byte[] encodedBytes;
        try {
            encodedBytes = Base32Encoder.getBytes(value);
        } catch (final IllegalArgumentException e) {
            return false;
        }
        if (25 != encodedBytes.length) {
            return false;
        }
        final int checksumStartIndex = 21;
        final byte[] versionPrefixedHash = Arrays.copyOfRange(encodedBytes, 0, checksumStartIndex);
        final byte[] addressChecksum = Arrays.copyOfRange(encodedBytes, checksumStartIndex, checksumStartIndex + 4);
        final byte[] calculatedChecksum = generateChecksum(versionPrefixedHash);
        return Arrays.equals(addressChecksum, calculatedChecksum);
    }


    private static byte[] generateChecksum(final byte[] input) {
        // step 1: sha3 hash of (input
        byte[] sha3StepThreeHash = new byte[0];
        try {
            sha3StepThreeHash = HashUtil.sha3(input);
        } catch (CryptoException e) {
            e.printStackTrace();
        }
        // step 2: get the first X bytes of (1)
        return Arrays.copyOfRange(sha3StepThreeHash, 0, 4);
    }

    /**
     * 判断是否为ACT地址
     *
     * @param address
     * @return
     */
    public static boolean isActAddress(String address) {
        boolean isAct = false;
        try {
            if (!StringUtils.isAnyEmpty(address) && address.length() > 3) {
                address.replace("0x", "");
                if (ACTAddress.check(address.substring(3), ACTAddress.Type.ADDRESS)) {
                    isAct = true;
                }
            }
        } catch (Exception e) {
        }
        return isAct;
    }

    /**
     * 尝试从String中解析出收款金额  后续优化，返回地址+类型+金额 2018年12月5日19:35:55
     *
     * @param addressStr
     * @return
     */
    public static double getReciveValue(String addressStr) {
        double value = 0;
        String valueStr = "0";
        if (!StringUtil.isEmpty(addressStr) && addressStr.contains("amount")) {
            valueStr = addressStr.substring(addressStr.lastIndexOf("=") + 1, addressStr.length());
        }
        try {
            value = Double.parseDouble(valueStr);
        } catch (Exception e) {

        }

        return value;
    }




    /**
     * P4BCH地址验证
     *
     * @param address
     * @return
     */
    public static boolean validateBchAddress(String address) {
        return isBchAddress(address);
    }


    /**
     * 验证地址是否正确
     *
     * @param address
     * @param coinType
     * @return
     */
    public static boolean validateAddress(String address, CoinType coinType) {
        boolean isRightAddress = false;

        if (coinType instanceof  EosMain) {
            String strPattern = "[1-5a-z]{12}$";
            Pattern p = Pattern.compile(strPattern);
            Matcher m = p.matcher(address);
            return m.matches();
        }
        if (coinType.isEthereumFamily || coinType instanceof TomoMain) {
            return  validateEthereumFamilyAddress(address);
        }

        if (coinType instanceof HTDFMain) {
            try {
                Bech32.Bech32Data decode = Bech32.decode(address);
                if (decode.hrp.equals(coinType.getUriScheme()) && decode.data.length == 32) {
                    return true;
                } else {
                    return false;
                }
            } catch (AddressFormatException e) {
                e.printStackTrace();
                return false;
            }
        }

        if (coinType instanceof TronMain) {
            try {
                return TrxHexUtils.addressValid(address);
            } catch (Exception e) {
                return false;
            }
        }


        if (coinType instanceof XlmMain) {
            return CoinUtil.isXlmAddress(address);
        }

        if (coinType instanceof RippleMain) {
            return CoinUtil.isRippleAddress(address);
        }
        if (coinType instanceof NemMain) {
            return CoinUtil.isNemAddress(address);
        }
        if (coinType instanceof IostMain) {
            return CoinUtil.isIostAccount(address);
        }
        if (coinType instanceof BinanceMain) {
            return CoinUtil.isBnbAddress(address);
        }
        if (coinType instanceof CosmosMain) {
            return CosmosUtil.validateAddress(address);
        }
        if (coinType instanceof FilecoinMain) {
            return CoinUtil.isFilecoinAddress(address);
        }

        if (coinType instanceof UsdtMain) {
            coinType = BitcoinMain.get();
        }


        try {
            List<CoinType> possibleTypes = GenericUtils.getPossibleTypes(address);
            if (possibleTypes.size() == 0) {
                //提示地址不正确
                isRightAddress = false;
            } else {
                for (CoinType type : possibleTypes) {
                    if (type.getSymbol().equals(coinType.getSymbol())) {
                        isRightAddress = true;
                        break;
                    }
                }
            }
        } catch (AddressMalformedException e) {
            e.printStackTrace();
            if (coinType != null) {
                if (CoinUtil.isRippleAddress(address) && coinType.coinTypeEquals(RippleMain.get())) {
                    isRightAddress = true;
                } else if (CoinUtil.isNemAddress(address) && coinType.coinTypeEquals(NemMain.get())) {
                    isRightAddress = true;
                }
            }
        }
        return isRightAddress;
    }




    /**
     * 解析路径
     *
     * @param path
     * @return
     */
    public static HDPath parsePath(@Nonnull String path) {
        String[] parsedNodes = path.replace("m", "").split("/");
        List<ChildNumber> nodes = new ArrayList<>();

        for (String n : parsedNodes) {
            n = n.replaceAll(" ", "");
            if (n.length() == 0) {
                continue;
            }
            boolean isHard = n.endsWith("'");
            if (isHard) {
                n = n.substring(0, n.length() - 1);
            }
            int nodeNumber = Integer.parseInt(n);
            nodes.add(new ChildNumber(nodeNumber, isHard));
        }

        return new HDPath(nodes);
    }



    /**
     * byte数组转16进制字符串
     *
     * @param byteArray
     * @return
     */
    public static String byteArrayToHexStr(byte[] byteArray) {
        if (byteArray == null) {
            return null;
        }
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[byteArray.length * 2];
        for (int j = 0; j < byteArray.length; j++) {
            int v = byteArray[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }


    /**
     * 校验IOST账户
     *
     * @param account
     * @return
     */
    public static boolean isIostAccount(String account) {
        return Pattern.matches("[a-z0-9_]{5,11}", account);
    }


    /**
     * 是否为华特东方币
     */
    public static boolean isHtdf(CoinType cointype) {
        if (cointype instanceof HTDFMain) {
            return true;
        }
        return false;
    }

    /**
     * 是否为恒星币币种
     *
     * @param address
     * @return
     */
    public static boolean isXlmAddress(String address) {
        try {
            KeyPair keyPair = KeyPair.fromAccountId(address);
            if (null != keyPair.getAccountId()) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }

        return false;
    }

    public static String getzerovalue(int decimal) {
        String valueStr = "";
        for (int i = 0; i < decimal; i++) {
            valueStr = valueStr + "0";
        }
        return valueStr;
    }







    /**
     * 判断是否为POC地址
     *
     * @param address
     * @return
     */
    public static boolean isPocAddress(String address) {
        boolean isPoc = false;
        try {
            if (!StringUtils.isAnyEmpty(address) &&
                    address.startsWith(PocMain.get().getSymbol().toLowerCase())
                    && address.length() == 42) {
//                byte[] addressByte = Base32Encoder.getBytes(address);
                isPoc = true;
            }
        } catch (Exception e) {
        }
        return isPoc;
    }

    /**
     * 判断是否为BNB地址
     *
     * @param address
     * @return
     */
    public static boolean isBnbAddress(String address) {
        boolean isBnbAddress = false;
        try {
            String result = ColdLarLibBinanceCoin.getCheckAddress(address);
            JSONObject json = new JSONObject(result);
            isBnbAddress = json.optBoolean("status", false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return isBnbAddress;
    }

    /**
     * 判断是否为BNB地址
     *
     * @param address
     * @return
     */
    public static boolean isFilecoinAddress(String address) {
        boolean isBnbAddress = false;
        try {
            String result = ColdLarLibFilecoin.checkAddress(address);
            JSONObject json = new JSONObject(result);
            isBnbAddress = json.optBoolean("status", false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return isBnbAddress;
    }


    /**
     * @param address
     * @return
     */
    public static boolean isBchAddress(String address) {
        return BitcoinCashAddressFormatter.isValidCashAddress(address, MoneyNetwork.MAIN);
    }


    /**
     * @param coinname
     * @param address
     * @param amount
     * @return bitcoin:**********************************?amount=28
     */
    public static String getCoinURI(String coinname, String address, String amount) {
        String uri = coinname.toLowerCase() + ":" + address + "?amount=" + amount;
        return uri.replace(" ", "");
    }




    /**
     * 判断币种是否需要显示备注
     *
     * @param coinType
     * @return
     */
    public static boolean isShowMemo(CoinType coinType) {
        if (coinType instanceof EosMain ||
                coinType instanceof RippleMain ||
                coinType instanceof XlmMain ||
                coinType instanceof EosMain ||
                coinType instanceof NemMain ||
                coinType instanceof BinanceMain ||
                coinType instanceof IostMain) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * @param coinType
     * @return 判断币种是否有备注
     */
    public static boolean isCoinHaveMemo(CoinType coinType) {
        if (coinType instanceof EosMain ||
                coinType instanceof RippleMain ||
                coinType instanceof IostMain ||
                coinType instanceof NeoMain ||
                coinType instanceof NemMain ||
                coinType instanceof BinanceMain) {
            return true;
        } else {
            return false;
        }

    }


    /**
     * @param address
     * @return 是否为BTC地址，或者LTC地址
     */
    public static boolean isCorrectAddress(String address, CoinType coinType) {
        int version = -1;
        try {
            version = AddressUtil.getExpectedVersion(20, address);
        } catch (Exception e) {

        } finally {
            if (version == coinType.getAddressHeader()) {
                return true;
            } else {
                {
                    LegacyAddress addressp2sh = null;
                    SegwitAddress segwitAddress = null;
                    try {
                        addressp2sh = BitAddress.fromBase58(coinType, address);
                    } catch (Exception e) {
                    } finally {
                        if (addressp2sh != null) {
                            return true;
                        } else {
                            try {
                                segwitAddress = SegwitAddress.fromBech32(coinType, address);
                            } catch (Exception exception) {
                            } finally {
                                if (segwitAddress != null) {
                                    return true;
                                } else {
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
        }
    }



}
