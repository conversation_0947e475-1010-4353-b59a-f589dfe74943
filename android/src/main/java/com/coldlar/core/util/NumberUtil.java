package com.coldlar.core.util;



import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Locale;

/**
 * Created by mark on 17/4/22.
 * 备注
 * 1.数字转换工具类型尽量全部使用BigDecimal或者BigInteger
 * 2.BigDecimal初始化传入String类型参数，否则会丢精度
 */

@SuppressWarnings("unchecked")
public class NumberUtil {


    /**
     * 计算类，固定小数位，区分四舍五入，返回double**************************************************************
     */


    /**
     * 保留0位小数，不四舍五入
     */
    public static double getPot(double num) {
        DecimalFormat myformat = new DecimalFormat("0");
        return Double.parseDouble(myformat.format(num));
    }

    /**
     * 保留2位小数，不四舍五入
     */
    public static double getPot2f(double num) {
        String str = NumberUtil.formatWithLanguage("%.2f", num);
        return Double.parseDouble(str) == 0 ? 0 : Double.parseDouble(str);
    }

    /**
     * 保留4位小数，不四舍五入
     */
    public static double getPot4f(double num) {
        String str = NumberUtil.formatWithLanguage("%.4f", num);
        return Double.parseDouble(str) == 0 ? 0 : Double.parseDouble(str);
    }

    /**
     * 保留5位小数，不四舍五入
     */
    public static double getPot5f(double num) {
        String str = NumberUtil.formatWithLanguage("%.5f", num);
        return Double.parseDouble(str) == 0 ? 0 : Double.parseDouble(str);
    }


    /**
     * 保留6位小数，不四舍五入
     */
    public static double getPot6f(double num) {
        String str = NumberUtil.formatWithLanguage("%.6f", num);
        return Double.parseDouble(str) == 0 ? 0 : Double.parseDouble(str);
    }


    /**
     * 保留8位小数，不四舍五入
     */
    public static double getPot8f(double num) {
        String str = NumberUtil.formatWithLanguage("%.8f", num);
        return Double.parseDouble(str);
    }

    /**
     * 保留10位小数，不四舍五入
     */
    public static double getPot10f(double num) {
        String str = NumberUtil.formatWithLanguage("%.10f", num);
        return Double.parseDouble(str) == 0 ? 0 : Double.parseDouble(str);
    }




    /**
     * 返回不用科学计数法的3位小数
     *
     * @param num
     * @return
     */
    public static String showText3f(double num) {
        String str = NumberUtil.formatWithLanguage("%.3f", num);
        if (StringUtil.isEmpty(str) || num == 0) {
            return "0";
        } else {
            BigDecimal bd = new BigDecimal(str);
            return bd.stripTrailingZeros().toPlainString();
        }
    }


    /**
     * 返回不用科学计数法的4位小数
     *
     * @param num
     * @return
     */
    public static String showText4f(double num) {
        String str = NumberUtil.formatWithLanguage("%.4f", num);
        if (StringUtil.isEmpty(str) || num == 0) {
            return "0";
        } else {
            BigDecimal bd = new BigDecimal(str);
            return bd.stripTrailingZeros().toPlainString();
        }
    }


    /**
     * 返回不用科学计数法的8位小数
     *
     * @param num
     * @return
     */
    public static String showText8f(double num) {
        String str = NumberUtil.formatWithLanguage("%.8f", num);
        if (StringUtil.isEmpty(str) || num == 0) {
            return "0";
        } else {
            BigDecimal bd = new BigDecimal(str);
            return bd.stripTrailingZeros().toPlainString();
        }
    }

    /**
     * 保留8位小数，不四舍五入
     */
    public static String getPot8(String num) {
        String result = "";
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.8f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }


    /**
     * 展示类，String截取固定长度，返回String**************************************************************
     */


    /**
     * 不用科学计数法
     * 保留2位
     *
     * @param str
     * @return
     */
    public static String setFormate2(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0.00";
        } else {
            BigDecimal bd = new BigDecimal(str);
            return NumberUtil.setFormate(bd.setScale(2, RoundingMode.DOWN).toString());
        }
    }


    /**
     * 保留3位不四舍五入
     * @param str
     * @return
     */
    public static String setFormate3f(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(3, RoundingMode.DOWN).toString());
        }
    }

    /**
     * 保留八位不四舍五入,截取8位后面小数
     *
     * @param str
     * @return
     */
    public static String setFormate8f(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(8, RoundingMode.DOWN).toString());
        }
    }
    /**
     * 保留八位不四舍五入,截取8位后面小数
     *
     * @param str
     * @return
     */
    public static String setFormate6f(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(6, RoundingMode.DOWN).toString());
        }
    }


    /**
     * 保留length位小数，不四舍五入,
     * @param str
     * @return
     */
    public static String setFormateStr(String str,int length) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(length, RoundingMode.DOWN).toString());
        }
    }


    /**
     * 保留十位小数，不四舍五入
     *
     * @param str
     * @return
     */
    public static String setFormate10f(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(10, RoundingMode.DOWN).toString());
        }
    }

    /**
     * 保留十位小数，四舍五入
     *
     * @param str
     * @return
     */
    public static String setFormate9f(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0";
        } else {
            BigDecimal a = new BigDecimal(str);
            return NumberUtil.setFormate(a.setScale(9, RoundingMode.DOWN).toString());
        }
    }


    /**
     * 处理数字为不用科学计数法
     *
     * @param str
     * @return
     */
    public static String setFormate(String str) {
        if (StringUtil.isEmpty(str)) {
            return "0";
        } else {
            str = str.replace(",", ".");
            if (Double.parseDouble(str) == 0) {
                return "0";
            }
            BigDecimal bd = new BigDecimal(str);
            return bd.stripTrailingZeros().toPlainString();
        }
    }

    public static String setFormateV10(String str) {
        if (StringUtil.isEmpty(str)) {
            return "0";
        } else {
            str = str.replace(",", ".");
            if (Double.parseDouble(str) == 0) {
                return "0";
            }
            BigDecimal bd = new BigDecimal(str);
            return bd.setScale(10,RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        }
    }





    /**
     * BigDecimal 计算加减乘除**************************************************************
     */

    /**
     * double相减
     */
    public static Double sub(Double v1, Double v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        double value = b1.subtract(b2).doubleValue();
        return value;
    }

    /**
     * double相加
     */
    public static double add(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.add(b2).doubleValue();
    }

    /**
     * double相乘
     */
    public static double mul(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.multiply(b2).doubleValue();
    }

    /**
     * double相乘，返回long
     */
    public static long mulLong(double v1, long v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.multiply(b2).longValue();
    }

    /**
     * double相除 保留10位，四舍五入,todo可优化
     */
    public static double div(double d1, double d2) {
        return div(d1, d2, 10);
    }

    /**
     * double相除，四舍五入,固定长度
     */
    @Deprecated
    public static double div(double d1, double d2, int len) {
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2, len, RoundingMode.HALF_UP).doubleValue();
    }


    /**
     * double相除不四捨五入
     */
    public static double divRealValue(double d1, double d2, int len) {
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2, len, RoundingMode.HALF_EVEN).doubleValue();
    }


    /**
     * V1-V2>0返回true
     *
     * @return
     */
    public static boolean compareValue(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        int value = b1.compareTo(b2);
        boolean result = value == 1 ? true : false;
        return result;
    }


    /**
     * 保留3位小数
     */
    public static String getPot3(String num) {
        String result = "";
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.3f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留2位小数
     */
    public static String getPot2(String num) {
        String result = "";
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.2f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留4位小数
     */
    public static String getPot4(String num) {
        String result = "";
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.4f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留2位小数，不四舍五入!
     */
    public static String getPot2f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            DecimalFormat formater = new DecimalFormat();
            formater.setMaximumFractionDigits(2);
            formater.setGroupingSize(0);//设置分组大小。分组大小是数的整数部分中分组分隔符之间的数字位数,例如在数 "123,456.78" 中，分组大小是 3

            formater.setRoundingMode(RoundingMode.FLOOR);
            result = formater.format(value);
        } catch (Exception e) {
            result = "0";
        }
        return result;
    }


    /**
     * 保留4位小数，四舍五入
     */
    public static String getPot4f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            String str = NumberUtil.formatWithLanguage("%.4f", value);
            DecimalFormat formater = new DecimalFormat();
            formater.setMaximumFractionDigits(4);
            formater.setGroupingSize(0);//设置分组大小。分组大小是数的整数部分中分组分隔符之间的数字位数,例如在数 "123,456.78" 中，分组大小是 3

            formater.setRoundingMode(RoundingMode.FLOOR);
            result = formater.format(value);
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留6位小数，不四舍五入
     */
    public static String getPot6f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.6f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留8位小数，不四舍五入
     */
    public static String getPot8f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.8f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }
    /**
     * 保留8位小数，不四舍五入
     */
    public static String getPot9f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.9f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * 保留18位小数，不四舍五入
     */
    public static String getPot18f(String num) {
        String result = null;
        try {
            double value = Double.parseDouble(num);
            if (value == 0) {
                return "0";
            }
            String str = NumberUtil.formatWithLanguage("%.18f", value);
            result = Double.parseDouble(str) + "";
            if (result.indexOf(".") > 0) {
                result = result.replaceAll("0+?$", "");
                result = result.replaceAll("[.]$", "");
            }
        } catch (Exception e) {
            result = "0";
        }
        return setFormate(result);
    }

    /**
     * String转double加异常判断
     *
     * @param data
     * @return
     */
    public static double getDoubleException(String data) {
        double value = 0;
        try {
            BigDecimal decimal = new BigDecimal(data);
            value = decimal.doubleValue();
        } catch (Exception e) {
            value = 0;
        }
        return value;
    }

    /**
     * String转double加异常判断
     *
     * @param data
     * @return
     */
    public static Integer getIntException(String data) {
        int value = 0;
        try {
            BigDecimal decimal = new BigDecimal(data);
            value = decimal.toBigInteger().intValue();
        } catch (Exception e) {
            value = 0;
        }

        return value;
    }


    /**
     * 获取数量，超过10万用万表示
     */
    public static String getVolStr(String value) {
        double volume = Double.parseDouble(value);
        String vol = volume + "";
        if (volume > 10000) {
            volume = getPot2f(volume);
        }
        if (volume / 100000 >= 1) {
            vol = NumberUtil.formatWithLanguage("%.2f", volume / 10000) + "";
            if (vol.indexOf(".") > 0) {
                vol = vol.replaceAll("0+?$", "");
                vol = vol.replaceAll("[.]$", "");
            }
            vol = vol + "W";
        } else {
            vol = NumberUtil.formatWithLanguage("%.2f", volume) + "";
            if (vol.indexOf(".") > 0) {
                vol = vol.replaceAll("0+?$", "");
                vol = vol.replaceAll("[.]$", "");
            }
        }
        return vol;
    }

    /**
     * 保留2位小数，四舍五入
     */
    public static String getPot2RMB(String num) {
        String result = "";
        try {
            double value = Double.parseDouble(num);
            result = NumberUtil.formatWithLanguage("%.2f", value);
        } catch (Exception e) {
            result = "0.00";
        }
        return result;
    }

    /**
     * 保留两位小数 不四舍五入 末位补零
     *
     * @param str
     * @return
     */
    public static String setFormateRMB(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0.00";
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            df.setRoundingMode(RoundingMode.DOWN);
            return df.format(Double.parseDouble(str));
        }
    }

    public static String setFormateRMBFmtt(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0.00";
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            df.setRoundingMode(RoundingMode.DOWN);
            String s = df.format(Double.parseDouble(str));
           return fmtMicrometer(s);
        }
    }

    /**
     * 金额千分位
     * @param text 文本
     * @return String
     */
    public static String fmtMicrometer(String text) {
        DecimalFormat df = null;
        if (text.indexOf(".") > 0) {
            if (text.length() - text.indexOf(".") - 1 == 0) {
                df = new DecimalFormat("###,##0.");
            } else if (text.length() - text.indexOf(".") - 1 == 1) {
                df = new DecimalFormat("###,##0.00");
            } else {
                df = new DecimalFormat("###,##0.00");
            }
        } else {
            df = new DecimalFormat("###,##0");
        }
        double number = 0.0;
        try {
            number = Double.parseDouble(text);
        } catch (Exception e) {
            number = 0.0;
        }
        return df.format(number);
    }


    /**
     * 行情发币显示
     * 大于 0.1 保留两位小数 不四舍五入 末位补零
     * 小于 0.1 保留四位小数 不四舍五入 末位不补零
     *
     * @param str
     * @return
     */
    public static String setFormateMarketRMB(String str) {
        str = str.replace(",", ".");
        if (StringUtil.isEmpty(str) || Double.parseDouble(str) == 0) {
            return "0.00";
        } else {
            if (Double.parseDouble(str) >= 0.1) {
                DecimalFormat df = new DecimalFormat("0.00");
                df.setRoundingMode(RoundingMode.DOWN);
                return df.format(Double.parseDouble(str));
            } else {
                DecimalFormat df = new DecimalFormat("0.00##");
                df.setRoundingMode(RoundingMode.DOWN);
                return df.format(Double.parseDouble(str));
            }
        }
    }



    /**
     * 格式字符串,默认设置成中文
     *
     * @param format
     * @param args
     * @return
     */
    public static String formatWithLanguage(String format, Object... args) {
        return String.format(Locale.ENGLISH, format, args);
    }

    public static boolean isDouble(String str) {
        boolean isdou = false;
        try {
            Double.parseDouble(str);
            isdou = true;
        } catch (Exception e) {
            isdou = false;
        }
        return isdou;
    }



    /**
     * double 相加
     *
     * @param d1
     * @param d2
     * @return
     */
    public static double addDouble(double d1, double d2) {
        BigDecimal bigDecimal1 = new BigDecimal(Double.toString(d1));
        BigDecimal bigDecimal2 = new BigDecimal(Double.toString(d2));
        return bigDecimal1.add(bigDecimal2).doubleValue();
    }





    public static double replaceDotToDouble(String text) {
        double value = 0;
        if (StringUtil.isEmpty(text)) {
            return value;
        }
        try {
            if (".".equals(text.substring(text.length() - 1))) {
                text = text.replace(".", "");
            }
            value = Double.parseDouble(text);

        } catch (Exception e) {
            value = 0;
        }
        return value;
    }

    /**
     * 大于1000转换成1K
     *
     * @param decimal
     * @return
     */
    public static String toThousandUnit(String decimal) {
        String num;
        double dou = 0;
        try {
            dou = Double.parseDouble(decimal);
        } catch (Exception e) {
            dou = 0;
        }
        if (dou > 1000) {
            dou = div(dou, 1000, 2);
            num = dou + "K";
        } else {
            dou = getPot6f(dou);
            num = setFormate(dou + "");
        }

        return num;
    }


    /**
     * 保留多少位小数 不四舍五入
     * 这个方法有bug,丢精度
     * 137.676
     *
     * @param digit 位数
     * @param num
     * @return
     */
    public static String getPotF(int digit, String num) {
        BigDecimal bd = new BigDecimal(num);
        BigDecimal setScale = bd.setScale(digit, RoundingMode.DOWN);
        return setFormate(setScale.toString());
    }


    /**
     * 保留几位小数,直接五入  (负数不五入)
     *
     * @param digit 几位小数
     * @param num
     * @return
     */
    public static String getRoundUP(int digit, String num) {
        BigDecimal bd = new BigDecimal(num);
        double dou = bd.setScale(digit, RoundingMode.UP).doubleValue();
        return setFormate(dou + "");
    }

    /**
     * 观点 点赞/评论数量
     * 超过999 显示k  超过999999 显示m
     *
     * @param num
     * @return
     */
    public static String viewCommentNum(int num) {
        String s = "";
        if (num < 0) {
            return "0";
        } else if (num >= 1000 && num < 1000000) {
            int x1 = num / 1000;
            int x2 = num % 1000 / 100;
            s = String.valueOf(x1);
            if (x2 != 0) {
                s = s + "." + x2;
            }
            s = s + "K";
            return s;
        } else if (num >= 1000000) {
            int x1 = num / 1000000;
            int x2 = num % 1000000 / 100000;
            s = String.valueOf(x1);
            if (x2 != 0) {
                s = s + "." + x2;
            }
            s = s + "M";
            return s;
        }
        return String.valueOf(num);
    }


    /**
     * 判断是否为数字
     * @param s
     * @return
     */
    public final static boolean isNumeric(String s) {
        if (s != null && !"".equals(s.trim()))
            return s.matches("^[0-9]*$");
        else
            return false;
    }
}
