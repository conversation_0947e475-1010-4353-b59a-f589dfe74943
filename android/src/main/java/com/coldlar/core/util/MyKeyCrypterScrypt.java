//package com.bitfork.core.util;
//
//import com.google.common.base.Objects;
//import com.google.common.base.Preconditions;
//import com.google.protobuf.ByteString;
//import com.lambdaworks.crypto.SCrypt;
//
//import org.bitcoinj.crypto.KeyCrypter;
//import org.bitcoinj.crypto.KeyCrypterException;
//import org.bitcoinj.crypto.KeyCrypterScrypt;
//import org.bitcoinj.wallet.Protos;
//import org.bitcoinj.crypto.EncryptedData;
//import org.bouncycastle.crypto.engines.AESFastEngine;
//import org.bouncycastle.crypto.modes.CBCBlockCipher;
//import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
//import org.bouncycastle.crypto.params.KeyParameter;
//import org.bouncycastle.crypto.params.ParametersWithIV;
//import org.bouncycastle.util.encoders.Hex;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.bouncycastle.crypto.BufferedBlockCipher;
//import java.io.Serializable;
//import java.security.SecureRandom;
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// * @date 2019/1/4 17:04
// * @description
// */
//public class MyKeyCrypterScrypt extends KeyCrypterScrypt  implements  KeyCrypter, Serializable {
//    private static final Logger log = LoggerFactory.getLogger(MyKeyCrypterScrypt.class);
//    private static final long serialVersionUID = 949662512049152670L;
//    public static final int KEY_LENGTH = 32;
//    public static final int BLOCK_LENGTH = 16;
//    public static final int SALT_LENGTH = 8;
//    private static final transient SecureRandom secureRandom = new SecureRandom();
//    private final transient Protos.ScryptParameters scryptParameters;
//    private static byte[] salts= new byte[0];
//
//    public static byte[] randomSalt() {
//        byte[] salt = new byte[8];
//        secureRandom.nextBytes(salt);
//        salts=salt;
//        return salt;
//    }
//
//    public MyKeyCrypterScrypt() {
//        Protos.ScryptParameters.Builder scryptParametersBuilder = Protos.ScryptParameters.newBuilder().setSalt(ByteString.copyFrom(randomSalt()));
//       scryptParameters = scryptParametersBuilder.build();
//    }
//
//    public MyKeyCrypterScrypt(byte[] salt) {
//        Protos.ScryptParameters.Builder scryptParametersBuilder = Protos.ScryptParameters.newBuilder().setSalt(ByteString.copyFrom(salt));
//        scryptParameters = scryptParametersBuilder.build();
//    }
//    public MyKeyCrypterScrypt(String salt) {
//        byte[] s= Hex.decode(salt);
//        Protos.ScryptParameters.Builder scryptParametersBuilder = Protos.ScryptParameters.newBuilder().setSalt(ByteString.copyFrom(s));
//       scryptParameters = scryptParametersBuilder.build();
//    }
//
//    public MyKeyCrypterScrypt(int iterations) {
//        Protos.ScryptParameters.Builder scryptParametersBuilder = Protos.ScryptParameters.newBuilder().setSalt(ByteString.copyFrom(randomSalt())).setN((long)iterations);
//        scryptParameters = scryptParametersBuilder.build();
//    }
//
//    public MyKeyCrypterScrypt(Protos.ScryptParameters scryptParameters) {
//        this.scryptParameters = (Protos.ScryptParameters) com.google.common.base.Preconditions.checkNotNull(scryptParameters);
//        if (scryptParameters.getSalt() == null || scryptParameters.getSalt().toByteArray() == null || scryptParameters.getSalt().toByteArray().length == 0) {
//            log.warn("You are using a ScryptParameters with no salt. Your encryption may be vulnerable to a dictionary attack.");
//        }
//
//    }
//
//    ////coin-review-todotag
//    @Override
//    public org.bitcoinj.crypto.EncryptedData encrypt(byte[] plainBytes, KeyParameter aesKey) throws KeyCrypterException {
//        com.google.common.base.Preconditions.checkNotNull(plainBytes);
//        com.google.common.base.Preconditions.checkNotNull(aesKey);
//
//        try {
//            byte[] iv = new byte[16];
//            secureRandom.nextBytes(iv);
//            ParametersWithIV keyWithIv = new ParametersWithIV(aesKey, iv);
//            BufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new CBCBlockCipher(new AESFastEngine()));
//            cipher.init(true, keyWithIv);
//            byte[] encryptedBytes = new byte[cipher.getOutputSize(plainBytes.length)];
//            int length1 = cipher.processBytes(plainBytes, 0, plainBytes.length, encryptedBytes, 0);
//            int length2 = cipher.doFinal(encryptedBytes, length1);
//            return new org.bitcoinj.crypto.EncryptedData(iv, Arrays.copyOf(encryptedBytes, length1 + length2));
//        } catch (Exception var9) {
//            throw new KeyCrypterException("Could not encrypt bytes.", var9);
//        }
//    }
//
//    @Override
//    public KeyParameter deriveKey(CharSequence password) throws KeyCrypterException {
//        byte[] passwordBytes = null;
//
//        KeyParameter var5;
//        try {
//            passwordBytes = convertToByteArray(password);
//            byte[] salt = new byte[0];
//            if (this.scryptParameters.getSalt() != null) {
//                salt = this.scryptParameters.getSalt().toByteArray();
//            } else {
//                log.warn("You are using a ScryptParameters with no salt. Your encryption may be vulnerable to a dictionary attack.");
//            }
//
//            byte[] keyBytes = SCrypt.scrypt(passwordBytes, salt, (int)this.scryptParameters.getN(), this.scryptParameters.getR(), this.scryptParameters.getP(), 32);
//            var5 = new KeyParameter(keyBytes);
//        } catch (Exception var9) {
//            throw new KeyCrypterException("Could not generate key from password and salt.", var9);
//        } finally {
//            if (passwordBytes != null) {
//                Arrays.fill(passwordBytes, (byte)0);
//            }
//
//        }
//
//        return var5;
//    }
//
//    @Override
//    public byte[] decrypt(EncryptedData privateKeyToDecode, KeyParameter aesKey) throws KeyCrypterException {
//        com.google.common.base.Preconditions.checkNotNull(privateKeyToDecode);
//        com.google.common.base.Preconditions.checkNotNull(aesKey);
//
//        try {
//            ParametersWithIV keyWithIv = new ParametersWithIV(new KeyParameter(aesKey.getKey()), privateKeyToDecode.initialisationVector);
//            BufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new CBCBlockCipher(new AESFastEngine()));
//            cipher.init(false, keyWithIv);
//            byte[] cipherBytes = privateKeyToDecode.encryptedBytes;
//            byte[] decryptedBytes = new byte[cipher.getOutputSize(cipherBytes.length)];
//            int length1 = cipher.processBytes(cipherBytes, 0, cipherBytes.length, decryptedBytes, 0);
//            int length2 = cipher.doFinal(decryptedBytes, length1);
//            return Arrays.copyOf(decryptedBytes, length1 + length2);
//        } catch (Exception var9) {
//            throw new KeyCrypterException("Could not decrypt bytes", var9);
//        }
//    }
//
//    private static byte[] convertToByteArray(CharSequence charSequence) {
//        Preconditions.checkNotNull(charSequence);
//        byte[] byteArray = new byte[charSequence.length() << 1];
//
//        for(int i = 0; i < charSequence.length(); ++i) {
//            int bytePosition = i << 1;
//            byteArray[bytePosition] = (byte)((charSequence.charAt(i) & '\uff00') >> 8);
//            byteArray[bytePosition + 1] = (byte)(charSequence.charAt(i) & 255);
//        }
//
//        return byteArray;
//    }
//    @Override
//    public Protos.ScryptParameters getScryptParameters() {
//        return this.scryptParameters;
//    }
//
//    @Override
//    public Protos.Wallet.EncryptionType getUnderstoodEncryptionType() {
//        return Protos.Wallet.EncryptionType.ENCRYPTED_SCRYPT_AES;
//    }
//
//
//
//    @Override
//    public String toString() {
//        return "Scrypt/AES";
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hashCode(new Object[]{this.scryptParameters});
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        } else if (o != null && this.getClass() == o.getClass()) {
//            MyKeyCrypterScrypt other = (MyKeyCrypterScrypt)o;
//            return Objects.equal(this.scryptParameters, other.scryptParameters);
//        } else {
//            return false;
//        }
//    }
//
//
//    public   String getSalt() {
//        return  Hex.toHexString(salts);
//    } public static   String getSalt2() {
//        return  Hex.toHexString(salts);
//    }
//
//}
