package com.coldlar.core.util;


import com.coldlar.coin.CoinType;
import com.coldlar.core.wallet.AbstractAddress;

import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.NetworkParameters;
import org.bitcoinj.core.SegwitAddress;

import java.nio.ByteBuffer;


public class BitSegwitAddress extends SegwitAddress implements AbstractAddress {


    private BitSegwitAddress(NetworkParameters params, int witnessVersion, byte[] witnessProgram) throws AddressFormatException {
        super(params, witnessVersion, witnessProgram);
    }

    public static BitSegwitAddress from(CoinType params, ECK<PERSON> key) {
        return new BitSegwitAddress(params, 0, key.getPubKeyHash());
    }

    @Override
    public String toString() {
        return super.toBech32();
    }

    @Override
    public CoinType getType() {
        return (CoinType) params;
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }
}
