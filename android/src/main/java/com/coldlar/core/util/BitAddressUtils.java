package com.coldlar.core.util;

import com.coldlar.coin.CoinType;
import com.coldlar.coin.bch.BitcoinCashMain;
import com.coldlar.coin.bitcoin.BitcoinDiamondMain;
import com.coldlar.core.wallet.AbstractAddress;
import com.coldlar.core.wallet.families.bitcoin.BitAddress;

import org.bitcoinj.core.Address;
import org.bitcoinj.core.LegacyAddress;
import org.bitcoinj.core.Transaction;
import org.bitcoinj.core.TransactionInput;
import org.bitcoinj.core.TransactionOutput;
import org.bitcoinj.core.Utils;
import org.bitcoinj.script.Script;
import org.bitcoinj.script.ScriptChunk;

import java.util.List;



public class BitAddressUtils {
    /**
     * coin-review-todotag 需要确认地址类型判断是否正确转换方式是否正确、
     *
     * @param address
     * @return
     */
    public static boolean isBchNewAddress(AbstractAddress address) {
        try {
            LegacyAddress address1 = LegacyAddress.fromBase58(BitcoinCashMain.get(), address.toString());
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    public static byte[] getHash160(AbstractAddress address) {
        if (address instanceof BitAddress) {
            return ((BitAddress) address).getHash160();
        } else {
            return null;
        }

    }


    /**
     * @param coinType
     * @param rawTx    16进制交易
     * @return Input中的第一个地址
     */
    public static Address getInputAddress(CoinType coinType, String rawTx) {
        Address address = null;
        boolean isQuar = false;
        if (coinType instanceof BitcoinDiamondMain) {
            rawTx = rawTx.replace(rawTx.substring(8, 72), "");
        }
        String str = rawTx.substring(8, 10);
        if (str.equals("00")) {
            rawTx = rawTx.substring(0, 8) + rawTx.substring(12, rawTx.length());
            isQuar = true;
        }
        byte[] txbytes = Utils.HEX.decode(rawTx);
        Transaction tx = new Transaction(coinType, txbytes);
        List<TransactionInput> inputs = tx.getInputs();
        TransactionInput input0 = inputs.get(0);
        if (input0.isCoinBase()) {
            return null;
        } else {
            Script script = input0.getScriptSig();
            try {
                List<ScriptChunk> chunks = script.getChunks();
                ScriptChunk scriptChunk = chunks.get(isQuar ? 0 : chunks.size() - 1);
                byte[] pukbyte = Utils.sha256hash160(scriptChunk.data);
                if (chunks.size() > 2) {//多签名//coin-review-todotag
//                    address = Address.fromP2SHHash(coinType, pukbyte);
                } else {
                    address = BitAddress.fromHash160Byte(coinType, false, pukbyte);////coin-review-todotag
                }
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return address;
    }


    /**
     * 取出TransactionOutput中的地址
     *
     * @param coinType 币种类型
     * @param output   输出
     * @return 输出地址
     */
    public static String getOutAddress(CoinType coinType, TransactionOutput output) {
        String address = "";
        TransactionOutput out = output;
        Script script = out.getScriptPubKey();
        Address add = script.getToAddress(coinType);
        if (add != null) {
            address = add.toString();
        }
        return address;
    }


}
