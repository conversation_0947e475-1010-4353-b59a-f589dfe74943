package com.coldlar.core.util;



import java.io.UnsupportedEncodingException;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {


    public static boolean isUrl(String urls) {
        boolean isurl = false;
        String regex = "(((https|http)?://)?([a-z0-9]+[.])|(www.))"
                + "\\w+[.|\\/]([a-z0-9]{0,})?[[.]([a-z0-9]{0,})]+((/[\\S&&[^,;\u4E00-\u9FA5]]+)+)?([.][a-z0-9]{0,}+|/?)";//设置正则表达式

        Pattern pat = Pattern.compile(regex.trim());//比对
        Matcher mat = pat.matcher(urls.trim());
        isurl = mat.matches();//判断是否匹配
        if (isurl) {
            isurl = true;
        }
        return isurl;

    }


    public static boolean isEmpty(String value) {
        return value == null || value.trim().equals("") || value.trim().equals("null") || value.length() == 0;
    }


    public static String checkPasswordStrength(String password) {
        int hasNumChar = 0,
                hasLowerCaseChar = 0,
                hasUpperCaseChar = 0,
                hasSpeciaChar = 0;
        int len = password.length();
        for (int i = 0; i < password.length(); i++) {
            char charStr = password.charAt(i);
            if (charStr >= '0' && charStr <= '9') {
                hasNumChar = 1;
            } else if (charStr >= 'a' && charStr <= 'z') {
                hasLowerCaseChar = 1;
            } else if (charStr >= 'A' && charStr <= 'Z') {
                hasUpperCaseChar = 1;
            } else {
                hasSpeciaChar = 1;
            }
        }

        int charTypeCount = hasNumChar + hasLowerCaseChar + hasUpperCaseChar + hasSpeciaChar;

        if (charTypeCount >= 3 && len >= 8) {
            return passwordStrength[2];
        } else if (charTypeCount >= 2 && len >= 8) {
            return passwordStrength[1];
        } else {
            return passwordStrength[0];
        }
    }


    public static int[] numberFormat() {
        int[] num = {0, 1, 2, 3, 4, 5, 6};
        Random r = new Random();
        int irdm = 0;
        int[] newNumber = new int[num.length];
        for (int i = 0; i < num.length; i++) {
            irdm = r.nextInt(num.length - i);
            newNumber[i] = num[irdm];
            for (int j = irdm; j < num.length - i - 1; j++) {
                num[j] = num[j + 1];
            }
        }
        return newNumber;
    }
    //点差值截取
    //截取字符串某字符前面的字符

    public static Double getStringFront(String value) {
        String str = value;
        if (!StringUtil.isEmpty(str)) {
            str = str.substring(0, str.indexOf("#"));
            return Double.parseDouble(str);
        } else {
            return 0d;
        }
    }

    //截取字符串某字符后的字符
    public static Double getStringBack(String value) {
        String str = value;
        if (!StringUtil.isEmpty(str)) {
            str = str.substring(str.indexOf("#") + 1, str.length());
            return Double.parseDouble(str);
        } else {
            return 0d;
        }

    }

    //截取字符串某字符前面的字符

    public static int getMoneyLen(String value) {
        String str = value;
        str = str.substring(0, str.indexOf("#"));
        return Integer.parseInt(str);
    }

    //截取字符串某字符后的字符
    public static int getCoinLen(String value) {
        String str = value;
        str = str.substring(str.indexOf("#") + 1, str.length());
        return Integer.parseInt(str);

    }

    public static String toLowercase(String str) {
        StringBuffer sb = new StringBuffer();
        if (str != null) {
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                if (Character.isUpperCase(c)) {
                    sb.append(Character.toLowerCase(c));
                } else {
                    sb.append(Character.toString(c));
                }
            }
        }

        return sb.toString();
    }

    public static String toUpper(String str) {
        StringBuffer sb = new StringBuffer();
        if (str != null) {
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                if (Character.isLowerCase(c)) {
                    sb.append(Character.toUpperCase(c));
                } else {
                    sb.append(Character.toString(c));
                }
            }
        }

        return sb.toString();
    }

    public static String exChange(String str) {
        StringBuffer sb = new StringBuffer();
        if (str != null) {
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                if (Character.isUpperCase(c)) {
                    sb.append(Character.toLowerCase(c));
                } else {
                    sb.append(Character.toString(c));
                }
            }
        }

        return sb.toString();
    }


    static String passwordStrength[] = {"0", "1", "2"};


    public static final String US_ASCII = "US-ASCII";

    /**
     * ISO 拉丁字母表 No.1，也叫作 ISO-LATIN-1
     */
    public static final String ISO_8859_1 = "ISO-8859-1";

    /**
     * 8 位 UCS 转换格式
     */
    public static final String UTF_8 = "UTF-8";

    /**
     * 16 位 UCS 转换格式，Big Endian（最低地址存放高位字节）字节顺序
     */
    public static final String UTF_16BE = "UTF-16BE";

    /**
     * 16 位 UCS 转换格式，Little-endian（最高地址存放低位字节）字节顺序
     */
    public static final String UTF_16LE = "UTF-16LE";

    /**
     * 16 位 UCS 转换格式，字节顺序由可选的字节顺序标记来标识
     */
    public static final String UTF_16 = "UTF-16";

    /**
     * 中文超大字符集
     */
    public static final String GBK = "GBK";

    /**
     * 将字符编码转换成US-ASCII码
     */
    public String toASCII(String str) throws UnsupportedEncodingException {
        return changeCharset(str, US_ASCII);
    }

    /**
     * 将字符编码转换成ISO-8859-1码
     */
    public static String toISO_8859_1(String str) throws UnsupportedEncodingException {
        return changeCharset(str, ISO_8859_1);
    }

    /**
     * 将字符编码转换成UTF-8码
     */
    public static String toUTF_8(String str) throws UnsupportedEncodingException {
        return changeCharset(str, UTF_8);
    }

    /**
     * 将字符编码转换成UTF-16BE码
     */
    public static String toUTF_16BE(String str) throws UnsupportedEncodingException {
        return changeCharset(str, UTF_16BE);
    }

    /**
     * 将字符编码转换成UTF-16LE码
     */
    public String toUTF_16LE(String str) throws UnsupportedEncodingException {
        return changeCharset(str, UTF_16LE);
    }

    /**
     * 将字符编码转换成UTF-16码
     */
    public String toUTF_16(String str) throws UnsupportedEncodingException {
        return changeCharset(str, UTF_16);
    }

    /**
     * 将字符编码转换成GBK码
     */
    public String toGBK(String str) throws UnsupportedEncodingException {
        return changeCharset(str, GBK);
    }

    /**
     * 字符串编码转换的实现方法
     *
     * @param str        待转换编码的字符串
     * @param newCharset 目标编码
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String changeCharset(String str, String newCharset)
            throws UnsupportedEncodingException {
        if (str != null) {
            //用默认字符编码解码字符串。
            byte[] bs = str.getBytes();
            //用新的字符编码生成字符串
            return new String(bs, newCharset);
        }
        return null;
    }

    /**
     * 字符串编码转换的实现方法
     *
     * @param str        待转换编码的字符串
     * @param oldCharset 原编码
     * @param newCharset 目标编码
     * @return
     * @throws UnsupportedEncodingException
     */
    public String changeCharset(String str, String oldCharset, String newCharset)
            throws UnsupportedEncodingException {
        if (str != null) {
            //用旧的字符编码解码字符串。解码可能会出现异常。
            byte[] bs = str.getBytes(oldCharset);
            //用新的字符编码生成字符串
            return new String(bs, newCharset);
        }
        return null;
    }




    /**
     * String转16进制
     *
     * @param
     * @return
     */
    public static String strTo0x(String str) {
        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
            // sb.append(' ');
        }
        return sb.toString().trim();
    }
    /**
     * 将string 转大大写 如果参数字符串为空 就返回""
     * @param str 字符
     * @return
     */
    public static String toUpperCase(String str) {
        if (!isEmpty(str)) {
            return str.toUpperCase();
        }
        return "";
    }


    /**
     * 替换文案，拓展币种时被写死的文案，替换symbol
     * @param content
     * @param str1
     * @param str2
     * @return
     */
    public static String  replaceStr(String content,String str1,String str2){
        return content.replace(str1,str2);
    }



}
