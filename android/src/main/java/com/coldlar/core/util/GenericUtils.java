package com.coldlar.core.util;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.CoreCoinBase;
import com.coldlar.coin.act.ACTAddress;
import com.coldlar.coin.act.AchainMain;
import com.coldlar.coin.ethereum.EthClassicMain;
import com.coldlar.coin.ethereum.EthereumMain;
import com.coldlar.coin.other.KcashMain;
import com.coldlar.coin.other.Value;
import com.coldlar.coin.other.ValueType;
import com.coldlar.coin.ripple.RippleMain;
import com.coldlar.core.exceptions.AddressMalformedException;
import com.google.common.collect.ImmutableList;

import org.apache.commons.lang3.StringUtils;
import org.bitcoinj.core.Monetary;

import java.util.List;
import java.util.Locale;

import javax.annotation.Nonnull;

/**
 * //coin-review-todotag
 */
public class GenericUtils {
    public static String formatValue(@Nonnull final Value value) {
        return formatCoinValue(value.type, value.toCoin(), "", "-", 8, 0);
    }

    public static String formatCoinValue(@Nonnull final ValueType type, @Nonnull final Monetary value) {
        return formatCoinValue(type, value, "", "-", 8, 0);
    }

    public static String formatCoinValue(@Nonnull final ValueType type, @Nonnull final Monetary value,
                                         final int precision, final int shift) {
        return formatCoinValue(type, value, "", "-", precision, shift);
    }

    public static String formatCoinValue(@Nonnull final ValueType type, @Nonnull final Monetary value,
                                         @Nonnull final String plusSign, @Nonnull final String minusSign,
                                         final int precision, final int shift) {
        return formatValue(type.getUnitExponent(), value, plusSign, minusSign, precision, shift, false);
    }

    public static String formatCoinValue(@Nonnull final ValueType type, @Nonnull final Monetary value,
                                         boolean removeFinalZeroes) {
        return formatValue(type.getUnitExponent(), value, "", "-", 8, 0, removeFinalZeroes);
    }

    private static String formatValue(final long unitExponent, @Nonnull final Monetary value,
                                      @Nonnull final String plusSign, @Nonnull final String minusSign,
                                      final int precision, final int shift, boolean removeFinalZeroes) {
        long longValue = value.getValue();

        final String sign = value.signum() == -1 ? minusSign : plusSign;

        String formatedValue;

        if (shift == 0) {
            long units = Math.round(Math.pow(10, unitExponent));
            long precisionUnits = (long) (units / Math.pow(10, precision));
            long roundingPrecisionUnits = precisionUnits / 2;

            if (precision == 2 || precision == 4 || precision == 6 || precision == 8) {
                if (roundingPrecisionUnits > 0) {
                    longValue = longValue - longValue % precisionUnits + longValue % precisionUnits / roundingPrecisionUnits * precisionUnits;
                }
            } else {
                throw new IllegalArgumentException("cannot handle precision/shift: " + precision + "/" + shift);
            }

            final long absValue = Math.abs(longValue);
            final long coins = absValue / units;
            final int satoshis = (int) (absValue % units);

            if (isShiftPossible(units, satoshis, 100)) {
                formatedValue = String.format(Locale.US, "%d.%02d",
                        coins, getShiftedCents(units, satoshis, 100));

            } else if (isShiftPossible(units, satoshis, 10000)) {
                formatedValue = String.format(Locale.US, "%d.%04d",
                        coins, getShiftedCents(units, satoshis, 10000));

            } else if (isShiftPossible(units, satoshis, 1000000)) {
                formatedValue = String.format(Locale.US, "%d.%06d", coins,
                        getShiftedCents(units, satoshis, 1000000));

            } else {
                formatedValue = String.format(Locale.US, "%d.%08d", coins, satoshis);
            }

        } else {
            throw new IllegalArgumentException("cannot handle shift: " + shift);
        }

        // Relax precision if incorrectly shows value as 0.00 but is in reality not zero
        if (formatedValue.equals("0.00") && value.getValue() != 0) {
            return formatValue(unitExponent, value, plusSign, minusSign, precision + 2, shift, removeFinalZeroes);
        }

        // Remove final zeroes if requested
        while (removeFinalZeroes && formatedValue.length() > 0 &&
                formatedValue.contains(".") && formatedValue.endsWith("0")) {
            formatedValue = formatedValue.substring(0, formatedValue.length() - 1);
        }
        if (removeFinalZeroes && formatedValue.length() > 0 && formatedValue.endsWith(".")) {
            formatedValue = formatedValue.substring(0, formatedValue.length() - 1);
        }

        // Add the sign if needed
        formatedValue = String.format(Locale.US, "%s%s", sign, formatedValue);

        return formatedValue;
    }

    private static long getShiftedCents(long units, int satoshis, int centAmount) {
        return satoshis / (units / centAmount);
    }

    private static boolean isShiftPossible(long units, int satoshis, int centAmount) {
        return units / centAmount != 0 && satoshis % (units / centAmount) == 0;
    }

    public static String formatFiatValue(final Value fiat, final int precision, final int shift) {
        return formatValue(fiat.smallestUnitExponent(), fiat, "", "-", precision, shift, false);
    }

    public static String formatFiatValue(Value fiat) {
        return formatFiatValue(fiat, 2, 0);
    }

    /**
     * Parses the provided string and returns the possible supported coin types.
     * Throws an AddressFormatException if the string is not a valid address or not supported.
     */
    public static List<CoinType> getPossibleTypes(String addressStr) throws AddressMalformedException {
        ImmutableList.Builder<CoinType> builder = ImmutableList.builder();
        tryBitcoinFamilyAddresses(addressStr, builder);
        tryEthereumFamilyAddresses(addressStr, builder);
        tryRippleFamilyAddress(addressStr, builder);
        tryActFamilyAddress(addressStr, builder);
        // TODO try other coin addresses
        List<CoinType> possibleTypes = builder.build();
        if (possibleTypes.size() == 0) {
            throw new AddressMalformedException("Unsupported address: " + addressStr);
        }
        return possibleTypes;
    }

    private static void tryRippleFamilyAddress(String address, ImmutableList.Builder<CoinType> paramBuilder) {
        if (ValidateAddressUtil.validateRippleAddress(address)) {
            paramBuilder.add(RippleMain.get());
        }
    }

    private static void tryActFamilyAddress(String address, ImmutableList.Builder<CoinType> paramBuilder) {
        try {
            if(!StringUtils.isAnyEmpty(address)&&address.length()>3){
                address.replace("0x","");
                if (ACTAddress.check(address.substring(3), ACTAddress.Type.ADDRESS)) {
                    paramBuilder.add(AchainMain.get());
                    paramBuilder.add(KcashMain.get());
                }
            }
        }catch (Exception e){

        }
    }



    /**
     * Tries to parse the addressStr as a Bitcoin style address and find potential compatible coin types
     *
     * @param addressStr possible bitcoin type address
     * @param builder    for the types list
     */
    private static void  tryBitcoinFamilyAddresses(final String addressStr, ImmutableList.Builder<CoinType> builder) {
        int version=9999;
        try {
             version = AddressUtil.getExpectedVersion(20, addressStr);
        }catch (Exception e){

        }
        List<CoinType> possibleTypes = CoreCoinBase.getAllCainList();
        //coin-review-todotag
        for (CoinType type : possibleTypes) {
            if (type.getAcceptableAddressCodes() == null){
                continue;
            }
            for (int addressCode : type.getAcceptableAddressCodes()) {
                if (addressCode == version) {
                    builder.add(type);
                    break;
                }
            }
        }
    }

    private static void tryEthereumFamilyAddresses(String paramString, ImmutableList.Builder<CoinType> paramBuilder) {
        if (paramString.matches("^(0x)?[0-9a-fA-F]{40}$")) {
            paramBuilder.add(EthereumMain.get());
            paramBuilder.add(EthClassicMain.get());
        }
    }


}