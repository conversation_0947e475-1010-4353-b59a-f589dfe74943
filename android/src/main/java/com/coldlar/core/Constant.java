package com.coldlar.core;

public class Constant {
    /**
     * 固定请求节点的错误方法
     */
    public static final String ERROR_METHOD = "asdsd_fewasdf_cser";
    /**
     * 固定请求节点错误提示
     */
    public static final String ERROR_MESSAGE = "unknown method: '" + ERROR_METHOD + "'";
    /**
     * SOCKET请求参数后缀
     */
    public static final String SOCKET_PARAMS_SUFFIX = "\n";

    /**
     * 种子类型
     */
    public static class SeedType {
        public final static int EN = 0;
        public final static int CN = 1;
        public final static int NUM = 2;
    }

    /**
     * 数字助记词默认类型
     */
    public static final int SEED_NUM_PRO4 = 0;
    public static final int SEED_NUM_PRO3 = 1;


    public static final class TrxType {
        public static final String TRC_10 = "TransferAssetContract";
        public static final String TRC_20 = "TriggerSmartContract";
        public static final String TRANSFER = "TransferContract";
        public static final String FROZEN = "FreezeBalanceContract";
        public static final String UNFROZEN = "UnfreezeBalanceContract";
        public static final String VOTE = "VoteWitnessContract";


        //质押2.0
        public static final String FROZEN_V2 = "FreezeBalanceV2Contract";


        public static final String UNFROZEN_V2 = "UnfreezeBalanceV2Contract";

        public static final String WithdrawExpireUnfreeze = "WithdrawExpireUnfreezeContract";

        //代理
        public static final String DelegateResourceContract = "DelegateResourceContract";

        //回收资源
        public static final String UnDelegateResourceContract = "UnDelegateResourceContract";


    }

}
