package com.coldlar.core;

import com.google.common.base.Joiner;

import org.bitcoinj.core.Sha256Hash;
import org.bitcoinj.crypto.MnemonicCode;
import org.bitcoinj.crypto.MnemonicException;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;


public class CoreUtils {
    public static int ENTROPY_SIZE_DEBUG = -1;
    /**
     * P4、APP钱包、Touch等所有的库神钱包，在创建钱包时，避免创建出所有数字都在1000-2047这种助记词，
     * 这种助记词无法判断是哪种类型，是否需要加1000
     * 即当用户选择数字助记词时，随机生成一组助记词后，要判断是不是都在这个范围之内，如果是，就重新生成一组
     */
    public static int SEED_LENGTH_TAG = 1047;


    /**
     * 生成助记词
     *
     * @param entropyBitsSize 多少位
     * @return
     */
    public static List<String> generateMnemonic(int entropyBitsSize) {
        byte[] entropy;
        if (ENTROPY_SIZE_DEBUG > 0) {
            entropy = new byte[ENTROPY_SIZE_DEBUG];
        } else {
            entropy = new byte[entropyBitsSize / 8];
        }
        List<String> seedList=null;
        while (true){
            SecureRandom sr = new SecureRandom();
            sr.nextBytes(entropy);
            byte[] hash = Sha256Hash.hash(entropy);////coin-review-todotag
            boolean[] hashBits = bytesToBits(hash);
            boolean[] entropyBits = bytesToBits(entropy);
            int checksumLengthBits = entropyBits.length / 32;
            boolean[] concatBits = new boolean[entropyBits.length + checksumLengthBits];
            System.arraycopy(entropyBits, 0, concatBits, 0, entropyBits.length);
            System.arraycopy(hashBits, 0, concatBits, entropyBits.length, checksumLengthBits);
            int nwords = concatBits.length / 11;
           boolean seedOk=false;
            for (int i = 0; i < nwords; ++i) {
                int index = 0;
                for (int j = 0; j < 11; ++j) {
                    index <<= 1;
                    if (concatBits[i * 11 + j]) {
                        index |= 1;
                    }
                }
                if(index>= SEED_LENGTH_TAG){
                    seedOk=true;
                }
            }
            if(seedOk){
                seedList=CoreUtils.bytesToMnemonic(entropy);
                break;
            }else {
                continue;
            }
        }
        return seedList;
    }


    private static boolean[] bytesToBits(byte[] data) {
        boolean[] bits = new boolean[data.length * 8];

        for (int i = 0; i < data.length; ++i) {
            for (int j = 0; j < 8; ++j) {
                bits[i * 8 + j] = (data[i] & 1 << 7 - j) != 0;
            }
        }

        return bits;
    }


    public static String getMnemonicToString(List<String> mnemonic) {
        return Joiner.on(' ').join(mnemonic);
    }

    /**
     * byte数组转化成string数组
     *
     * @param bytes
     * @return
     */
    public static List<String> bytesToMnemonic(byte[] bytes) {
        List<String> mnemonic;
        try {
            mnemonic = MnemonicCode.INSTANCE.toMnemonic(bytes);
        } catch (MnemonicException.MnemonicLengthException e) {
            throw new RuntimeException(e); // should not happen, we have 16bytes of entropy
        }
        return mnemonic;
    }

    public static String bytesToMnemonicString(byte[] bytes) {
        return getMnemonicToString(bytesToMnemonic(bytes));
    }

    public static ArrayList<String> parseMnemonic(String mnemonicString) {
        ArrayList<String> seedWords = new ArrayList<>();
        for (String word : mnemonicString.trim().split(" ")) {
            if (word.isEmpty()) continue;
            seedWords.add(word);
        }
        return seedWords;
    }

}
