package com.coldlar.core.base;

public interface Chain {

    /**
     * P4 地址正常类型
     */
    public static final int ADDRESS_TYPE_NOMAL = 0;
    /**
     * P4 地址P2SH类型
     */
    public static final int ADDRESS_TYPE_P2SH = 1;
    /**
     * P4 地址BECH32类型
     */
    public static final int ADDRESS_TYPE_BECH32 = 2;
    /**
     * 地址路径 bip44
     */
    public static final int ADDRESS_BIP44 = 44;
    /**
     * 地址路径 bip49
     */
    public static final int ADDRESS_BIP49 = 49;
    /**
     * 地址路径 bip84
     */
    public static final int ADDRESS_BIP84 = 84;

    /**
     * 地址路径 bip86
     */
    public static final int ADDRESS_BIP86 = 86;
    /**
     * EOS交易过期时间
     */
    public static final int EOS_ACTION_DEADLING = 1800000;

    String btc ="btc";

    String eth = "eth";

    String bnb = "bnb";

    String bch = "bch";

    String doge = "doge";

    String ltc = "ltc";

    String usdt = "usdt";

    String dash = "dash";

    String eos = "eos";

    String bsc = "bsc";

    String ht = "ht";

    String okt = "okt";

    String polygon = "polygon";

    String arb = "arb";

    String opt = "opt";

    String etc = "etc";

    String fil = "fil";

    String xem = "xem";

    String qtum = "qtum";

    String xrp = "xrp";

    String trx = "trx";

    String zec = "zec";
}
