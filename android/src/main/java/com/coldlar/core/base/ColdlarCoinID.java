package com.coldlar.core.base;

/**
 * Created by Administrator on 2017/8/16 0016.
 */

public class ColdlarCoinID {

    public static final String ID_All_Chain = "all";

    /**
     * KCASH 支持定制版本设置为主链，特殊处理
     */
    public static final String ID_KCASH = "0";
    /**
     * GAS 支持定制版本设置为主链，特殊处理
     */
    public static final String ID_GAS = "1";
    /**
     * WWB 支持定制版本设置为主链，特殊处理
     */
    public static final String ID_WWB = "2";
    /**
     * BTC
     */
    public static final String ID_BTC = "000";
    /**
     * LTC
     */
    public static final String ID_LTC = "001";
    /**
     * BCD
     */
    public static final String ID_BCD = "002";
    /**
     * DASH
     */
    public static final String ID_DASH = "003";
    /**
     * BTG
     */
    public static final String ID_BTG = "004";
    /**
     * DOGE
     */
    public static final String ID_DOGE = "006";
    /**
     * ETH
     */
    public static final String ID_ETH = "009";
    /**
     * ETC
     */
    public static final String ID_ETC = "010";
    /**
     * BCH
     */
    public static final String ID_BCH = "011";
    /**
     * ZCASH
     */
    public static final String ID_ZCASH = "013";
    /**
     * XRP
     */
    public static final String ID_RIPPLE = "014";
    /**
     * NEM
     */
    public static final String ID_NEM = "015";
    /**
     * USDT
     */
    public static final String ID_USDT = "016";
    /**
     * SBTC
     */
    public static final String ID_SBTC = "017";
    /**
     * BCX
     */
    public static final String ID_BCX = "018";
    /**
     * QTUM
     */
    public static final String ID_QTUM = "020";
    /**
     * IPC
     */
    public static final String ID_IPCHAIN = "021";
    /**
     * ACT
     */
    public static final String ID_ACHIN = "022";
    /**
     * EOS
     */
    public static final String ID_EOS = "023";
    /**
     * GOD
     */
    public static final String ID_GOD = "024";
    /**
     * NEO
     */
    public static final String ID_NEO = "025";
    /**
     * WICC
     */
    public static final String ID_WICC = "027";
    /**
     * BSV
     */
    public static final String ID_BSV = "028";
    /**
     * BTM
     */
    public static final String ID_BTM = "029";
    /**
     * ISOT
     */
    public static final String ID_IOST = "030";
    /**
     * TRX
     */
    public static final String ID_TRX = "032";
    /**
     * XLM
     */
    public static final String ID_XLM = "033";
    /**
     * HTDF
     */
    public static final String ID_HTDF = "034";
    /**
     * TOMO
     */
    public static final String ID_TOMO = "035";
    /**
     * BTP
     */
    public static final String ID_BTP = "036";
    /**
     * POC
     */
    public static final String ID_POC = "037";
    /**
     * BNB
     */
    public static final String ID_BNB = "038";
    /**
     * ATOM
     */
    public static final String ID_ATOM = "039";
    /**
     * FIL
     */
    public static final String ID_FIL = "040";
    /**
     * FSC
     */
    public static final String ID_FSC = "041";

    /**
     * BSC
     */
    public static final String ID_BSC = "100";
    /**
     * MATIC
     */
    public static final String ID_MATIC = "101";
    /**
     * ARB
     */
    public static final String ID_ARB = "102";
    /**
     * OPT
     */
    public static final String ID_OPT = "103";
    /**
     * HT
     */
    public static final String ID_HT = "104";

    /**
     * OKT
     */
    public static final String ID_OKT = "105";

    /**
     * BASE
     */
    public static final String ID_BASE = "106";

    /**
     * ZKS
     */
    public static final String ID_ZKS = "107";
    /**
     * ZKE
     */
    public static final String ID_ZKE = "108";
    /**
     * LINEA
     */
    public static final String ID_LINEA = "109";
    /**
     * BLAST
     */
    public static final String ID_BLAST = "110";
    /**
     * AVAX
     */
    public static final String ID_AVAX = "112";

    /**
     * FVM
     */
    public static final String ID_FVM = "113";
    /**
     * EOSVM
     */
    public static final String ID_EOSVM = "114";

    /**
     * BTC Test
     */
    public static final String ID_BTCTEST = "10001";

    /**
     * BTC Test
     */
    public static final String ID_BTCSIGNTEST = "10002";

    /**
     * ETH Test
     */
    public static final String ID_ETH_HOLESKY = "17000";

    /**
     * SOL
     */
    public static final String ID_SOLANA = "501";


    public static final String ID_Baby = "116";
    public static final String ID_TBaby = "117";

}
