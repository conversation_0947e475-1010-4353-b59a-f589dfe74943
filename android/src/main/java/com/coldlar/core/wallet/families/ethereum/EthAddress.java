package com.coldlar.core.wallet.families.ethereum;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.ethereum.EthereumMain;
import com.coldlar.coin.ethereum.transaction.HashUtil;
import com.coldlar.core.wallet.AbstractAddress;

import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

public final class EthAddress implements AbstractAddress {
    private final String address;
    private final CoinType type;

    public EthAddress(CoinType paramCoinType, String paramString) {
        this.type = paramCoinType;
        this.address = paramString.replace("0x", "").toLowerCase();
    }

    public EthAddress(CoinType paramCoinType, String paramString, String type) {
        this.type = paramCoinType;
        this.address = paramString.toLowerCase();
    }

    public byte[] getHash160() {
        return HashUtil.ripemd160(this.address.getBytes(Charset.forName("utf-8")));
    }

    public String getHexString() {
        return this.address;
    }

    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    public CoinType getType() {
        return this.type;
    }

    @Override
    public String toString() {
        return "0x" + this.address;
    }


    public String addressFormat() {
        return addressFormat(this.address);
    }


    public String addressFormat(String address) {
        String lowercaseAddress = Numeric.cleanHexPrefix(address).toLowerCase();
        String addressHash = Numeric.cleanHexPrefix(Hash.sha3String(lowercaseAddress));
        StringBuilder result = new StringBuilder(lowercaseAddress.length() + 2);
        result.append("0x");
        for (int i = 0; i < lowercaseAddress.length(); ++i) {
            if (Integer.parseInt(String.valueOf(addressHash.charAt(i)), 16) >= 8) {
                result.append(String.valueOf(lowercaseAddress.charAt(i)).toUpperCase());
            } else {
                result.append(lowercaseAddress.charAt(i));
            }
        }
        return result.toString();
    }


}