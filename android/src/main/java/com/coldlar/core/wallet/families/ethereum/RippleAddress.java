package com.coldlar.core.wallet.families.ethereum;


import com.coldlar.coin.CoinType;
import com.coldlar.coin.ethereum.transaction.HashUtil;
import com.coldlar.core.wallet.AbstractAddress;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

public final class RippleAddress implements AbstractAddress
{
    private final String address;
    private final CoinType type;

    public RippleAddress(CoinType paramCoinType, String paramString)
    {
        this.type = paramCoinType;
        this.address = paramString;
    }

    public byte[] getHash160()
    {
        return HashUtil.ripemd160(this.address.getBytes(Charset.forName("utf-8")));
    }

    public long getId()
    {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    public CoinType getType()
    {
        return this.type;
    }

    @Override
    public String toString()
    {
        return this.address;
    }
}