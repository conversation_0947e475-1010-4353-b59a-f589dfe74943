package com.coldlar.core.wallet.families.bitcoin;

import com.coldlar.coin.CoinType;
import com.coldlar.core.wallet.AbstractAddress;

import org.bitcoinj.core.AddressFormatException;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.LegacyAddress;
import org.bitcoinj.core.NetworkParameters;
import org.bouncycastle.util.encoders.Hex;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public class BitAddress extends LegacyAddress implements AbstractAddress {


    private BitAddress(NetworkParameters params, boolean p2sh, byte[] hash160) throws AddressFormatException {
        super(params, p2sh, hash160);
    }


    /**
     * 通过地址获取BitAddress
     *
     * @param params  币种类型
     * @param address 真实地址
     * @return
     */
    public static BitAddress fromAddress(CoinType params, String address) {
        LegacyAddress address1 = LegacyAddress.fromBase58(params, address);
        return new BitAddress(params, false, address1.getHash160());
    }

    /**
     * 通过hash160获取BitAddress,默认为P2PKSH类型
     *
     * @param type
     * @param publicKeyHash160 PKhash160
     * @return
     */
    public static BitAddress fromHash160Byte(CoinType type, byte[] publicKeyHash160) {
        return new BitAddress(type, false, publicKeyHash160);
    }

    public static BitAddress fromHash160String(NetworkParameters params, String hash160) throws AddressFormatException {
        return new BitAddress(params, false, Hex.decode(hash160));
    }

    /**
     * 通过hash160获取BitAddress,传入地址类型，适用于多签地址
     *
     * @param type
     * @param isp2sh           是否为P2SH类型地址
     * @param publicKeyHash160 PKhash160
     * @return
     */
    public static BitAddress fromHash160Byte(CoinType type, boolean isp2sh, byte[] publicKeyHash160) {
        return new BitAddress(type, isp2sh, publicKeyHash160);
    }

    public static BitAddress fromHash160String(NetworkParameters params, boolean p2sh, String hash160) throws AddressFormatException {
        return new BitAddress(params, p2sh, Hex.decode(hash160));
    }


    /**
     * 通过公钥生成地址，传入地址类型
     *
     * @param params
     * @param key
     * @return
     */
    public static BitAddress fromEckey(CoinType params, ECKey key) {
        return new BitAddress(params, false, key.getPubKeyHash());
    }

    public static BitAddress fromEckey(CoinType params, boolean isP2sh, ECKey key) {
        return new BitAddress(params, isP2sh, key.getPubKeyHash());
    }


    /**
     * 通过公钥生成地址，不传入地址类型，默认为P2PKSH类型
     *
     * @param params
     * @param key
     * @return
     */
    public static BitAddress from(CoinType params, ECKey key) {
        return new BitAddress(params, false, key.getPubKeyHash());
    }


    @Override
    public CoinType getType() {
        return (CoinType) getParameters();
    }

    @Override
    public long getId() {
        return ByteBuffer.wrap(getHash160()).getLong();
    }

    /**
     *
     */

}
