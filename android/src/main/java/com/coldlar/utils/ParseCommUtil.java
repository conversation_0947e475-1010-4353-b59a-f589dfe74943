package com.coldlar.utils;

import android.text.TextUtils;

import com.coldlar.model.SignData;


/**
 * <AUTHOR> huyx
 * @date : 2020/9/15 11:55
 */
public class ParseCommUtil {

    private static final int SIGN_LEN1 = 128;
    private static final int SIGN_LEN2 = 130;

    /***
     * 解析交易签名数据（R，S，V）
     */
    public static SignData parseSignData(String data) {
        if (TextUtils.isEmpty(data)) {
            return null;
        }
        try {
            if (data.length() == SIGN_LEN1 || data.length() == SIGN_LEN2) {
                SignData signRet = new SignData();
                signRet.setR(data.substring(0, 64));
                signRet.setS(data.substring(64, 128));
                if (data.length() == SIGN_LEN2) {
                    signRet.setV(data.substring(128, 130));
                } else {
                    signRet.setV("00");
                }
                return signRet;
            }
        } catch (Exception e) {
        }
        return null;
    }


}
