package com.coldlar.utils;

import android.text.TextUtils;

import org.bitcoinj.core.Utils;
import org.bitcoinj.crypto.MnemonicCode;
import org.spongycastle.util.encoders.Hex;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *         数字助记词说明
 *         pro3：(1-2048)
 *         pro4：（1001-3048）
 */

public class SeedUtil {

    private static final int SEED_CHINESE = 1;
    public static String BIP39_ENGLISH_SHA256 = "ad90bf3beb7b0eb7e5acd74727dc0da96e0a280a258354e7293fb7e211ac03db";

    public static final int SEED_TYPE_ENG = 0;
    public static final int SEED_TYPE_NUM = 1;
    public static final int SEED_TYPE_CN = 2;
    public static final int SEED_NUMBER_SHOW = 1000;

    public static boolean isChinese(String str) {
        String regEx = "[\\u4e00-\\u9fa5]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isNumber(String str) {
        String regEx = "[0-9]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * byte数组转化成string数组
     */
    public static List<String> bytesToMnemonic(byte[] seedEntropy) {
        List<String> mnemonic;
        try {
            // 选择创建钱包才使用卡片的种子
            ByteArrayInputStream inputStream = new ByteArrayInputStream(seedEntropy);
            byte[] value = new byte[seedEntropy.length - 1];
            inputStream.read(value);
            mnemonic = MnemonicCode.INSTANCE.toMnemonic(value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return mnemonic;
    }

    /**
     * 2048助记词
     */
    public static List<String> getPhrases(int type) {

        List<String> wordList = new ArrayList<>();
        switch (type) {
            case SEED_TYPE_ENG:
                wordList = getWordList(0);
                break;
            case SEED_TYPE_CN:
                wordList = getWordList(1);
                break;
            case SEED_TYPE_NUM:
                String mnemonic = mnemonicListToString(getWordList(0));
                String wordListStr = seedToNumberShow(mnemonic);
                if (wordListStr != null) {
                    wordList = mnemonicStringToList(wordListStr);
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported seed type");
        }

        return wordList;
    }

    /**
     * 熵转换助记词
     *
     * @param data 随机数据（熵）
     * @param type 助记词类型
     * @return 助记词列表
     */
    public static List<String> getMnemonic(byte[] data, int type) {
        List<String> mnemonicList = bytesToMnemonic(data);
        if (type == SEED_TYPE_ENG) {
            return mnemonicList;
        }

        String mnemonic = mnemonicListToString(mnemonicList);

        switch (type) {
            case SEED_TYPE_CN:
                mnemonic = seedToaChinese(mnemonic);
                break;
            case SEED_TYPE_NUM:
                mnemonic = seedToNumberShow(mnemonic);
                break;
            default:
                throw new IllegalArgumentException("Unsupported seed type");
        }

        return mnemonicStringToList(mnemonic);
    }

    public static String seedToaChinese(String mnemonic) {
        // 1.获取英文单词列表
        List<String> wordEnList = getWordList(0);
        // 2.查找出单词对应的序号
        List<Integer> wordSerialNumber = getSelectWordsNum(wordEnList, mnemonic);
        // 3.获取中文单词列表
        List<String> wordCnList = getWordList(1);
        // 4.找出对应的中文，转换成字符串
        String result = getMnemonic(wordCnList, wordSerialNumber);
        return result;
    }

    public static String seedToEnglish(String mnemonic) {
        // 1.获取中文单词列表
        List<String> wordCnList = getWordList(1);
        // 2.查找出单词对应的序号
        List<Integer> wordSerialNumber = getSelectWordsNum(wordCnList, mnemonic);
        // 3.获取英文单词列表
        List<String> wordEnList = getWordList(0);
        // 4.找出对应的中文，转换成字符串
        String result = getMnemonic(wordEnList, wordSerialNumber);
        return result;
    }

    /**
     * 数字减去1000 转英文
     *
     * @param context
     * @param mnemonic
     * @return
     */
    public static String seedNumToEnglishShow(String mnemonic) {
        String result = null;
        try {
            // 1.获取英文单词列表
            List<String> wordEnList = getWordList(0);
            String[] numbers = mnemonic.split(" ");
            List<Integer> wordSerialNumber = new ArrayList<>();
            for (int i = 0; i < numbers.length; i++) {
                if (numbers[i].isEmpty()) {
                    continue;
                }
                wordSerialNumber.add(Integer.parseInt(numbers[i]) - SEED_NUMBER_SHOW);
            }
            // 找出对应的中文，转换成字符串
            result = getMnemonic(wordEnList, wordSerialNumber);
        } catch (Exception e) {

        }
        return result;
    }

    /**
     * 加1000的数字种子
     *
     * @param mumemonic
     * @return
     */
    public static String seedToNumberShow(String mumemonic) {
        // 1.获取英文单词列表
        List<String> wordEnList = getWordList(0);
        // 2.查找出单词对应的序号
        List<Integer> wordSerialNumber = getSelectWordsNum(wordEnList, mumemonic);

        if (wordSerialNumber == null) {
            return null;
        } else {
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < wordSerialNumber.size(); i++) {
                sb.append(wordSerialNumber.get(i) + SEED_NUMBER_SHOW).append(" ");
            }
            return sb.toString();
        }
    }

    /**
     * 序号转中文
     */
    private static String getMnemonic(List<String> wordCnList, List<Integer> wordSerialNumber) {
        if (wordCnList == null || wordSerialNumber == null) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (int num : wordSerialNumber) {
            sb.append(wordCnList.get(num)).append(" ");
        }
        return sb.toString();
    }

    /**
     * 获取对应的序号
     *
     * @param wordList
     * @param mnemonic
     * @return
     */
    private static List<Integer> getSelectWordsNum(List<String> wordList, String mnemonic) {
        if (TextUtils.isEmpty(mnemonic)) {
            return null;
        }
        String[] mnemonicWords = mnemonic.split(" ");

        List<Integer> items = new ArrayList<>();
        for (int i = 0; i < mnemonicWords.length; i++) {
            String word = mnemonicWords[i];
            if (word.isEmpty()) {
                continue;
            }
            int ndx = binarySearch(wordList, word);
            if (ndx < 0) {
                return null;
            } else {
                items.add(ndx);
            }
        }
        return items;
    }

    private static int binarySearch(List<String> wordList, String word) {
        for (int i = 0; i < wordList.size(); i++) {
            if (word.equals(wordList.get(i))) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取单词列表
     * 2019年1月22日21:32:56 取bitcoinj的助记词表
     *
     * @param type
     * @return
     */
    public static List<String> getWordList(int type) {
        List<String> wordList = null;
        if (type == 1) {
            try {
                wordList = getMonemoic(openChineseWords(), BIP39_ENGLISH_SHA256, SEED_CHINESE);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            try {
                wordList = getMonemoic(openDefaultWords(), BIP39_ENGLISH_SHA256, 0);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return wordList;
    }

    public static InputStream openChineseWords() throws IOException {
        InputStream stream = MnemonicCode.class.getResourceAsStream("mnemonic/wordlist/chinese_simplified.txt");
        if (stream == null) {
            throw new FileNotFoundException("mnemonic/wordlist/chinese_simplified.txt");
        } else {
            return stream;
        }
    }

    public static InputStream openDefaultWords() throws IOException {
        InputStream stream = MnemonicCode.class.getResourceAsStream("mnemonic/wordlist/english.txt");
        if (stream == null) {
            throw new FileNotFoundException("mnemonic/wordlist/english.txt");
        } else {
            return stream;
        }
    }

    @SuppressWarnings("unchecked")
    public static ArrayList<String> getMonemoic(InputStream wordstream, String wordListDigest, int type)
            throws IOException, IllegalArgumentException {
        BufferedReader br = new BufferedReader(new InputStreamReader(wordstream, "UTF-8"));
        ArrayList<String> wordList = new ArrayList(2048);
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException var8) {
            throw new RuntimeException(var8);
        }

        String word;
        while ((word = br.readLine()) != null) {
            md.update(word.getBytes());
            wordList.add(word);
        }

        br.close();
        if (wordList.size() != 2048) {
            throw new IllegalArgumentException("input stream did not contain 2048 words");
        } else {
            if (type == SEED_CHINESE) {

            } else {// 英文参照MnemonicCode做了校验
                if (wordListDigest != null) {
                    byte[] digest = md.digest();
                    String hexdigest = Utils.HEX.encode(digest);
                    if (!hexdigest.equals(wordListDigest)) {
                        throw new IllegalArgumentException("wordlist digest mismatch");
                    }
                }
            }
        }
        return wordList;
    }

    private static String mnemonicListToString(List<String> wordList) {
        StringBuilder sb = new StringBuilder();
        for (String s : wordList) {
            sb.append(s).append(" ");
        }
        return sb.toString().trim();

    }

    /**
     * 将助记词字符串转换为列表
     *
     * @param mnemonicString 助记词字符串，以空格分隔的单词
     * @return 助记词列表
     */
    private static List<String> mnemonicStringToList(String mnemonicString) {
        String[] split = mnemonicString.split(" ");
        return Arrays.asList(split);
    }

    /**
     * 根据种子列表生成熵
     *
     * @param seed
     * @return
     */
    public static byte[] getseedEntropy(List<String> seedText) {
        List<String> seed;
        String mnemonic = mnemonicListToString(seedText);

        try {
            if (isChinese(mnemonic)) {// 如果是中文，就转换
                seed = mnemonicStringToList(seedToEnglish(mnemonic));
            } else if (isNumber(mnemonic)) {
                seed = mnemonicStringToList(seedNumToEnglishShow(mnemonic));
            } else {
                seed = seedText;
            }
            MnemonicCode mnemonicCode = new MnemonicCode();
            byte[] bytes = mnemonicCode.toEntropy(seed);
            String string = Hex.toHexString(bytes);
            MessageDigest instance = MessageDigest.getInstance("sha-256");
            byte[] digest = instance.digest(bytes);
            return concat(bytes, new byte[] { (byte) (digest[0] & 0xf0) });
        } catch (Exception e) {
            e.fillInStackTrace();
            return null;
        }
    }

    /**
     * 拼接数组
     *
     * @param a 原有数据
     * @param b 新数据
     * @return
     */
    public static byte[] concat(byte[] a, byte[] b) {
        byte[] c = new byte[a.length + b.length];
        System.arraycopy(a, 0, c, 0, a.length);
        System.arraycopy(b, 0, c, a.length, b.length);
        return c;
    }

}
