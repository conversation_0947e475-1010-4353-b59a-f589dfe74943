package com.coldlar.utils


import com.coldlar.coin.CoinType
import com.coldlar.coin.act.ACTAddress
import com.coldlar.coin.act.RIPEMD160
import com.coldlar.core.CommonUtil
import com.coldlar.core.wallet.AbstractAddress
import com.coldlar.core.wallet.families.bitcoin.BitAddress
import com.coldlar.core.wallet.families.ethereum.EthAddress
import com.coldlar.core.wallet.families.ethereum.RippleAddress
import com.ripple.config.RippleConfig
import com.ripple.utils.HashUtils
import org.bitcoinj.core.Base58
import org.bitcoinj.crypto.DeterministicKey
import org.bitcoinj.crypto.HDKeyDerivation
import org.spongycastle.util.Arrays
import org.spongycastle.util.encoders.Hex


/**
 * @Date：2018/10/27 on 14:43
 * desc:
 * @author11447
 */
class PubkeyAddressUtil {


    companion object {
        /**
         * 得到一个地址
         */
        fun getAddressFromPub(coinType: CoinType, xpub: String, index: Int): String {
            val decodeByteArray = Base58.decodeChecked(xpub)
            val chain = ByteArray(32)
            val pubkey = ByteArray(33)
            System.arraycopy(decodeByteArray, 13, chain, 0, 32)
            System.arraycopy(decodeByteArray, 45, pubkey, 0, 33)

            return getAddrFromPub(coinType, pubkey, chain, index)
        }

        /**
         * 根据拓展公钥获取地址
         */
        private fun getAddrFromPub(coinType: CoinType, pubkey: ByteArray?, chain: ByteArray?, index: Int): String {
            val masterPubkey = HDKeyDerivation.createMasterPubKeyFromBytes(pubkey, chain)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)// bip44
            return BitAddress.from(coinType, addressPubkey).toString()
        }

        private fun getPubkeyFromxpub(coinType: CoinType, pubkey: ByteArray?, chain: ByteArray?, index: Int): String {
            val masterPubkey = HDKeyDerivation.createMasterPubKeyFromBytes(pubkey, chain)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)// bip44
            return Hex.toHexString(addressPubkey.pubKey)
        }

        /**
         * 比特系列根据拓展公钥回去公钥
         */
         fun getPubkeyFromxpub( xpub: String,  index: Int): String {
            val decodeByteArray = Base58.decodeChecked(xpub)
            val chain = ByteArray(32)
            val pubkey = ByteArray(33)
            System.arraycopy(decodeByteArray, 13, chain, 0, 32)
            System.arraycopy(decodeByteArray, 45, pubkey, 0, 33)

            val masterPubkey = HDKeyDerivation.createMasterPubKeyFromBytes(pubkey, chain)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)
            return Hex.toHexString(addressPubkey.pubKey)
        }

        /**
         * 根据拓展公钥的到公钥
         */
        @Throws(Exception::class)
        fun getAddressPubkey(xpub: String, childNumber: Int): String {
            val masterPubkey = DeterministicKey.deserializeB58(xpub)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, childNumber)// bip44
            return Hex.toHexString(addressPubkey.pubKey)

        }

//        /**
//         * 返回地址和公钥
//         */
//        fun getAddressesFromPub(coinType: CoinType, xpub: String, count: Int): ArrayList<AddressModel> {
//            val addrList = ArrayList<AddressModel>()
//            val decodeByteArray = Base58.decodeChecked(xpub)
//            val chain = ByteArray(32)
//            val pubkey = ByteArray(33)
//            System.arraycopy(decodeByteArray, 13, chain, 0, 32)
//            System.arraycopy(decodeByteArray, 45, pubkey, 0, 33)
//            repeat(count) {
//                val model = AddressModel();
//                model.address = getAddrFromPub(coinType, pubkey, chain, it);
//                model.addressPub = getPubkeyFromxpub(coinType, pubkey, chain, it);
//                addrList.add(model)
//            }
//            return addrList
//        }

        fun getEthAddressFromPub(coinType: CoinType, xpub: String, index: Int): EthAddress {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)// bip44
            val arrayOfByte = addressPubkey.getPubKeyPoint().getEncoded(false)
            val addressByte = CommonUtil.sha3omit12(Arrays.copyOfRange(arrayOfByte, 1, arrayOfByte.size))
            return EthAddress(coinType, Hex.toHexString(addressByte))
        }

        fun getEthAddress(coinType: CoinType, addressPubkey: DeterministicKey, index: Int): EthAddress {
            val arrayOfByte = addressPubkey.getPubKeyPoint().getEncoded(false)
            val addressByte = CommonUtil.sha3omit12(Arrays.copyOfRange(arrayOfByte, 1, arrayOfByte.size))
            return EthAddress(coinType, Hex.toHexString(addressByte))
        }

        fun getEthAddressesFromPub(coinType: CoinType, xpub: String, count: Int): ArrayList<EthAddress> {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addrList = ArrayList<EthAddress>()

            repeat(count) {
                val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, it)// bip44
                val arrayOfByte = addressPubkey.getPubKeyPoint().getEncoded(false)
                val addressByte = CommonUtil.sha3omit12(Arrays.copyOfRange(arrayOfByte, 1, arrayOfByte.size))
                addrList.add(EthAddress(coinType, Hex.toHexString(addressByte)))
            }
            return addrList
        }

        fun getRippleAddressFromPub(coinType: CoinType, xpub: String, index: Int): RippleAddress {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)// bip44
            val rippleAddress = RippleAddress(
                coinType,
                RippleConfig.getB58IdentiferCodecs().encodeAddress(
                    HashUtils.SHA256_RIPEMD160(
                        addressPubkey.pubKeyPoint
                            .getEncoded(true)
                    )
                )
            )
            return rippleAddress
        }

        fun getRippleAddressListFromPub(coinType: CoinType, xpub: String, count: Int): ArrayList<RippleAddress> {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addrList = ArrayList<RippleAddress>()
            repeat(count) {
                val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, it)// bip44
                val rippleAddress = RippleAddress(
                    coinType,
                    RippleConfig.getB58IdentiferCodecs().encodeAddress(
                        HashUtils.SHA256_RIPEMD160(
                            addressPubkey.pubKeyPoint
                                .getEncoded(true)
                        )
                    )
                )
                addrList.add(rippleAddress)
            }
            return addrList
        }

        fun getActAddressFromPub(coinType: CoinType, xpub: String, index: Int): ACTAddress {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, index)// bip44
            val actAddress = ACTAddress(
                RIPEMD160.hash(
                    SHA._512hash(
                        addressPubkey.pubKeyPoint
                            .getEncoded(true)
                    )
                ), ACTAddress.Type.ADDRESS
            )
            return actAddress
        }

        fun getActAddressListFromPub(coinType: CoinType, xpub: String, count: Int): ArrayList<ACTAddress> {
            val masterPubkey = DeterministicKey.deserializeB58(null, xpub)// 主密钥
            val addrList = ArrayList<ACTAddress>()
            repeat(count) {
                val addressPubkey = HDKeyDerivation.deriveChildKey(masterPubkey, it)// bip44
                val actAddress = ACTAddress(
                    RIPEMD160.hash(
                        SHA._512hash(
                            addressPubkey.pubKeyPoint
                                .getEncoded(true)
                        )
                    ), ACTAddress.Type.ADDRESS
                )
                addrList.add(actAddress)
            }
            return addrList
        }

//        * 2020年7月20日17:16:43调整bitcoinj时注释，后期如果还需要支持重写，后台无任何真实使用记录，可去掉
//        * coin-review-todotag
//        fun getNeoAddressFromPub(coinType: CoinType, xpub: String, index: Int): AbstractAddress {
//            val masterPubkey = NeoDeterministicKey.deserializeB58(xpub)// 主密钥
//            val addressPubkey = NeoHDKeyDerivation.deriveChildKey(masterPubkey, 0)// bip44
//            val pubKeyInfo = addressPubkey.pubKeyPoint.getEncoded(true)
//            val address = generateNeoAddress(coinType, pubKeyInfo)
//            return address
//        }

//        * 2020年7月20日17:16:43调整bitcoinj时注释，后期如果还需要支持重写，后台无任何真实使用记录，可去掉
//        * coin-review-todotag
        fun getNeoAddressListFromPub(coinType: CoinType, xpub: String, count: Int): ArrayList<AbstractAddress> {
//            val masterPubkey = NeoDeterministicKey.deserializeB58(xpub)// 主密钥
            val addrList = ArrayList<AbstractAddress>()

            return addrList
        }



    }


}