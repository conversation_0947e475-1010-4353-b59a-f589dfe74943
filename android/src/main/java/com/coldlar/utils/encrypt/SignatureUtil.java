package com.coldlar.utils.encrypt;

import java.util.Iterator;
import java.util.Map;
import java.util.SortedMap;

/**
 * Created by mark on 17/2/28.
 */

public class SignatureUtil {

    public static String SIGN_TYPE = "MD5";
    
    /**
     * @param params   请求参数
     * @param signType 签名类型
     * @return
     * @Description: ：sign签名
     */
    public static String sign(SortedMap<String, String> params, String signType) {
        StringBuffer sb = new StringBuffer();
        params = paraFilter(params);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (null != value && !"".equals(value)) {
                if (!params.lastKey().equals(key)) {
                    sb.append(key + "=" + value + "&");
                } else {
                    sb.append(key + "=" + value);
                }
            }
        }
        String sign = "";
        if (signType.equalsIgnoreCase("MD5")) {
//            sign = MD5Util.md5(sb.toString()).toUpperCase();
            sign = MD5Util.MD5Encode(sb.toString(), "UTF-8");
        } else if (signType.equalsIgnoreCase("SHA1")) {
            sign = SHA1Util.getSha1(sb.toString());
        }
        return sign;
    }

    /**
     * 除去数组中的空值和签名参数
     *
     * @param params 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    public static SortedMap<String, String> paraFilter(SortedMap<String, String> params) {
        Iterator<SortedMap.Entry<String, String>> it = params.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            String key = entry.getKey();
            String value = entry.getValue();
            if (value == null || value.equals("")
                    || key.equalsIgnoreCase("sign")) {
                it.remove(); // 删除value为空的和key=sign的
            }
        }
        return params;
    }
}