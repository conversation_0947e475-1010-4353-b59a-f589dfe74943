package com.coldlar.utils.encrypt;

import java.security.MessageDigest;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

public class SHA256Util {

  public static final String MD5 = "MD5";
  public static final String SHA1 = "sha1";
  public static final String SHA256 = "SHA-256";
  private static final char[] HEX_DIGITS = {
      '0', '1', '2', '3', '4', '5',
      '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

  /**
   * encode string
   *
   * @return String
   */
  public static String encode(String algorithm, String str) {
    if (str == null) {
      return null;
    }

    try {
      MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
      messageDigest.update(str.getBytes());
      return getFormattedText(messageDigest.digest());
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Takes the raw bytes from the digest and formats them correct.
   *
   * @param bytes the raw bytes from the digest.
   * @return the formatted bytes.
   */
  private static String getFormattedText(byte[] bytes) {
    int len = bytes.length;
    StringBuilder buf = new StringBuilder(len * 2);

    // 把密文转换成十六进制的字符串形式
    for (int j = 0; j < len; j++) {
      buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
      buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
    }
    return buf.toString();
  }

  public static String buildStrToSign(Map<String, String> paramMap, String appKey) {
    StringBuilder sigStrBuffer = new StringBuilder();
    // 首先对paramMap的key进行升序排序
    Map<String, String> sortMap = new TreeMap<String, String>(paramMap);
    // 拼接除了sign参数的所有字符串
    for (Entry<String, String> entry : sortMap.entrySet()) {
      String key = entry.getKey();
      String value = entry.getValue();
      if (!"sign".equals(key)) {
        sigStrBuffer.append(value);
      }
    }
    return sigStrBuffer.toString() + appKey;
  }

  public static boolean signVerify(Map<String, String> paramMap, String appKey) {
    String sigStr = buildStrToSign(paramMap, appKey);
    String serverSig = SHA256Util.encode(SHA256Util.SHA256, sigStr);
    return serverSig.equals(paramMap.get("sign"));
  }

  public static String sign(Map<String, String> paramMap) {
    // 首先对paramMap的key进行升序排序
    String appKey = paramMap.get("appKey");
    String sigStr = buildStrToSign(paramMap, appKey);
    return SHA256Util.encode(SHA256Util.SHA256, sigStr);
  }

  public static String sign(Map<String, String> paramMap, String appKey) {
    // 首先对paramMap的key进行升序排序
    String sigStr = buildStrToSign(paramMap, appKey);
    return SHA256Util.encode(SHA256Util.SHA256, sigStr);
  }

}
