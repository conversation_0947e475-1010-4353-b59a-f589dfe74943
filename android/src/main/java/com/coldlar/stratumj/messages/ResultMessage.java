package com.coldlar.stratumj.messages;

import com.coldlar.core.Constant;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
public class ResultMessage extends BaseMessage {

    private String jsonResult;

    protected ResultMessage(String source) throws JSONException {
        super(source);
    }

    public ResultMessage() {
        setId(-1);
    }

    public static ResultMessage fromJson(String json) throws JSONException {
        return new ResultMessage(json);
    }

    /**
     * 设置需要返回的json
     *
     * @param json
     * @return
     */
    public static ResultMessage setJsonResult(String json) {
        ResultMessage baseMessage = null;
        try {
            baseMessage = new ResultMessage(json);
        } catch (JSONException e) {
            baseMessage = new ResultMessage();
        }

        try {
            JSONArray array = new JSONArray(json);
            for (int i = 0; i < array.length(); i++) {
                JSONObject object = array.getJSONObject(i);
                String s = object.toString();
                if (object.has("error")) {
                    JSONObject jsonObject = object.optJSONObject("error");
                    if (jsonObject.has("message")) {
                        //找到固定的错误方法提示
                        if (Constant.ERROR_MESSAGE.equals(jsonObject.optString("message"))) {
                            //响应体设置ID,很据ID找到请求实体并返回数据
                            baseMessage.setId(object.optLong("id"));
                            //移除固定错误
                            array.remove(i);
                            break;
                        }
                    }
                }
            }

            baseMessage.setResultJson(array.toString());
            return baseMessage;

        } catch (JSONException e) {

            return  null;
        }



//        return null;

    }


    public JSONArray getResult() {
        if (has("result")) {
            if (opt("result") instanceof JSONArray) {
                try {
                    return getJSONArray("result");
                } catch (JSONException e) {
                    // Should not happen
                    throw new RuntimeException(e);
                }
            } else {
                JSONArray result = new JSONArray();
                try {
                    result.put(get("result"));
                } catch (JSONException e) {
                    // Should not happen
                    throw new RuntimeException(e);
                }
                return result;
            }
        } else {
            return new JSONArray();
        }
    }

    public String getResultJson() {
        return jsonResult;
    }

    public void setResultJson(String result) {
        this.jsonResult = result;
    }

    @Override
    public String toString() {
        if (!has("result")) {
            return jsonResult;
        }
        return super.toString();
    }
}
