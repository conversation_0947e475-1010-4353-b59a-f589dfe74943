import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomSheetDialog extends StatelessWidget {
  const BottomSheetDialog({
    this.bodyWidget,
    this.title,
    this.fullScreenBodyWidget,
    this.disableBack = true,
    this.onCancel,
    this.onBackPressed,
    this.hideHeader = false,
    this.bottomWidget,
    this.paddingBottom,
    this.padding,
    this.customBodyWidget,
    this.customHeadWidget,
    this.bgColor,
    super.key,
  });

  final String? title;

  ///禁用Android back键，默认不禁用，可动态控制
  final bool? disableBack;

  ///自适应高度的bodyWidget，自带滑动控件
  final Widget? bodyWidget;

  final Widget? customBodyWidget; // 自适应高度的body完全自定义

  ///如果需要全屏弹窗或固定高度弹窗，bodyWidget换成这个，isScrollControlled设置为true，如果需要固定高度，修改 如 maxHeight = 300  默认高度为全屏
  final Widget? fullScreenBodyWidget;

  ///右侧关闭
  final VoidCallback? onCancel;

  ///Android Back键监听
  final VoidCallback? onBackPressed;

  ///隐藏header
  final bool? hideHeader;

  ///底部按钮
  final Widget? bottomWidget;

  ///自定义距底高度，不传默认是size.paddingBottom,可以传0默认没有高度
  final double? paddingBottom;

  ///自定义padding 默认 Padding(padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16),

  final EdgeInsetsGeometry? padding;

  /// 自定义顶部
  final Widget? customHeadWidget;

  ///bgColor
  final Color? bgColor;

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: disableBack ?? true,
        onPopInvokedWithResult: (didPop, result) async {
          if (result != null) {
            return; // 如果 result 不为空，直接返回
          }
          if (didPop) {
            return;
          }

          if (onBackPressed != null) {
            onBackPressed!();
          }
        },
        child: KeyboardDismissWidget(
            child: Container(
                width: Get.width,
                constraints: BoxConstraints(
                    minHeight: fullScreenBodyWidget == null
                        ? 0
                        : Get.height - ScreenUtil().statusBarHeight,
                    maxHeight: Get.height - ScreenUtil().statusBarHeight),
                padding: EdgeInsets.only(
                    bottom: paddingBottom ??
                        (MediaQuery.of(context).padding.bottom == 0
                            ? Get.setHeight(15)
                            : ScreenUtil().bottomBarHeight)),
                decoration: BoxDecoration(
                    color: bgColor ?? Get.theme.bgColor,
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(Get.setRadius(16)),
                        topLeft: Radius.circular(Get.setRadius(16)))),
                child: Stack(
                  children: [
                    _headWidget(context),
                    _contentWidget(context),
                    if (bottomWidget != null)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: bottomWidget!,
                      ),
                  ],
                ))));
  }

  Positioned _headWidget(BuildContext context) => Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: hideHeader!
          ? const SizedBox.shrink()
          : customHeadWidget ??
              Padding(
                padding: EdgeInsets.symmetric(
                    vertical: Get.setHeight(15.5),
                    horizontal: Get.setHeight(16)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
                        child: Text(title ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: Get.theme.textPrimary,
                                fontFamily: Get.setFontFamily(),
                                fontSize: Get.setFontSize(18),
                                fontWeight: FontWeightX.medium)),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (onCancel != null) {
                          onCancel!();
                        } else {
                          Get.back();
                        }
                      },
                      child: Container(
                        color: Get.theme.bgColor,
                        child: ImageWidget(
                          assetUrl: "icon_exit",
                          width: Get.setWidth(24),
                          height: Get.setHeight(24),
                        ),
                      ),
                    )
                  ],
                ),
              ));

  Container _contentWidget(BuildContext context) => Container(
      margin: EdgeInsets.only(
          top: hideHeader! ? 0 : Get.setHeight(54),
          bottom: bottomWidget == null
              ? 0
              : (26 +
                  (ScreenUtil().bottomBarHeight == 0
                      ? Get.setHeight(15)
                      : ScreenUtil().bottomBarHeight))),
      child: fullScreenBodyWidget ??
          customBodyWidget ??
          SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                bodyWidget ?? const SizedBox.shrink(),
              ])));
}
