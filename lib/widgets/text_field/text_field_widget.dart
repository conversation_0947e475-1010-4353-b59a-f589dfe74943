/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-31 11:08:23
 * @LastEditTime: 2025-03-17 10:34:39
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text_field/text_number_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TextFieldWidget extends StatefulWidget {
  const TextFieldWidget({
    super.key,
    this.readOnly = false,
    this.controller,
    this.errorBorder,
    this.autofocus,
    this.keyboardType,
    this.showCounterText = false,
    this.showClear = false,
    this.minLines,
    this.maxLength,
    this.fillColor,
    this.enabledBorder,
    this.focusedBorder,
    this.maxLines,
    this.contentPadding,
    this.prefixIconPadding,
    this.suffixIconPadding,
    this.hintStyle,
    this.textStyle,
    this.onValueChanged,
    this.hintText,
    this.focusNode,
    this.textInputAction,
    this.textInputType,
    this.errorText,
    this.validator,
    this.onClear,
    this.onTap,
    this.radius,
    this.onFieldSubmitted,
    this.showEyes = false,
    this.obscureText = false,
    this.suffixIcon,
    this.prefixIcon,
    this.scale,
    this.border,
    this.textAlign,
    this.onFocusNodeChanged,
    this.isNumberInputFormatter = true,
    this.showCursor,
  });

  final String? hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onValueChanged;
  final GestureTapCallback? onTap;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextInputType? textInputType;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onFieldSubmitted;
  final String? errorText;
  final VoidCallback? onClear;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? prefixIconPadding;
  final EdgeInsetsGeometry? suffixIconPadding;

  final TextInputType? keyboardType;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final InputBorder? errorBorder;
  final Color? fillColor;
  final int? maxLength;
  final int? minLines;
  final int? maxLines;
  final bool? showCounterText;
  final bool? autofocus;
  final bool? readOnly; //不可编辑
  final double? radius;
  final bool? showClear;
  final bool showEyes;
  final bool obscureText;
  // 小数位精度 仅当 textInputType = number 时有效 是否能输入小数取决于 decimal
  final int? scale;
  // 是否需要对数字键盘输入验证 默认为true
  final bool isNumberInputFormatter;

  /// 自定义suffixIcon(showClear showEyes失效)
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final InputBorder? border;
  final TextAlign? textAlign;
  final Function(FocusNode focusNode)? onFocusNodeChanged;
  final bool? showCursor;

  @override
  TextFieldWidgetState createState() => TextFieldWidgetState();
}

class TextFieldWidgetState extends State<TextFieldWidget> {
  TextEditingController? controller;
  FocusNode? focusNode;
  bool isHasfocus = false;
  bool hasValue = false;
  bool isObscure = false;

  List<TextInputFormatter>? get _inputFormatters {
    if (widget.keyboardType != null) {
      if (widget.keyboardType!.index == 2 && widget.isNumberInputFormatter) {
        return TextNumberInputFormatter.numberInputFormatter(
            scale: widget.scale, inputType: widget.keyboardType!);
      }
    }
    return null;
  }

  @override
  void initState() {
    controller = widget.controller ?? TextEditingController();
    focusNode = widget.focusNode ?? FocusNode();
    isObscure = widget.obscureText;
    super.initState();

    controller?.addListener(() {
      hasValue = controller!.text.isNotEmpty;
      if (mounted) {
        setState(() {});
      }
      // 每次文本变化时，你可以在这里做出反应。
    });

    if (focusNode != null) {
      focusNode!.addListener(() {
        setState(() {
          if (focusNode!.hasFocus) {
            isHasfocus = true;
          } else {
            isHasfocus = false;
          }
        });
        if (widget.onFocusNodeChanged != null) {
          widget.onFocusNodeChanged!(focusNode!);
        }
      });
    }
  }

  @override
  void dispose() {
    // 默认没有传入controller,需要内部释放
    if (widget.controller == null) {
      controller?.dispose();
    }
    if (widget.focusNode == null) {
      focusNode?.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
        onTap: widget.onTap,
        controller: controller,
        focusNode: focusNode,
        obscureText: isObscure,
        showCursor: widget.showCursor,
        style: widget.textStyle ??
            TextStyle(
                fontSize: Get.setFontSize(16),
                color: Get.theme.textPrimary,
                fontFamily: Get.setFontFamily(),
                fontWeight: FontWeightX.medium),
        textAlign: widget.textAlign ?? TextAlign.start,
        maxLines: widget.maxLines ?? 1,
        maxLength: widget.maxLength,
        minLines: widget.minLines ?? 1,
        onFieldSubmitted: widget.onFieldSubmitted,
        cursorColor: Get.theme.primary,
        validator: widget.validator,
        autofocus: widget.autofocus ?? false,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        textInputAction: widget.textInputAction,
        keyboardType: widget.keyboardType ?? TextInputType.text,
        readOnly: widget.readOnly ?? false,
        inputFormatters: _inputFormatters,
        onChanged: (text) {
          if (widget.onValueChanged != null) {
            widget.onValueChanged!(text);
          }
        },
        decoration: InputDecoration(
          prefixIcon: (widget.prefixIcon != null)
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: widget.prefixIconPadding ??
                          EdgeInsets.symmetric(
                              horizontal: Get.setPaddingSize(12)),
                      child: widget.prefixIcon,
                    )
                  ],
                )
              : null,
          suffixIcon: (widget.suffixIcon != null)
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: widget.suffixIconPadding ??
                          EdgeInsets.symmetric(
                              horizontal: Get.setPaddingSize(12)),
                      child: widget.suffixIcon!,
                    )
                  ],
                )
              : _suffixIcon(),
          border: widget.border ??
              OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(widget.radius ?? Get.setRadius(12)),
                borderSide: BorderSide(
                  color: Get.theme.colorD3D3D3,
                  width: Get.setWidth(1),
                ),
              ),
          enabledBorder: widget.enabledBorder ??
              OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                    widget.radius ?? widget.radius ?? Get.setRadius(12)),
                borderSide: BorderSide(
                  color: Get.theme.colorD3D3D3,
                  width: Get.setWidth(1),
                ),
              ),
          focusedBorder: widget.focusedBorder ??
              OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Get.theme.primary,
                    width: Get.setWidth(1),
                  ),
                  borderRadius: BorderRadius.circular(
                      widget.radius ?? Get.setRadius(12))),
          errorBorder: widget.errorBorder ??
              OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Get.theme.colorScheme.error,
                    width: Get.setWidth(1),
                  ),
                  borderRadius: BorderRadius.circular(
                      widget.radius ?? Get.setRadius(12))),
          contentPadding:
              widget.contentPadding ?? EdgeInsets.all(Get.setWidth(12)),
          hintText: widget.hintText ?? "",
          hintStyle: widget.hintStyle ??
              TextStyle(
                  fontSize: Get.setFontSize(16),
                  color: Get.theme.textTertiary,
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular),
          errorText: widget.errorText,
          errorStyle: TextStyle(
              fontSize: Get.setFontSize(12),
              fontFamily: Get.setFontFamily(),
              color: Get.theme.colorScheme.error,
              fontWeight: FontWeightX.regular),
          counterText: widget.showCounterText! ? null : "",
          fillColor: widget.fillColor ?? Get.theme.bgColor,
          filled: true,
        ));
  }

  Row? _suffixIcon() {
    return (widget.showClear == true || widget.showEyes == true)
        ? Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: _suffixIconChildren(),
          )
        : null;
  }

  List<Widget> _suffixIconChildren() {
    List<Widget> children = [];

    final EdgeInsetsGeometry padding1;
    final EdgeInsetsGeometry padding2;
    if (widget.showEyes == true && widget.showClear == true) {
      padding1 =
          EdgeInsets.only(left: Get.setWidth(12), right: Get.setWidth(6));
      padding2 =
          EdgeInsets.only(right: Get.setWidth(12), left: Get.setWidth(6));
    } else {
      padding1 = EdgeInsets.symmetric(horizontal: Get.setWidth(12));
      padding2 = EdgeInsets.symmetric(horizontal: Get.setWidth(12));
    }

    if (widget.showClear == true) {
      children.add(Visibility(
          visible: isHasfocus && hasValue,
          child: GestureDetector(
            child: Padding(
                padding: padding1,
                child: ImageWidget(
                  assetUrl: 'icon_clear_et',
                  width: Get.setWidth(16),
                  height: Get.setHeight(16),
                )),
            onTap: () {
              controller!.text = '';
              if (widget.onClear != null) {
                widget.onClear!();
              } else {
                widget.onValueChanged!('');
              }
            },
          )));
    }

    if (widget.showEyes == true) {
      children.add(Visibility(
          visible: widget.showEyes,
          child: GestureDetector(
            child: Padding(
                padding: padding2,
                child: ImageWidget(
                  assetUrl: isObscure ? 'icon_eye_close' : 'icon_eye_open',
                  width: Get.setWidth(20),
                  height: Get.setHeight(20),
                )),
            onTap: () {
              isObscure = !isObscure;
              setState(() {});
            },
          )));
    }

    return children;
  }
}
