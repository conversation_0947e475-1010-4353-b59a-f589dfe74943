/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-12-16 14:32:40
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class DappLogoWidget extends StatelessWidget {
  final String? dappUrl;
  final DappModels? dappModel;
  final bool showName;
  const DappLogoWidget(
      {super.key,
      required this.dappUrl,
      this.dappModel,
      this.showName = false});

  @override
  Widget build(BuildContext context) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          dappModel != null && dappModel?.dAppLogo != null
              ? ImageWidget(
                  imageUrl: dappModel?.dAppLogo,
                  width: Get.setImageSize(48),
                  height: Get.setImageSize(48),
                  radius: Get.setRadius(12),
                )
              : ImageWidget(
                  assetUrl: "icon_dapp_default_logo",
                  width: Get.setImageSize(48),
                  height: Get.setImageSize(48),
                ),
          const SizedBox(height: 8),
          Text(
              (dappModel != null && dappModel?.dAppName! != null)
                  ? dappModel!.dAppName!
                  : "",
              maxLines: 1,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(18),
                fontFamily: Get.setFontFamily(),
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeightX.medium,
              )),
          Visibility(
            visible: !Get.isEmptyString(dappUrl),
            child: Text(
              dappUrl ?? "",
              maxLines: 5,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setFontFamily(),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Visibility(
            visible: showName,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                  (dappModel != null && dappModel?.abs! != null)
                      ? dappModel!.abs!
                      : (dappModel != null && dappModel?.dAppName! != null)
                          ? dappModel!.dAppName!
                          : "",
                  maxLines: 1,
                  style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setFontFamily(),
                    overflow: TextOverflow.ellipsis,
                    fontWeight: FontWeightX.regular,
                  )),
            ),
          ),
        ]);
  }
}
