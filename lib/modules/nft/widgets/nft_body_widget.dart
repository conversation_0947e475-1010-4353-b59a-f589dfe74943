/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-13 17:22:30
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/sliver/auto_sliver_appbar_delegate.dart';
import 'package:coinbag/widgets/tab_bar/base_tab_bar.dart';
import 'package:flutter/material.dart';

class NftBodyWidget extends BaseStatelessWidget<NftController> {
  const NftBodyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: OverScrollBehavior(),
      child: NestedScrollView(
          controller: controller.scrollController,
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverPersistentHeader(
                pinned: true,
                floating: false,
                delegate: AutoSliverPersistentHeaderDelegate(baseTabBar(
                  controller: controller.tabController,
                  isScrollable: true,
                  hideIndicator: true,
                  hideDivider: true,
                  onTab: (index) {
                    controller.selectedPage.value = index;
                    controller.pagerController.jumpToPage(index);
                  },
                  tabs: [
                    Tab(
                      child: Padding(
                        padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
                        child: Obx(() => ImageWidget(
                            assetUrl: controller.selectedPage.value == 0
                                ? 'nft_asset_selected'
                                : 'nft_asset',
                            width: Get.setImageSize(26),
                            height: Get.setImageSize(32))),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
                      child: Obx(() => ImageWidget(
                          assetUrl: controller.selectedPage.value == 1
                              ? 'nft_collection_selected'
                              : 'nft_collection',
                          width: Get.setImageSize(26),
                          height: Get.setImageSize(32))),
                    ),
                  ],
                )),
              ),
            ];
          },
          body: PageView.builder(
              itemCount: 2,
              onPageChanged: (index) {
                controller.tabController.animateTo(index);
                controller.selectedPage.value = index;
              },
              controller: controller.pagerController,
              itemBuilder: (_, int index) => controller.pagerList[index])),
    );
  }
}
