/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-10 09:44:24
 * @LastEditTime: 2024-09-19 15:49:36
 */
import 'dart:ui';

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/res/strings.en.dart';
import 'package:coinbag/res/strings.ja_jp.dart';
import 'package:coinbag/res/strings.zh.dart';
import 'package:coinbag/res/strings.zh_hk.dart';

///使用Get配置语言环境
///使用Get.updateLocale(locale);即可更新
class TranslationsMessage extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        'zh_CN': localizedValueZH,
        'zh_HK': localizedValueZHHK,
        'en_US': localizedValueEN,
        'ja_JP': localizedValueJAJP,
      };
}

///新增语言要在此处手动增加
class LanguageSource {
  static List<LanguageModel> get languageList => [
        LanguageModel('简体中文', 'zh', 'CN',
            imagePath: 'language_china', requestCountry: "CN", id: 0),
        LanguageModel('繁體中文(香港)', 'zh', 'HK',
            imagePath: 'language_china', requestCountry: "CN", id: 1),
        LanguageModel('English', 'en', 'US',
            imagePath: 'language_usa', requestCountry: "EN", id: 2),
        LanguageModel('日本語', 'ja', 'JP',
            imagePath: 'language_japan', requestCountry: "ja", id: 3),
      ];
}

class LocaleController {
  ///Flutter中支持的系统的语言
  static final Iterable<Locale> supportedLocales = [
    const Locale('en', "US"), //英语
    const Locale('ja', 'JP'), //日语
    const Locale('vi', 'VI'), //越南语
    const Locale('ko', 'KR'), //韩语
    const Locale('zh', "CN"), //中文
    const Locale('zh', "TW"), //繁体
    const Locale('zh', "HK"), //繁体
    const Locale('tr', "TR"), //土耳其语
    const Locale('es', "LA"), //西班牙语(拉丁美洲)
    const Locale('id', "ID"), //印尼语
    const Locale('hi', "IN"), //印地语
    const Locale('ar', "AE"), //阿拉伯语
    const Locale('pt', "PT"), //葡萄牙语
    const Locale('th', "TH"), //泰语
    const Locale('fr', "FR"), //法语
    const Locale('ru', "RU"), //俄语
  ];

  ///更新语言
  static void updateLocale(LanguageModel language) {
    Get.updateLocale(Locale(language.language, language.country));
  }

  ///获取当前存储的默认语言
  static LanguageModel _getDefault() {
    LanguageModel? model = StorageManager.getObject(
        key: StorageKey.language, fromJson: LanguageModel.fromJson);
    if (model == null) {
      // 获取系统当前语言 如未支持则默认为英语
      LanguageModel sysModel = _sysLanguageModel();
      StorageManager.saveObject(key: StorageKey.language, obj: sysModel);
      return sysModel;
    } else {
      return model;
    }
  }

  static LanguageModel _sysLanguageModel() {
    Locale? locale = Get.deviceLocale;
    String code = '';
    if (locale != null) {
      code = locale.languageCode;
      if (locale.scriptCode != null) {
        code = '${code}_${locale.scriptCode}';
      }
    }
    LanguageType sysType = LanguageModel.typeWithCode(
      code: code,
      countryCode: locale?.countryCode,
    );
    return LanguageModel.originModel(sysType);
  }

  /// 当前语言
  static LanguageModel defaultLanguageModel() {
    LanguageModel? model = StorageManager.getObject(
        key: StorageKey.language, fromJson: LanguageModel.fromJson);
    _sysLanguageModel();
    if (model == null) {
      model = LocaleController._getDefault();
      for (LanguageModel obj in LanguageSource.languageList) {
        if (obj.type == model.type) {
          return obj;
        }
      }
      return LanguageModel.model(LanguageType.en);
    }

    return model;
  }

  /// 获取当前语言 code
  static String get getCountryCode {
    LanguageModel model = LocaleController.defaultLanguageModel();
    return model.requestCountry!;
  }

  static Locale locale() {
    LanguageModel model = LocaleController.defaultLanguageModel();
    return Locale(model.language, model.country);
  }

  /// 获取标准CountryCode
  static String getStandardCountryCode() {
    LanguageModel model = LocaleController.defaultLanguageModel();
    return model.country;
  }

  ///是否中国区
  static bool isChinese() {
    LanguageModel model = LocaleController.defaultLanguageModel();
    return model.language == "zh";
  }
}
