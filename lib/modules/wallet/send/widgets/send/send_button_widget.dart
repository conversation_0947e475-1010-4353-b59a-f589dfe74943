/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-22 17:44:32
 * @LastEditTime: 2025-03-10 17:23:28
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class SendButtonWidget extends BaseStatelessWidget<SendController> {
  const SendButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    if (controller.action == WalletAction.fee) {
      return Padding(
        padding: EdgeInsets.only(
            left: Get.setWidth(16),
            right: Get.setWidth(16),
            bottom: Get.getSafetyBottomPadding(),
            top: Get.setHeight(16)),
        child: Text(
          ID.stringBestFeeTitle.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(14),
          ),
        ),
      );
    }
    return Obx(() => Padding(
          padding: EdgeInsets.only(
              left: Get.setWidth(16),
              right: Get.setWidth(16),
              bottom: Get.getSafetyBottomPadding(),
              top: Get.setHeight(16)),
          child: ButtonWidget(
            text: ID.sendButton.tr,
            buttonStatus:
                controller.buttonStatus.value, //controller.buttonStatus,
            buttonSize: ButtonSize.full,
            onPressed: () => controller.sendButtonAction(context),
          ),
        ));
  }
}
