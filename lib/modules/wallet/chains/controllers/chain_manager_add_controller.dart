/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-05 09:22:45
 */

import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/chains/dialog/sol_add_address_dialog.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_add_address_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:drift/drift.dart' as drift;
import 'package:wallet_core/chain/bitcoin/ltc.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';

extension ChainManagerAddController on ChainManagerController {
  void addAddressAction() {
    if (coinType is SolanaChain) {
      Get.showBottomSheet(
        title: ID.stringSynAddressTitle.tr,
        paddingBottom: 0,
        bodyWidget: SolanaAddAddressDialog(onPressed: () async {
          if (Get.isAuthorizeTransaction()) {
            final result = await Get.toNamed(AppRoutes.securityPage,
                arguments: SecurityState.security);
            if (result == true) {
              _solanaSynAddress();
            }
          } else {
            _solanaSynAddress();
          }
        }),
      );
    } else {
      Get.showBottomSheet(
        hideHeader: true,
        paddingBottom: 0,
        bodyWidget: ChainAddAddressWidget(
          controller: this,
          addAddressTap: (addressType, addressLabel) async {
            if (Get.isAuthorizeTransaction()) {
              final result = await Get.toNamed(AppRoutes.securityPage,
                  arguments: SecurityState.security);
              if (result == true) {
                _insertDB(addressType, addressLabel);
              }
            } else {
              _insertDB(addressType, addressLabel);
            }
          },
        ),
      );
    }
  }

  Future<void> _insertDB(int? addressType, String? addressLable) async {
    Get.back();
    await _insertAddressModel(
      addressType: addressType,
      addressLabel: addressLable,
    );
  }

  Future<void> _solanaSynAddress() async {
    Get.back();
    String result = await Get.toScanner(
      arguments: {GetArgumentsKey.scanAction: ScanAction.synAddress},
    );

    try {
      var jsonObject = json.decode(result);

      List data = jsonObject['data'];
      for (var item in data) {
        String? pubData = item['pub_data'];
        String? address = item['address'];
        String? path = item['path'];
        int addressIndex = item['address_index'] ?? 0;
        int? slip44Id = item['bip44Index'];

        CoinType? coinType = CoinBase.getCoinTypeBySlip44Id(slip44Id!);
        if (coinType is! SolanaChain) {
          // 当前地址不对
          Get.showToast(ID.stringSynChainError.tr, toastMode: ToastMode.waring);
          return;
        }

        if (Get.isEmptyString(address)) {
          // 当前地址不对
          Get.showToast(ID.stringSynAddressError.tr,
              toastMode: ToastMode.failed);
          return;
        }

        AddressModel? addressModel =
            await Get.database.addressDao.getAddressModel(
          walletId: walletModel.walletId!,
          deviceId: walletModel.deviceId!,
          chain: coinType.chain,
          address: address!,
        );
        if (addressModel != null) return;

        await _setupAddressMapping(
            address: address, addressIndex: addressIndex);

        String addressLabel =
            '${coinModel!.value.symbol} - ${(addressIndex + 1).toString().padLeft(2, '0')}';

        await _setupAddressTable(
          address: address,
          addressIndex: addressIndex,
          slip44Id: slip44Id,
          publicKey: pubData,
          xpubData: pubData,
          addressPath: path,
          addressLabel: addressLabel,
          segwitType: 44,
          sortedId: addressIndex + 1,
        );

        Get.showToast(ID.stringSynSuccess.tr, toastMode: ToastMode.success);
      }
    } catch (_) {}
  }

  Future<void> _insertAddressModel({
    int? addressType,
    String? addressLabel,
  }) async {
    MonitorModel? monitor = await Get.database.monitorDao.getMonitorModel(
        chain: coinModel?.value.chain,
        walletId: walletModel.walletId!,
        segwitType: addressType);
    int addressIndex = addressList.length;

    /// btc lit 特殊处理
    if ((coinType!.isBitCoinNetWork || coinType is LitecoinChain) &&
        !wallet.isP1P2P3Wallet) {
      addressIndex = 0;
      for (var obj in addressList) {
        if (obj.segwitType == addressType) {
          addressIndex++;
        }
      }
    }

    String? addressPath;
    String? address;
    String? publicKey;
    int segwitType = 44;

    if (wallet.isP1P2P3Wallet) {
      String? publickey = monitor?.publickey;
      String chainBase58 = publickey!.substring(
          0, publickey.lastIndexOf("0") < 0 ? 0 : publickey.lastIndexOf("0"));
      String publicKeyBase58 =
          publickey.substring(publickey.lastIndexOf("0") + 1, publickey.length);
      address = await Get.walletCore.createAddressOrPublicKeyByBase58(
        chain: coinType!.chain,
        chainBase58: chainBase58,
        publicKeyBase58: publicKeyBase58,
        addressIndex: addressIndex,
        isAddress: true,
      );
      Log.logPrint(address);
    } else {
      addressPath = "${monitor?.path}/$addressIndex";
      segwitType = CoinBase.getBipProtocol(monitor!.path!);
      address = await Get.walletCore.createAddress(
          chain: coinType!.chain,
          pubData: monitor.publickey ?? '',
          isXpub: monitor.isXPub ?? true,
          path: monitor.path ?? '',
          index: addressIndex);

      publicKey = await Get.walletCore.getPublicKey(
        chain: coinType?.chain ?? '',
        pubData: monitor.publickey ?? '',
        index: addressIndex,
        path: monitor.path ?? '',
        isXpub: monitor.isXPub ?? true,
      );
    }

    addressIndex = addressIndex + 1;
    String? walletId = walletModel.walletId;
    String? deviceId = walletModel.deviceId;
    if (walletId == null || deviceId == null) {
      return;
    }

    /// 保存mapping表
    await _setupAddressMapping(
      address: address!,
      addressIndex: addressIndex,
    );

    /// 保存地址
    await _setupAddressTable(
      address: address,
      addressIndex: addressIndex,
      slip44Id: monitor?.slip44Id,
      publicKey: publicKey,
      xpubData: monitor?.publickey,
      addressPath: addressPath,
      addressLabel: addressLabel!,
      segwitType: segwitType,
      sortedId: addressList.length + 1,
    );

    Get.showToast(ID.stringAddressSuccess.tr, toastMode: ToastMode.success);
  }

  Future<void> _setupAddressMapping({
    required String address,
    required int addressIndex,
  }) async {
    AddressMappingTableCompanion addressMapping =
        AddressMappingTableCompanion.insert(
      walletId: walletModel.walletId!,
      deviceId: walletModel.deviceId!,
      address: drift.Value(address),
      addressIndex: drift.Value(addressIndex),
      chain: drift.Value(coinType!.chain),
    );

    await Get.database.addressMappingDao.insertBatch([addressMapping]);
  }

  Future<void> _setupAddressTable({
    required String? address,
    required int? addressIndex,
    required int? slip44Id,
    required String? publicKey,
    required String? xpubData,
    required String? addressPath,
    required String addressLabel,
    required int segwitType,
    required int sortedId,
  }) async {
    AddressTableCompanion addressModel = AddressTableCompanion.insert(
      walletId: drift.Value(walletModel.walletId),
      chainCode: drift.Value(coinType?.id),
      chain: drift.Value(coinType?.chain),
      address: drift.Value(address),
      addressIndex: drift.Value(addressIndex),
      isSelected: const drift.Value(false),
      slip44Id: drift.Value(slip44Id),
      publickey: drift.Value(publicKey),
      xpubData: drift.Value(xpubData),
      path: drift.Value(addressPath),
      isUploaded: const drift.Value(false),
      sortedId: drift.Value(sortedId),
      addressLabel: drift.Value(addressLabel),
      walletType: const drift.Value(1),
      segwitType: drift.Value(segwitType),
    );

    await Get.database.addressDao.insertOnConflictUpdate(addressModel);

    updateAddressList();

    _uploadWalletAddress(addressModel);
  }

  void _uploadWalletAddress(AddressTableCompanion addressModel) {
    WalletTableCompanion walletTableCompanion = WalletTableCompanion(
      deviceId: drift.Value(walletModel.deviceId),
      walletType: drift.Value(walletModel.walletType),
      monitorTime: drift.Value(walletModel.monitorTime),
      batchId: drift.Value(walletModel.batchId),
      appVersion: drift.Value(walletModel.appVersion),
      chipVersion: drift.Value(walletModel.chipVersion),
      firmwareVersion: drift.Value(walletModel.firmwareVersion),
      walletId: drift.Value(walletModel.walletId),
      walletName: drift.Value(walletModel.walletName),
      deviceType: drift.Value(walletModel.deviceType),
      isPassphraseWallet: drift.Value(walletModel.isPassphraseWallet),
      checked: const drift.Value(false),
      vipLevel: drift.Value(walletModel.vipLevel),
    );
    Get.appController.uploadWalletAddress([addressModel], walletTableCompanion);
  }
}
