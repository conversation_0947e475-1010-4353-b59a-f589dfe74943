/*
 * @description:钱包首页
 * @Author: wangdognshenng
 * @Date: 2024-01-10 09:45:42
 * @LastEditTime: 2024-11-29 14:59:07
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_chain_widget.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_empty_wallet.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_token_list.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_top_widget.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:flutter/material.dart';

class WalletPage extends BaseStatefulWidget<WalletController> {
  const WalletPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.walletStatus.value.isEmpty) {
        return const HomeEmptyWalletWidget();
      }
      return KeyboardDismissWidget(
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: baseAppBar(
                titleSpacing: -10,
                title: controller.getWaleltName()!,
                centerTitle: false,
                leading: Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
                  child: IconButton(
                    onPressed: () async {
                      KeyboardUtils.hideKeyboard(context);
                      await Get.toNamed(AppRoutes.walletManager);
                    },
                    icon: ImageWidget(
                      assetUrl: 'icon_titlebar_menu',
                      width: Get.setImageSize(28),
                      height: Get.setImageSize(28),
                    ),
                  ),
                ),
                bottom: const HomeTopWidget(),
                actionWidget: <Widget>[
                  Padding(
                      padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
                      child: const HomeChainWidget())
                ]),
            body: const HomeTokenListWidget()),
      );
    });
  }
}
