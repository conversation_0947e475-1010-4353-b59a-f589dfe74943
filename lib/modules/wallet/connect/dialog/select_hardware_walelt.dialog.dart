/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-10-26 18:56:58
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

typedef StringCallback = void Function(String);

class SelectHardWareWalletDialog extends BaseStatelessWidget {
  final StringCallback? onPressed;

  const SelectHardWareWalletDialog({super.key, this.onPressed});

  void showBottomSheet() {
    Get.showBottomSheet(
      title: ID.stirngSelectHardwareWallet.tr,
      bodyWidget: this,
    );
  }

  @override
  build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_item(title: 'Pro 1'), _item(title: 'Pro 2')],
    );
  }

  HighLightInkWell _item({required String title}) {
    return HighLightInkWell(
      onTap: () {
        if (onPressed != null) {
          onPressed!(title); // 触发回调
        }
        Get.back(); // 关闭弹窗
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(16), vertical: Get.setHeight(14)),
        child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
              child: Text(
            title,
            style: stylePrimary_16_m,
          )),
        ]),
      ),
    );
  }
}
