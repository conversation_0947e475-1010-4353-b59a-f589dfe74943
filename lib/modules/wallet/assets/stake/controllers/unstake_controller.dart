/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-15 14:06:20
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/stake/widgets/unstake_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/segment/segment_control_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class UnstakeController extends BaseController<BlockChainService> {
  TronResourceType type = TronResourceType.energy;
  late SegmentedController segmentedController;
  late PageController pagerController;
  ScrollController scrollController = ScrollController();
  final List<Widget> pagerList = [
    const UnstakeWidget<UnstakeEnergyController>(),
    const UnstakeWidget<UnstakeBandwidthController>(),
  ];
  final List<String> tabList = [
    ID.stringEnergy.tr,
    ID.stringBandwidth.tr,
  ];

  String updateId = GetKey.unstakeEnergyId;
  TextEditingController amountController = TextEditingController();

  late CoinModel coinModel;
  late TronAccountInfo accountInfo;
  late AddressModel addressModel;
  late WalletModel walletModel;
  late TransferModel tsModel;

  String balance = '0';

  String errorText = '';
  ButtonStatus buttonStatus = ButtonStatus.disable;

  @override
  void onInit() {
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    accountInfo = Get.arguments[GetArgumentsKey.tronAccountInfo];
    addressModel = Get.arguments[GetArgumentsKey.addressModel];
    walletModel = Get.arguments[GetArgumentsKey.walletModel];
    Wallet wallet = Get.arguments[GetArgumentsKey.wallet];

    tsModel = TransferModel(
      chain: coinModel.chain!,
      type: TransferType.unstake,
      fromAddress: addressModel.address,
      toAddress: addressModel.address,
      wallet: wallet,
      addressPath: addressModel.path,
      coinSymbol: coinModel.symbol,
      coinDecimal: coinModel.chainDecimal,
      walletId: addressModel.walletId,
    )..resourceType = type;

    TronResourceType currentType =
        Get.arguments[GetArgumentsKey.tronResourceType];
    int selectedIndex = 0;
    if (currentType == TronResourceType.energy) {
      selectedIndex = 0;
    } else {
      selectedIndex = 1;
    }
    segmentedController = SegmentedController(selectedIndex: selectedIndex.obs);
    pagerController = PageController(initialPage: selectedIndex);

    super.onInit();

    updateAmount();
  }

  void updateAmount() {
    String amount = amountController.text;
    if (Get.isEmptyString(amount)) {
      amount = '0';
    }
    tsModel.amount = amount;

    if (type == TronResourceType.energy) {
      balance = accountInfo.frozenV2Energy;
    } else {
      balance = accountInfo.frozenV2Bandwidth;
    }

    if (amount.moreThan(balance)) {
      errorText = ID.stringResourceMaxTip.tr;
    } else {
      errorText = '';
    }

    buttonStatus = (amount.equal('0') || !Get.isEmptyString(errorText))
        ? ButtonStatus.disable
        : ButtonStatus.enable;

    update([updateId]);
  }

  void _showPop() {
    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: coinModel,
      addressModel: addressModel,
      mainCoinModel: coinModel,
      onTap: () {
        if (tsModel.wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchSignPage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.walletModel: walletModel,
          });
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.qrType: QRCodeType.transfer
          });
        }
      },
    ).showBottomSheet();
  }

  @override
  void loadData() {
    buttonStatus = ButtonStatus.loading;
    update([updateId]);

    int amount = int.parse(
        tsModel.amount!.mul(pow(10, tsModel.coinDecimal!).toString()));

    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.unstakeTransactionV2,
          requestParams: RequestParams()
              .put(APIConstant.chain, tsModel.chain)
              .put(APIConstant.params, {
            APIConstant.resource: type == TronResourceType.energy
                ? APIConstant.energy
                : APIConstant.bandWidth,
            APIConstant.unfreezeBalance: amount,
            APIConstant.ownerAddress: tsModel.fromAddress,
          }).getRequestBody(),
        )),
        handleSuccess: false,
        handleError: false, (value) async {
      dynamic data = value.data;
      if (data is String) {
        Get.showToast(data, toastMode: ToastMode.failed);
      } else if (data is Map) {
        String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
        Map<String, dynamic>? rawData =
            data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
        String? txID = data[GetArgumentsKey.txID] as String?;
        if (Get.isEmptyString(txID)) {
          Get.isTronResoueceResponseDataValid(data, isShowToast: true);
        } else {
          bool result = await Get.walletCore.verityTronRawDataHex(
                rawDataHex: rawDataHex ?? '',
                fromAddress: tsModel.fromAddress!,
                toAddress: tsModel.toAddress!,
                amount: tsModel.amount!,
                decimal: coinModel.chainDecimal!,
              ) ??
              false;
          if (result == false) {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          } else {
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop();
          }
        }
      }
      buttonStatus = ButtonStatus.enable;
      update([updateId]);
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      buttonStatus = ButtonStatus.enable;
      update([updateId]);
    });
  }

  @override
  void onClose() {
    super.onClose();
    segmentedController.onClose();
    scrollController.dispose();
    pagerController.dispose();
  }
}

class UnstakeEnergyController extends UnstakeController {
  @override
  void onInit() {
    type = TronResourceType.energy;
    updateId = GetKey.unstakeEnergyId;
    super.onInit();
  }
}

class UnstakeBandwidthController extends UnstakeController {
  @override
  void onInit() {
    type = TronResourceType.bandwidth;
    updateId = GetKey.unstakeBandwidthId;
    super.onInit();
  }
}

class UnstakeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UnstakeController());
    Get.lazyPut(() => UnstakeEnergyController());
    Get.lazyPut(() => UnstakeBandwidthController());
  }
}
