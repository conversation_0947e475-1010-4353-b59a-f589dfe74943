/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-09 10:07:12
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:flutter/material.dart';

class ResourceBorderWidget extends StatelessWidget {
  final Widget? headerWidget;
  final Widget? contentWidget;
  const ResourceBorderWidget(
      {super.key, this.headerWidget, this.contentWidget});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Get.theme.colorECECEC, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: headerWidget != null,
            child: headerWidget ?? const SizedBox.shrink(),
          ),
          Visibility(
            visible: contentWidget != null && headerWidget != null,
            child: DividerWidget(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
            ),
          ),
          Visibility(
            visible: contentWidget != null,
            child: contentWidget ?? const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
