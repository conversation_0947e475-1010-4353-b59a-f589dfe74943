// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Permission _$PermissionFromJson(Map<String, dynamic> json) => Permission(
      linkedActions: json['linked_actions'] as List<dynamic>?,
      parent: json['parent'] as String?,
      permName: json['perm_name'] as String?,
      requiredAuth: json['required_auth'] == null
          ? null
          : RequiredAuth.fromJson(
              json['required_auth'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PermissionToJson(Permission instance) =>
    <String, dynamic>{
      'linked_actions': instance.linkedActions,
      'parent': instance.parent,
      'perm_name': instance.permName,
      'required_auth': instance.requiredAuth,
    };
