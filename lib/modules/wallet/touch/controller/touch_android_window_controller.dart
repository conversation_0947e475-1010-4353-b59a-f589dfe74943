/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-08 11:31:24
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_android_window_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';

enum TouchAndroidStatus { start, read, error, success }

class TouchAndroidWindowController extends BaseController {
  @override
  void loadData() {}
  RxString message = ID.stringTouchPhone.tr.obs;
  String imageformat = '.gif';
  TouchAndroidStatus androidStatus = TouchAndroidStatus.start;

  String get statusImage {
    if (androidStatus == TouchAndroidStatus.start) {
      imageformat = '.gif';
      return 'android_touch';
    } else if (androidStatus == TouchAndroidStatus.success) {
      imageformat = '.png';
      return 'touch_success';
    } else if (androidStatus == TouchAndroidStatus.error) {
      imageformat = '.png';
      return 'touch_error';
    } else if (androidStatus == TouchAndroidStatus.read) {
      imageformat = '.gif';
      return 'android_touch';
    }

    imageformat = '.gif';
    return 'android_touch';
  }
}

class AndroidNfcKit {
  static TouchAndroidWindowController? get controller {
    try {
      return Get.find<TouchAndroidWindowController>();
    } catch (_) {
      return null;
    }
  }

  /// 读取 弹窗
  static void read() {
    Get.lazyPut(() => TouchAndroidWindowController());
    Get.showAlertDialog(
      barrierDismissible: false,
      disableBack: false,
      child: const TouchAndroidWindowWidget(),
    );
  }

  /// 更新message
  static void setAlertMessage(String message) {
    if (controller == null) return;
    controller!.message.value = message;
  }

  /// 结束
  static Future<void> finish(
      {String? alertMessage,
      String? errorMessage,
      bool isTimeOut = false}) async {
    if (controller == null) return;
    if (!Get.isEmptyString(alertMessage)) {
      controller!.message.value = alertMessage!;
      controller!.androidStatus = TouchAndroidStatus.success;
    }

    if (!Get.isEmptyString(errorMessage)) {
      controller!.message.value = errorMessage!;
      controller!.androidStatus = TouchAndroidStatus.error;
    }

    await delayedBack(isTimeOut);
  }

  static Future<void> delayedBack(bool isTimeOut) async {
    if (isTimeOut == false) {
      await Future.delayed(const Duration(seconds: 2));
    }
    if (controller != null) {
      Get.back();
      FlutterNfcKit.finish();
    }
  }
}
