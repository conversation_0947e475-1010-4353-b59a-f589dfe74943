import 'dart:typed_data';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:enough_convert/enough_convert.dart';
import 'package:flutter/foundation.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/extension/uint8_hex.dart';

enum TouchStatus {
  /// 0：芯片未激活
  inactive,

  /// 1：已激活，未创建钱包
  noWallet,

  /// 2.已激活，已创建钱包
  wallet,
}

class TouchModel {
  /// 状态 0：芯片未激活 1：已激活，未创建钱包 2.已激活，已创建钱包
  String? chipStatus;

  /// 版本号
  String? cosVersion;

  /// 批次号
  String? batch;

  /// 设备ID
  String? chipId;

  /// 发行商
  String? issuer;

  /// 制造方
  String? manufacturer;

  /// 生产日期
  String? dateStr;

  /// 工作模式
  String? workModel;

  /// 等待时间
  String? signWaiting;

  /// 是否可以导出私钥
  String? isExportPriv;

  /// 芯片公钥
  Uint8List? chipPub;

  /// fido公钥
  String? fidoPub;

  /// 是否使用安全密码
  bool? isUseSpass = false;

  /// 安全密码的最大错误次数, 当sPassErrMax=0xff时表示无限次尝试
  String? sPassErrMax;

  /// 安全密码的剩余错误次数
  String? sPassErrRemain;

  /// 是否使用支付密码
  String? isUseMpass;

  /// 支付密码的最大错误次数，当mPassErrMax=0xff时表示无限次尝试
  String? mPassErrMax;

  /// 支付密码的剩余错误次数
  String? mPassErrRemain;

  /// 是否向外界展示过助记词
  String? isShowEntropy;

  /// 是否已经验证助记词
  String? isVerityEntropy;

  /// 钱包是创建的还是恢复的
  String? isCreate;

  /// 种子类型，即表示使用多少个单词的助记词
  int? seedType;

  /// 钱包id
  String? walletID;

  /// 钱包名称
  String? walletName;

  /// 芯片签名累计次数，因为是后期添加数据，所以追加到数据末尾
  String? signTotal;

  /// 种子熵
  Uint8List? entropy;

  List<TouchXpubModel> xpubModelsList = [];

  /// 卡片密码
  String? cardPassword;

  /// 助记词
  List<String> mnemonicsList = [];

  /// 创建钱包名称
  String? newWalletName;

  TouchModel({
    this.chipStatus,
    this.cosVersion,
    this.batch,
    this.chipId,
    this.issuer,
    this.manufacturer,
    this.dateStr,
    this.workModel,
    this.signWaiting,
    this.isExportPriv,
    this.chipPub,
    this.fidoPub,
    this.isUseSpass,
    this.sPassErrMax,
    this.sPassErrRemain,
    this.isUseMpass,
    this.mPassErrMax,
    this.mPassErrRemain,
    this.isShowEntropy,
    this.isVerityEntropy,
    this.isCreate,
    this.seedType,
    this.walletID,
    this.walletName,
    this.signTotal,
  });

  /// 卡片状态
  TouchStatus get status {
    int index = int.parse(chipStatus ?? '0');
    return TouchStatus.values[index];
  }

  Map<String, dynamic> bindJson() {
    List<Map> pubs = [];
    for (var element in xpubModelsList) {
      pubs.add(element.toJson());
    }

    return {
      'batch': int.parse(batch ?? '3000'),
      'device_id': chipId,
      'wallet_id': walletID,
      'wallet_name': walletName,
      'task_id': '000000000000',
      'se_version': 'COS 3.0',
      'vip_level': 0,
      'app_version': cosVersion,
      'is_passphrase': 0,
      'sign': '',
      'pubs': pubs,
    };
  }

  Map<String, dynamic> toJson() => {
        'chipStatus': chipStatus,
        'cosVersion': cosVersion,
        'batch': batch,
        'chipId': chipId,
        'issuer': issuer,
        'manufacturer': manufacturer,
        'dateStr': dateStr,
        'workModel': workModel,
        'signWaiting': signWaiting,
        'isExportPriv': isExportPriv,
        'chipPub': chipPub,
        'fidoPub': fidoPub,
        'isUseSpass': isUseSpass,
        'sPassErrMax': sPassErrMax,
        'sPassErrRemain': sPassErrRemain,
        'isUseMpass': isUseMpass,
        'mPassErrMax': mPassErrMax,
        'mPassErrRemain': mPassErrRemain,
        'isShowEntropy': isShowEntropy,
        'isVerityEntropy': isVerityEntropy,
        'isCreate': isCreate,
        'seedType': seedType,
        'walletID': walletID,
        'walletName': walletName,
        'signTotal': signTotal,
      };

  factory TouchModel.fromPayload(Uint8List payload) {
    int start = 0;

    // 状态
    String chipStatus = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      callback: (s) => start = s,
    );
    chipStatus = int.parse(chipStatus).toString();

    /// COS版本
    String cosVerson = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    /// 批次
    String batch = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // chipId
    String chipId = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // issuer
    String issuer = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // issuer
    String manufacturer = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // 生产日期
    String dateStr = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // 工作模式
    String workModel = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      callback: (s) => start = s,
    );

    // 等待时间
    String signWaiting = _getValueToHex(
      payload: payload,
      start: start,
      length: 4,
      callback: (s) => start = s,
    );

    // 是否可以导出私钥
    String isExportPriv = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 芯片公钥
    Uint8List chipPub = _getValue(
      payload: payload,
      start: start,
      isToString: false,
      callback: (s) => start = s,
    );

    // fido公钥
    String fidoPub = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // 是否使用安全密码
    String isUseSpass = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );
    isUseSpass = int.parse(isUseSpass).toString();

    // 安全密码的最大错误次数, 当sPassErrMax=0xff时表示无限次尝试
    String sPassErrMax = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 安全密码的剩余错误次数
    String sPassErrRemain = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 是否使用支付密码
    String isUseMpass = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 支付密码的最大错误次数，当mPassErrMax=0xff时表示无限次尝试
    String mPassErrMax = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 支付密码的剩余错误次数
    String mPassErrRemain = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 是否向外界展示过助记词
    String isShowEntropy = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 是否已经验证助记词
    String isVerityEntropy = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 钱包是创建的还是恢复的
    String isCreate = _getValue(
      payload: payload,
      start: start,
      isStartlength: false,
      length: 1,
      callback: (s) => start = s,
    );

    // 种子类型，即表示使用多少个单词的助记词
    String seedTypeStr = _getValueToHex(
      payload: payload,
      start: start,
      length: 1,
      callback: (s) => start = s,
    );

    int? seedType = int.tryParse(seedTypeStr);

    // 钱包id
    String walletID = _getValue(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // 钱包名称
    String walletName = _getWalletName(
      payload: payload,
      start: start,
      callback: (s) => start = s,
    );

    // 芯片签名累计次数，因为是后期添加数据，所以追加到数据末尾
    String signTotal = _getValueToHex(
      payload: payload,
      start: start,
      length: 4,
      callback: (s) => start = s,
    );

    return TouchModel(
      chipStatus: chipStatus,
      cosVersion: cosVerson,
      batch: batch,
      chipId: chipId,
      issuer: issuer,
      manufacturer: manufacturer,
      dateStr: dateStr,
      workModel: workModel,
      signWaiting: signWaiting,
      isExportPriv: isExportPriv,
      chipPub: chipPub,
      fidoPub: fidoPub,
      isUseSpass: isUseSpass == '1',
      sPassErrMax: sPassErrMax,
      sPassErrRemain: sPassErrRemain,
      isUseMpass: isUseMpass,
      mPassErrMax: mPassErrMax,
      mPassErrRemain: mPassErrRemain,
      isShowEntropy: isShowEntropy,
      isVerityEntropy: isVerityEntropy,
      isCreate: isCreate,
      seedType: seedType,
      walletID: walletID,
      walletName: walletName,
      signTotal: signTotal,
    );
  }

  static dynamic _getValue({
    required Uint8List payload,
    required int start,
    Function(int s)? callback,
    bool isStartlength = true,
    bool isToString = true,
    int length = 1,
  }) {
    if (start + length > payload.length) return '';
    Uint8List valueList = payload.sublist(start, start + length);
    start += length;

    if (isStartlength == false) {
      if (callback != null) {
        callback(start);
      }
      String value = valueList.toHexRawString;
      return value.toString();
    }

    String lengthValue = valueList.toHexRawString.hexToDecimal;
    if (Get.isEmptyString(lengthValue)) {
      if (callback != null) {
        callback(start);
      }
      return '';
    }

    int valueLength = int.parse(lengthValue);

    valueList = payload.sublist(start, start + valueLength);
    String value = String.fromCharCodes(valueList);
    if (callback != null) {
      callback(start + valueLength);
    }
    if (isToString == false) {
      return valueList;
    }
    return value;
  }

  static dynamic _getValueToHex(
      {required Uint8List payload,
      required int start,
      Function(int s)? callback,
      int length = 1}) {
    if (start + length > payload.length) return '';
    Uint8List valueList = payload.sublist(start, start + length);
    start += length;
    if (callback != null) {
      callback(start);
    }
    return valueList.toHexRawString;
  }

  static dynamic _getWalletName({
    required Uint8List payload,
    required int start,
    Function(int s)? callback,
    int length = 1,
  }) {
    if (start + length > payload.length) return '';
    Uint8List valueList = payload.sublist(start, start + length);
    start += length;

    String lengthValue = valueList.toHexRawString.hexToDecimal;
    if (Get.isEmptyString(lengthValue)) {
      if (callback != null) {
        callback(start);
      }
      return '';
    }

    int valueLength = int.parse(lengthValue);

    valueList = payload.sublist(start, start + valueLength);
    String value = (const GbkCodec(allowInvalid: false)).decode(valueList);
    if (callback != null) {
      callback(start + valueLength);
    }

    return value;
  }
}
