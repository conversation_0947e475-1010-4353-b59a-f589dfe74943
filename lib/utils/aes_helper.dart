/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-10-22 14:20:59
 */
import 'dart:convert';

import 'package:encrypt/encrypt.dart';

class AesHelpler {
  final Key key;
  final IV iv;
  late Encrypter encrypter;

  AesHelpler(String aesMainKey, String aesIVKey,
      {String? keyString, String? iv})
      : key = Key.fromUtf8(keyString ?? aesMainKey),
        iv = IV.fromUtf8(iv ?? aesIVKey) {
    encrypter = Encrypter(AES(key, mode: AESMode.cbc)); // 使用CBC模式
  }

  String encrypt(String plainText) {
    final encryptedData = encrypter.encrypt(plainText, iv: iv);
    return encryptedData.base64;
  }

  String decrypt(String encryptedText) {
    final encryptedData = Encrypted.from64(encryptedText);
    final decryptedData = encrypter.decrypt(encryptedData, iv: iv);
    return decryptedData;
  }

  static String encryptPKCS7(String plainText, {required String mainKey}) {
    final newKey = Key.fromUtf8(mainKey); // 兼容模式的密钥
    final newIv = IV.fromUtf8(mainKey); // 兼容模式的 IV

    final newEncrypter =
        Encrypter(AES(newKey, mode: AESMode.cbc, padding: "PKCS7"));
    final encrypted = newEncrypter.encrypt(plainText, iv: newIv);
    return base64Encode(encrypted.bytes);
  }

  static String decryptPKCS7(String encryptedText, {required String mainKey}) {
    final newKey = Key.fromUtf8(mainKey); // 兼容模式的密钥
    final newIv = IV.fromUtf8(mainKey); // 兼容模式的 IV

    final newEncrypter =
        Encrypter(AES(newKey, mode: AESMode.cbc, padding: "PKCS7"));
    final encryptedData = Encrypted(base64Decode(encryptedText));
    final decrypted = newEncrypter.decrypt(encryptedData, iv: newIv);
    return decrypted;
  }
}
