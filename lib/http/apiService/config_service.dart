/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-08-14 11:20:18
 */
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/dio/dio_provider.dart';
import 'package:coinbag/http/response/config/config_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'config_service.g.dart';

@RestApi()
abstract class ConfigService {
  factory ConfigService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;
    return _ConfigService(dio, baseUrl: API.configBaseUrl);
  }

  // App Config URLls
  @GET("")
  Future<ConfigResponse> getConfigBaseUrl();
}
