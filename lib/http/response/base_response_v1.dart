/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-10-23 11:11:40
 */
import 'package:coinbag/http/response/error_bean.dart';
import 'package:json_annotation/json_annotation.dart';

part 'base_response_v1.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseResponseV1<T> {
  int? id;
  String? jsonrpc;
  ErrorBean? error;
  @JsonKey(name: "result")
  T? data;

  BaseResponseV1({
    this.id,
    this.jsonrpc,
    this.data,
  });

  factory BaseResponseV1.fromJson(
          Map<String, dynamic> json, T Function(Object? json) fromJsonT) =>
      _$BaseResponseV1FromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseV1ToJson(this, toJsonT);
}
