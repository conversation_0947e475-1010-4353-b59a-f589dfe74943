<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2018 xuexiangjys(<EMAIL>)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            tools:srcCompat="@drawable/xupdate_bg_app_top" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/xupdate_bg_app_info"
                android:orientation="vertical"
                android:padding="@dimen/xupdate_content_padding">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/xupdate_title_text_color"
                    android:textSize="@dimen/xupdate_title_text_size"
                    tools:text="是否升级到4.1.1版本？" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/xupdate_common_padding"
                    android:paddingBottom="@dimen/xupdate_common_padding">

                    <TextView
                        android:id="@+id/tv_update_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/xupdate_content_text_color"
                        android:textSize="@dimen/xupdate_content_text_size"
                        tools:text="1、xxxxxxxx\n2、ooooooooo" />

                </LinearLayout>

                <Button
                    android:id="@+id/btn_update"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="@dimen/xupdate_button_min_height"
                    android:text="@string/xupdate_lab_update"
                    android:textColor="@color/xupdate_button_text_color"
                    android:textSize="@dimen/xupdate_button_text_size" />

                <TextView
                    android:id="@+id/tv_ignore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="@dimen/xupdate_button_min_height"
                    android:text="@string/xupdate_lab_ignore"
                    android:textColor="@color/xupdate_content_text_color"
                    android:textSize="@dimen/xupdate_button_text_size"
                    android:visibility="gone" />

                <com.xuexiang.xupdate.widget.NumberProgressBar
                    android:id="@+id/npb_progress"
                    style="@style/XUpdate_ProgressBar_Red"
                    android:paddingTop="@dimen/xupdate_common_padding"
                    android:paddingBottom="@dimen/xupdate_common_padding"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/btn_background_update"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="@dimen/xupdate_button_min_height_mini"
                    android:text="@string/xupdate_lab_background_update"
                    android:textColor="@color/xupdate_button_text_color"
                    android:textSize="@dimen/xupdate_button_text_size_mini"
                    android:visibility="gone" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>


        <LinearLayout
            android:id="@+id/ll_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <View
                android:layout_width="@dimen/xupdate_close_line_width"
                android:layout_height="@dimen/xupdate_close_line_height"
                android:background="@color/xupdate_close_line_color" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/xupdate_close_icon_size"
                android:layout_height="@dimen/xupdate_close_icon_size"
                android:layout_marginTop="-2dp"
                app:srcCompat="@drawable/xupdate_icon_app_close" />

        </LinearLayout>
    </LinearLayout>


</FrameLayout>
